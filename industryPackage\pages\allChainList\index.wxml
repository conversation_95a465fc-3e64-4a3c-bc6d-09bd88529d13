<view class="page-container">
	<!-- 搜索组件 这里不回显    code="{{currentFilter.chain_code}}"-->
	<view class="search-container">
		<search-bar bind:input="handleIpt" code="{{iptCode}}" isDisable="{{true}}" isDisFilter="{{true}}"></search-bar>
	</view>
	<!-- tabs -->
	<view class="custom-tabs">
		<Tab bind:tabChange="handleTabChange" currentCode="{{currentFilter.chain_code}}" />
	</view>
	<!-- 内容区域 -->
	<view class="content-container">
		<RenderList id="renderList" wrapHeight="{{remainingHeight}}px" bind:cardClick="onIndustryClick" type="hot" wx:if="{{currentFilter.chain_code==='hot'}}" fixedCount="{{statsData}}" leftList="{{hotLeftList}}" contentList="{{hotContentList}}"></RenderList>
		<RenderList id="renderList" wrapHeight="{{remainingHeight}}px" bind:cardClick="onIndustryClick" type="classic" wx:if="{{currentFilter.chain_code==='classic'}}" fixedCount="{{statsData}}" leftList="{{classicLeftList}}" contentList="{{classicContentList}}"></RenderList>
		<RenderList id="renderList" wrapHeight="{{remainingHeight}}px" bind:cardClick="onIndustryClick" type="chainMap" wx:if="{{currentFilter.chain_code==='chainMap'}}" fixedCount="{{statsData}}" leftList="{{chainMapLeftList}}" contentList="{{chainMapContentList}}"></RenderList>
	</view>
	<!-- vip弹窗 -->
	<VipPop visible="{{vipVisible}}" bindclose="vipPop"></VipPop>
</view>