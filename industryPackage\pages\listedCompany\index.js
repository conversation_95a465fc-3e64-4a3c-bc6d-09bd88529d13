import {getHeight} from '../../../utils/height.js';
import {formatLargeNumberAry} from '../../../utils/util';
import {
  chainListedEntInfoStatisticApi,
  appChainListedEntTrendApi, // 增长情况
  regionChainListedEntRankApi, // 区域分布
  itypeApi
} from '../../../service/industryApi.js';

const app = getApp();

Page({
  data: {
    dropDownMenuTitle: ['全国', '产业类型'],
    dropDownMenuConfig: ['region', 'industry'],
    isLogin: app.isLogin(),
    computedHeight: 600, // 列表容器高度
    vipVisible: false, // VIP弹窗显示状态
    fixTabsAnimated: false, // fixTabs动画状态
    // 地图相关数据
    horizontalBarData: [], // 横向柱状图数据
    trendData: [], // 趋势图数据
    showPop: false, // 企业列表弹窗显示状态

    // 弹窗组件相关数据
    popupDropDownMenuTitle: ['全国', '更多筛选'], // 弹窗下拉菜单标题（不包含产业类型）
    popupFixedTitle: '', // 弹窗固定标题，显示当前选中的产业类型名字

    // 外部筛选状态 - 用于传递给弹窗内的组件
    externalFilterState: {
      regionData: null, // 地区数据
      industrial_list: {}, // 产业链数据
      filterParams: {
        listing_status: ['A', 'B', 'NTB', 'HK', 'STAR', 'USA']
      } // 其他筛选参数
    },
    fixTabs: [
      {
        value: '0',
        unit: '家',
        originalUnit: '家',
        bgImg:
          'https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/cyt_v2/image/industry/i_ss1',
        label: '上市企业数量',
        key: 'listed_ent_qua'
      },
      {
        value: '0',
        unit: '亿元',
        originalUnit: '亿元',
        bgImg:
          'https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/cyt_v2/image/industry/i_ss2',
        label: '总市值',
        key: 'total_market'
      },
      {
        value: '0',
        unit: '%',
        originalUnit: '%',
        bgImg:
          'https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/cyt_v2/image/industry/i_ss3',
        label: '平均研发投入占比',
        key: 'rd_invest_prop'
      },
      {
        value: '0',
        unit: '家',
        originalUnit: '家',
        bgImg:
          'https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/cyt_v2/image/industry/i_ss4',
        label: '供应链企业',
        key: 'supply_ent_qua'
      },
      {
        value: '0',
        unit: '亿元',
        originalUnit: '亿元',
        bgImg:
          'https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/cyt_v2/image/industry/i_ss5',
        label: '总营业收入',
        key: 'total_revenue'
      },
      {
        value: '0',
        unit: '亿元',
        originalUnit: '亿元',
        bgImg:
          'https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/cyt_v2/image/industry/i_ss6',
        label: '总利润',
        key: 'total_profit'
      },
      {
        value: '0',
        unit: '%',
        originalUnit: '%',
        bgImg:
          'https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/cyt_v2/image/industry/i_ss7',
        label: '平均毛利率',
        key: 'gross_margin'
      },
      {
        value: '0',
        unit: '%',
        originalUnit: '%',
        bgImg:
          'https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/cyt_v2/image/industry/i_ss8',
        label: '平均市盈率',
        key: 'asset_liabi_ratio'
      }
    ],
    isIndustryMap: true, // 这个页面只有产业图谱
    treeData: [] //产业链图谱数据
  },

  onLoad: function (options) {
    let {
      chain_name,
      chain_code = '',
      filter_code = '',
      filter_name = '',
      region_name = '',
      region_code = ''
    } = options;
    chain_name = chain_name ? decodeURIComponent(chain_name) : '';
    filter_name = filter_name ? decodeURIComponent(filter_name) : '产业类型';
    region_name = region_name ? decodeURIComponent(region_name) : '全国';
    const setObj = {
      chain_code,
      filter_code, // 产业类型发请求用
      externalFilterState: {
        industrial_list: {
          // 这是下拉弹窗的孩子
          code: chain_code || filter_code,
          name: chain_code ? chain_name : filter_name
        },
        filterParams: {
          listing_status: ['A', 'B', 'NTB', 'HK', 'STAR', 'USA']
        }
      },
      popupFixedTitle: chain_name,
      chain_name
    };
    if (region_code) {
      setObj['externalFilterState']['regionData'] = {
        code: region_code,
        name: region_name
      };
      this.selectComponent('#drop-menu')?.setHighlightByMenuType(
        'region',
        true
      );
    }
    setObj['dropDownMenuTitle'] = [
      region_name,
      chain_code ? chain_name : filter_name
    ];
    console.log(333, setObj);

    setObj['isIndustryMap'] = true;
    this.selectComponent('#drop-menu')?.setHighlightByMenuType(
      'industry',
      true
    );
    this.setData(
      {
        ...setObj
      },
      () => {
        this.getInitData();
        this.getTupuData(); // 图谱不受下拉框影响 就只调取一次
      }
    );

    // 触发fixTabs动画
    this.triggerFixTabsAnimation();
  },

  onShow() {
    this.handleHeight();
    // 触发fixTabs动画
    this.triggerFixTabsAnimation();
  },

  // 动态获取蒙层高度
  handleHeight() {
    const that = this;
    getHeight(that, '.page_head', data => {
      this.setData({
        computedHeight: data.screeHeight - data.res[0].height,
        isLogin: app.isLogin()
      });
    });
  },

  // 触发fixTabs动画
  triggerFixTabsAnimation() {
    // 确保页面渲染完成后再触发动画
    wx.nextTick(() => {
      setTimeout(() => {
        this.setData({
          fixTabsAnimated: true
        });
      }, 300); // 稍微延长延迟时间，确保页面完全加载
    });
  },

  // 顶部筛选
  onFlitter(e) {
    let {
      industrial_list = '',
      area_code_list = '',
      regionData,
      name1,
      name2,
      isFilter,
      ...restParams
    } = e.detail;

    let {dropDownMenuTitle, externalFilterState: oldVal} = this.data;
    // 更新下拉菜单标题
    dropDownMenuTitle = [name1 || '全国', name2 || oldVal.industrial_list.name];

    // 处理regionData  如果是全国的时候
    let tempRegionData = {};
    if (regionData?.code === 'All' || !regionData?.code) {
      tempRegionData = {
        code: '1000000',
        name: '全国'
      };
    } else {
      tempRegionData = regionData;
    }

    // 更新地图区域数据
    const mapRegion = this.convertToMapRegion(tempRegionData);

    // 更新外部筛选状态，用于传递给弹窗
    const externalFilterState = {
      regionData, //ps: 这个全国All和1000000不一样
      industrial_list: {
        code: industrial_list || oldVal.industrial_list.code, //如果一开始没选默认把最原始的带进去
        name: name2 || oldVal.industrial_list.name
      }, // 保存完整的对象信息
      filterParams: {
        ...restParams,
        listing_status: ['A', 'B', 'NTB', 'HK', 'STAR', 'USA']
      }
    };

    // 更新弹窗固定标题为当前选中的产业类型名字
    const popupFixedTitle = name2;

    this.setData(
      {
        dropDownMenuTitle: dropDownMenuTitle,
        mapRegion: mapRegion,
        externalFilterState: externalFilterState,
        popupFixedTitle: popupFixedTitle
      },
      () => {
        // 刷新数据
        this.getInitData();
      }
    );
  },

  // VIP弹窗
  vipPop(val) {
    if (val.type === 'close') {
      this.setData({
        vipVisible: false
      });
      return;
    }
    this.setData({
      vipVisible: val
    });
  },

  // 跳转到列表模式
  goListMode() {
    app.route(this, '/industryPackage/pages/businessList/index');
  },

  // 企业列表点击事件
  onEnterpriseItemClick(e) {
    console.log('企业类型点击:', e.detail);
    // 暂时不处理，等待其他需求
  },

  // 浮窗按钮点击 - 显示企业列表弹窗
  onShowEnterpriseList() {
    this.setData({
      showPop: true
    });
  },

  // 关闭企业列表弹窗
  onClose() {
    this.setData({
      showPop: false
    });
  },

  // 转换regionData为Map组件需要的格式
  convertToMapRegion(regionData) {
    let {level, name, code, parent} = regionData;
    if (code === 'All') {
      level = '1';
      name = 'china';
      code = '1000000';
    }
    return {
      code: code,
      name: name,
      level: level,
      parent: parent || ''
    };
  },
  // 获取地图数据
  async getInitData() {
    try {
      // 并行获取所有需要的数据
      await Promise.all([
        this.getStatisticData(),
        this.getTrendData(),
        this.getRegionRankData()
      ]);
    } catch (error) {
      console.error('获取数据失败:', error);
    }
  },

  // 获取统计数据 (fixTabs数据)
  async getStatisticData() {
    const {externalFilterState, fixTabs} = this.data;

    try {
      const code = externalFilterState?.regionData?.code;
      const level = externalFilterState?.regionData?.level;
      const {code: chain_code} = externalFilterState?.industrial_list;
      const municipalities = ['110000', '120000', '310000', '500000'];
      const isMunicipality = municipalities.includes(code);

      const params = {
        chain_code: chain_code,
        region_code: code == '1000000' || code == 'All' ? '' : code,
        chain_type: 1,
        region_type: isMunicipality ? 2 : level ? +level : 0 // 0: 全国 1: 省 2: 市 3: 区
      };

      const response = await chainListedEntInfoStatisticApi(params);

      if (response) {
        const updatedFixTabs = fixTabs.map(item => {
          return {
            ...item,
            value: formatLargeNumberAry(response[item.key] || 0, 2)[0],
            unit:
              item.originalUnit == '家' || item.originalUnit.includes('元')
                ? formatLargeNumberAry(response[item.key] || 0, 2)[1] +
                  item.originalUnit
                : item.originalUnit
          };
        });

        this.setData({
          fixTabs: updatedFixTabs
        });
      }
    } catch (error) {
      console.error('获取统计数据失败:', error);
    }
  },

  // 获取趋势数据 (增长情况图表)
  async getTrendData() {
    const {externalFilterState} = this.data;

    try {
      // const regionConfig = this._getRegionConfig(
      //   externalFilterState?.regionData
      // );
      const code = externalFilterState?.regionData?.code;
      const level = externalFilterState?.regionData?.level;
      const {code: chain_code} = externalFilterState?.industrial_list;
      const municipalities = ['110000', '120000', '310000', '500000'];
      const isMunicipality = municipalities.includes(code);
      const region_type = isMunicipality ? 2 : level ? +level : 0;
      const params = {
        chain_code: chain_code,
        region_code: code == '1000000' || code == 'All' ? '' : code,
        chain_type: 1,
        region_type: region_type // 0: 全国 1: 省 2: 市 3: 区
      };

      const response = await appChainListedEntTrendApi(params);

      if (response && response.length > 0) {
        // 这里可以处理趋势数据，传递给echarts-bar组件
        const trendData = response
          .map(item => ({
            year: item.date,
            value: item.value
          }))
          .sort((a, b) => Number(a.year) - Number(b.year));
        this.setData({
          trendData: trendData
        });
      } else {
        this.setData({
          trendData: []
        });
      }
    } catch (error) {
      console.error('获取趋势数据失败:', error);
      // 出错时也要设置空数组，确保组件能显示空状态
      this.setData({
        trendData: []
      });
    }
  },

  // 获取区域排行数据
  async getRegionRankData() {
    const {externalFilterState} = this.data;

    try {
      const code = externalFilterState?.regionData?.code;
      const level = externalFilterState?.regionData?.level;
      const {code: chain_code} = externalFilterState?.industrial_list;
      const municipalities = ['110000', '120000', '310000', '500000'];
      const params = {
        chain_code: chain_code,
        region_code: code == '1000000' || code == 'All' ? '' : code,
        chain_type: 1,
        top: 10 // 只取前8名
      };
      console.log(code, level);

      // 1: 全国 2: 省 3: 市 4: 区
      if (municipalities.includes(code)) {
        params.rank_type = 3; // 直辖市
      } else if (level === '1') {
        params.rank_type = 2;
      } else if (level === '2') {
        params.rank_type = 3;
      } else if (code === 'all') {
        params.rank_type = 1;
      } else if (level === '3') {
        params.rank_type = 4;
      }

      const response = await regionChainListedEntRankApi(params);

      if (response && response.length > 0) {
        // 转换数据格式为横向柱状图需要的格式
        const horizontalBarData = response.map(item => ({
          name: item.region_name,
          value: item.total || 0
        }));

        this.setData({
          horizontalBarData: horizontalBarData
        });
      } else {
        // 区没有排行
        this.setData({
          horizontalBarData: []
        });
      }
    } catch (error) {
      this.setData({
        horizontalBarData: []
      });
    }
  },
  // 获取产业图谱数据
  async getTupuData() {
    const res = await itypeApi({
      chain_code: this.data.filter_code
    });

    this.setData({
      treeData: this._formatTreeData(res)
    });
  },
  /**
   * 格式化树形数据
   */
  _formatTreeData(data) {
    if (!data || !data.name) {
      return [];
    }
    return [
      {
        name: data.name,
        children:
          data.childs?.map(firstLevel => ({
            name: firstLevel.name,
            children:
              firstLevel.childs?.map(secondLevel => ({
                name: secondLevel.name
              })) || []
          })) || []
      }
    ];
  },

  // 处理弹窗内部滑动，阻止冒泡
  onPopupTouchMove() {
    // 阻止事件冒泡，允许弹窗内部滚动而不影响外部
    return false;
  },

  onTuClick() {
    const {filter_code, filter_name} = this.data;
    app.route(
      this,
      `/industryPackage/pages/IndustryMapMasonry/index?chain_code=${filter_code}&chain_name=${filter_name}&purchased=${true}&type=chainMap`
    );
  },

  // fixTabs 点击事件
  onFixTabClick(e) {
    // const index = e.currentTarget.dataset.index;
    // const item = this.data.fixTabs[index];
    // const {
    // 	chain_code,
    // 	chain_name
    // } = this.data;
    // if (item.label !== '上市企业数量') return;
    // // 跳转到列表页面
    // app.route(
    // 	this,
    // 	`/industryPackage/pages/businessList/index?isIndustryMap=true&chain_name=${decodeURIComponent(
    //     chain_name
    //   )}&title=${encodeURIComponent('上市企业')}&chain_code=${chain_code}`
    // );
    this.setData({
      showPop: true
    });
  }
});
