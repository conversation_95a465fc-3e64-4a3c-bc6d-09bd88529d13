// industryPackage/components/Table/index.js
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 表格数据
    data: {
      type: Array,
      value: []
    },
    // 表格列配置
    columns: {
      type: Array,
      value: []
    },
    // 是否合并第一列
    mergeFirstColumn: {
      type: Boolean,
      value: false
    },
    // 第一列合并的依据字段，用于判断哪些行需要合并
    mergeBy: {
      type: String,
      value: ''
    },
    // 是否正在加载数据
    loading: {
      type: Boolean,
      value: false
    }
  },

  /**
   * 组件的初始数据
   */
  data: {},

  /**
   * 组件的计算属性
   */
  observers: {
    'data, loading': function (data, loading) {
      // 计算是否为空状态（只有在非加载状态且数据为空时才显示空状态）
      const isEmpty = !loading && (!data || data.length === 0);
      this.setData({
        isEmpty: isEmpty
      });
    },
    'data, columns, mergeFirstColumn, mergeBy': function () {
      this.processTableData();
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    // 预处理表格数据
    processTableData() {
      const {data, columns, mergeFirstColumn, mergeBy} = this.data;

      if (!data || !columns || columns.length === 0) {
        this.setData({
          showList: [],
          firstColumn: null,
          firstColumnData: [],
          otherColumns: [],
          otherColumnsData: []
        });
        return;
      }

      // 显示所有数据
      const showList = data;

      // 分离第一列和其他列
      const firstColumn = columns[0];
      const otherColumns = columns.slice(1).map((column, index) => ({
        ...column,
        style: this.getColumnStyle(column, index + 1)
      }));

      // 处理第一列数据
      const firstColumnData = showList.map((row, rowIndex) => {
        const content = this.getCellContent(row, firstColumn, rowIndex);
        let style = this.getColumnStyle(firstColumn, 0);
        let merged = false;
        let isMergedMain = false;

        // 处理第一列合并逻辑
        if (mergeFirstColumn && mergeBy) {
          if (rowIndex > 0 && this.shouldMergeCell(rowIndex, showList)) {
            merged = true;
            style += 'display: none;';
          } else {
            // 计算合并样式
            const mergeInfo = this.getMergeInfo(rowIndex, showList);
            if (mergeInfo.span > 1) {
              isMergedMain = true;
              const cellHeight = 74; // 单个单元格高度（rpx）
              const totalHeight = cellHeight * mergeInfo.span;
              style += `height: ${totalHeight}rpx;`;
            }
          }
        }

        return {
          content: content,
          style: style,
          merged: merged,
          isMergedMain: isMergedMain
        };
      });

      // 处理其他列数据
      const otherColumnsData = showList.map((row, rowIndex) => {
        const cells = otherColumns.map((column, colIndex) => ({
          content: this.getCellContent(row, column, rowIndex),
          style: this.getColumnStyle(column, colIndex + 1)
        }));

        return {
          cells: cells
        };
      });

      this.setData({
        showList: showList,
        firstColumn: firstColumn,
        firstColumnData: firstColumnData,
        otherColumns: otherColumns,
        otherColumnsData: otherColumnsData
      });
    },

    // 计算每列的最大内容宽度
    calculateColumnWidths() {
      const {columns, data} = this.data;
      if (!columns || !data || columns.length <= 1) return {};

      const columnWidths = {};
      const otherColumns = columns.slice(1); // 除第一列外的其他列

      // 计算每列内容的最大宽度
      otherColumns.forEach((column, index) => {
        let maxWidth = 143; // 最小宽度（rpx）

        // 检查表头宽度
        const headerWidth = this.estimateTextWidth(column.title, 26); // 表头字体26rpx
        maxWidth = Math.max(maxWidth, headerWidth);

        // 检查数据内容宽度
        data.forEach(row => {
          const content = this.getCellContent(row, column, 0);
          const contentWidth = this.estimateTextWidth(content, 24); // 内容字体24rpx
          maxWidth = Math.max(maxWidth, contentWidth);
        });

        columnWidths[index] = maxWidth + 40; // 加上左右padding 40rpx
      });

      // 找到最大宽度
      const maxColumnWidth = Math.max(...Object.values(columnWidths));

      // 所有列使用相同的最大宽度，但要考虑flex:1的情况
      const uniformWidth = Math.max(maxColumnWidth, 183); // 最小183rpx

      return {
        uniformWidth,
        shouldUseFlex: otherColumns.length <= 3 // 列数少时使用flex:1
      };
    },

    // 估算文字宽度（简单估算）
    estimateTextWidth(text, fontSize) {
      if (!text) return 0;
      const str = String(text);
      // 中文字符按fontSize计算，英文数字按fontSize*0.6计算
      let width = 0;
      for (let i = 0; i < str.length; i++) {
        const char = str.charAt(i);
        if (/[\u4e00-\u9fa5]/.test(char)) {
          // 中文字符
          width += fontSize;
        } else {
          // 英文数字等
          width += fontSize * 0.6;
        }
      }
      return width;
    },

    // 获取列样式
    getColumnStyle(column, index) {
      if (!column) return '';

      let style = '';

      // 第一列固定宽度
      if (index === 0) {
        style += 'width: 272rpx; min-width: 272rpx; max-width: 272rpx;';
      } else {
        // 其他列使用统一宽度
        const {uniformWidth, shouldUseFlex} = this.calculateColumnWidths();

        if (shouldUseFlex) {
          // 列数少时使用flex:1撑满
          style += `flex: 1; min-width: ${uniformWidth}rpx; white-space: nowrap; padding: 0 20rpx !important;`;
        } else {
          // 列数多时使用固定宽度
          style += `flex: none; width: ${uniformWidth}rpx; white-space: nowrap; padding: 0 20rpx !important;`;
        }
      }

      return style;
    },

    // 获取单元格内容
    getCellContent(row, column, rowIndex) {
      if (!row || !column) return '';

      const text = row[column.dataIndex];

      // 如果有自定义渲染函数，则使用自定义渲染
      if (column.customRender && typeof column.customRender === 'function') {
        return column.customRender({
          text: text,
          index: rowIndex
        });
      }

      return text || '';
    },

    // 判断当前行是否应该被合并（隐藏）
    shouldMergeCell(rowIndex, showList) {
      if (!this.data.mergeFirstColumn || !this.data.mergeBy || rowIndex === 0) {
        return false;
      }

      if (!showList || rowIndex >= showList.length) return false;

      const currentValue = showList[rowIndex][this.data.mergeBy];
      const prevValue = showList[rowIndex - 1][this.data.mergeBy];

      return currentValue === prevValue;
    },

    // 获取合并信息
    getMergeInfo(rowIndex, showList) {
      if (!this.data.mergeFirstColumn || !this.data.mergeBy) {
        return {span: 1};
      }

      // 如果不是第一行且应该被合并，则返回 span 0
      if (rowIndex > 0 && this.shouldMergeCell(rowIndex, showList)) {
        return {span: 0};
      }

      // 计算当前行后面有多少行需要被合并
      if (!showList || showList.length === 0) return {span: 1};

      const currentValue = showList[rowIndex][this.data.mergeBy];
      let span = 1;

      for (let i = rowIndex + 1; i < showList.length; i++) {
        if (showList[i][this.data.mergeBy] === currentValue) {
          span++;
        } else {
          break;
        }
      }

      return {span: span};
    },

    // 获取合并样式（计算 rowspan 效果）- 保持向后兼容
    getMergeStyle(rowIndex, showList) {
      const mergeInfo = this.getMergeInfo(rowIndex, showList);

      if (mergeInfo.span === 0) {
        return 'display: none;';
      }

      if (mergeInfo.span > 1) {
        const cellHeight = 74; // 单个单元格高度（rpx）
        const totalHeight = cellHeight * mergeInfo.span;
        return `height: ${totalHeight}rpx;`;
      }

      return '';
    }
  }
});
