/* companyPackage/pages/cardDetail/components/layout.scss */

.layout_box {
  background-color: #fff;
  margin-bottom: 20rpx;
}

.layout_box .title {
  position: relative;
  height: 84rpx;
  /* text-indent: 40rpx; */
  font-weight: 600;
  color: #20263a;
  font-size: 32rpx;
  display: flex;
  padding: 0 24rpx;
  align-items: center;
  justify-content: space-between;
  /* border-bottom: 1px solid #eee; */
}
.layout_box .title::after {
  content: "";
  width: 100%;
  height: 2rpx;
  background: #eee;
  position: absolute;
  left: 0;
  bottom: 0;
  transform: scaleY(0.5);
}
/* .layout_box .title::before{
  content: '';
  width: 4rpx;
  height: 28rpx;
  position: absolute;
  left: 24rpx;
  top:28rpx;
  background-color: #E72410;
} */
.layout_box .title .btn {
  display: flex;
  align-items: center;
  font-size: 24rpx;
  color: #E72410;
  font-weight: 400;
  text-indent: 0;
}
.layout_box .title .btn .btn-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 12rpx;
}