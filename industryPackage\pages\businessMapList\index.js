import {getHeight} from '../../../utils/height.js';
import {
  regionChainEntRankApi,
  appChainRegionEntDataAnalysisApi,
  singleChainKeyEntNumApi
} from '../../../service/industryApi.js';
import {chain} from '../../../service/api';

const app = getApp();

Page({
  data: {
    chain_code: '', // 产业链代码
    dropDownMenuTitle: ['全国', '产业类型'],
    dropDownMenuConfig: ['region', 'industry'],
    isLogin: app.isLogin(),
    computedHeight: 600, // 列表容器高度
    vipVisible: false, // VIP弹窗显示状态
    // 地图相关数据
    mapRegion: {
      code: '1000000',
      name: 'china',
      level: '1'
    },
    heatMapData: [], // 热力图数据
    totalCompanyCount: 0, // 企业总量
    markPoints: [], // Level 3时的标记点数据
    provinceBarData: [], // 横向柱状图数据--省
    cityBarData: [], // 横向柱状图数据--省
    showPop: false, // 企业列表弹窗显示状态
    enterpriseListData: [], // 企业列表数据
    company_num: 0, // 企业总数

    // 弹窗组件相关数据
    popupDropDownMenuTitle: ['全国', '更多筛选'], // 弹窗下拉菜单标题（不包含产业类型）
    popupFixedTitle: '', // 弹窗固定标题，显示当前选中的产业类型名字

    // 外部筛选状态 - 用于传递给弹窗内的组件
    externalFilterState: {},
    industryData: [
      {
        name: '上市企业',
        img: 'https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/cyt_v2/image/industry/i_ent1.png',
        count: 0,
        key: 'listed_ent_cnt'
      },
      {
        name: '小巨人',
        img: 'https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/cyt_v2/image/industry/i_ent2.png',
        count: 0,
        key: 'little_giant'
      },
      {
        name: '单项冠军',
        img: 'https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/cyt_v2/image/industry/i_ent3.png',
        count: 0,
        key: 'single_champ'
      },
      {
        name: '高新技术企业',
        img: 'https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/cyt_v2/image/industry/i_ent4.png',
        count: 0,
        key: 'high_tech_ent_cnt'
      },
      {
        name: '专精特新中小企业',
        img: 'https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/cyt_v2/image/industry/i_ent5.png',
        count: 0,
        key: 'specialized_ent_cnt'
      },
      {
        name: '创新型中小企业',
        img: 'https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/cyt_v2/image/industry/i_ent6.png',
        count: 0
      },
      {
        name: '科技型企业',
        img: 'https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/cyt_v2/image/industry/i_ent7.png',
        count: 0,
        key: 'technology_ent_cnt'
      }
    ],
    useIndustrySingleSelect: false
  },

  onLoad: function (options) {
    let {chain_name, chain_code, isIndustryMap, category} = options;

    chain_name = chain_name ? decodeURIComponent(chain_name) : '产业类型';
    let setObj = {
      chain_code, // 产业类型发请求用
      category: isIndustryMap === 'true' ? 'chainMap' : category, //区分经典 热点 产业链
      useIndustrySingleSelect: isIndustryMap === 'true',
      popupFixedTitle: chain_name,
      externalFilterState: {},
      // 原始类型 --重点企业跳转用
      originalCategory: isIndustryMap === 'true' ? 'chainMap' : category,
      originalChainName: chain_name
    };
    if (category === 'classic') {
      setObj['externalFilterState']['classic_industry_code_list'] = {
        code: chain_code,
        name: chain_name
      };
    } else {
      setObj['externalFilterState']['industrial_list'] = {
        code: chain_code,
        name: chain_name
      };
    }

    setObj['dropDownMenuTitle'] = ['全国', chain_name];
    if (isIndustryMap) {
      setObj['isIndustryMap'] = true;
    } // 初始化地图数据
    this.setData(
      {
        ...setObj
      },
      () => {
        this.getMapData();
      }
    );
  },

  onShow() {
    this.handleHeight();
  },

  // 动态获取蒙层高度
  handleHeight() {
    const that = this;
    getHeight(that, '.page_head', data => {
      this.setData({
        computedHeight: data.screeHeight - data.res[0].height,
        isLogin: app.isLogin()
      });
    });
  },

  // 顶部筛选
  onFlitter(e) {
    // console.log('筛选条件:', e.detail);
    let {
      industrial_list = '',
      area_code_list = '',
      regionData,
      name1,
      name2,
      isFilter,
      chainType: category,
      ...restParams
    } = e.detail;

    let {dropDownMenuTitle, externalFilterState: oldVal} = this.data;
    category = category ? category : this.data.category;
    const oldName =
      category === 'classic'
        ? oldVal.classic_industry_code_list?.name
        : oldVal.industrial_list?.name;
    const oldCode =
      category === 'classic'
        ? oldVal.classic_industry_code_list?.code
        : oldVal.industrial_list?.code;

    dropDownMenuTitle = [name1 || '全国', name2 || oldName];

    // 处理regionData  如果是全国的时候
    let tempRegionData = {};
    if (regionData?.code === 'All' || !regionData?.code) {
      tempRegionData = {
        code: '1000000',
        name: '全国'
      };
    } else {
      tempRegionData = regionData;
    }

    // 更新地图区域数据
    const mapRegion = this.convertToMapRegion(tempRegionData);

    // 更新外部筛选状态，用于传递给弹窗
    const externalFilterState = {
      regionData, //ps: 这个全国All和1000000不一样
      filterParams: {
        ...restParams
      }
    };
    if (category === 'classic') {
      externalFilterState['classic_industry_code_list'] = {
        code: industrial_list || oldCode, //如果一开始没选默认把最原始的带进去
        name: name2 || oldName
      };
    } else {
      externalFilterState['industrial_list'] = {
        code: industrial_list || oldCode, //如果一开始没选默认把最原始的带进去
        name: name2 || oldName
      };
    }

    // 更新弹窗固定标题为当前选中的产业类型名字
    const popupFixedTitle = name2;
    this.setData(
      {
        dropDownMenuTitle: dropDownMenuTitle,
        mapRegion: mapRegion,
        externalFilterState: externalFilterState,
        popupFixedTitle: popupFixedTitle,
        category // 下拉框选过后 当前类型也需要发生变化
      },
      () => {
        // 刷新数据
        this.getMapData();
      }
    );
  },

  // VIP弹窗
  vipPop(val) {
    if (val.type === 'close') {
      this.setData({
        vipVisible: false
      });
      return;
    }
    this.setData({
      vipVisible: val
    });
  },

  // 跳转到列表模式
  goListMode() {
    app.route(this, '/industryPackage/pages/businessList/index');
  },

  // 企业列表点击事件
  onEnterpriseItemClick(e) {
    const {
      item: {name}
    } = e.detail;
    const {
      chain_code,
      originalCategory, // 原始分类
      originalChainName,
      category,
      mapRegion,
      externalFilterState: {classic_industry_code_list, industrial_list}
    } = this.data;

    if (name === '上市企业' && category === 'chainMap') {
      wx.navigateTo({
        url: `/industryPackage/pages/listedCompany/index?&chain_code=${
          industrial_list.code
        }&chain_name=${decodeURIComponent(
          industrial_list.name
        )}&filter_code=${chain_code}&filter_name=${decodeURIComponent(
          originalChainName
        )}&region_code=${mapRegion.code}&region_name=${encodeURIComponent(
          mapRegion.name
        )}`
      });
      return;
    }

    // 这里全国没有入口 所以不用考虑特殊情况
    let url = `/industryPackage/pages/businessList/index?title=${encodeURIComponent(
      name
    )}&showMapMode=false&region_code=${
      mapRegion.code
    }&region_name=${encodeURIComponent(mapRegion.name)}`;
    if (originalCategory === 'chainMap') {
      url += `&isIndustryMap=true&category=chainMap&chain_code=${
        industrial_list.code
      }&chain_name=${decodeURIComponent(
        industrial_list.name
      )}&filter_code=${chain_code}&filter_name=${decodeURIComponent(
        originalChainName
      )}`;
    } else {
      if (category === 'classic') {
        url =
          url +
          `&chain_code=${
            classic_industry_code_list.code
          }&chain_name=${decodeURIComponent(
            classic_industry_code_list.name
          )}&category=${category}`;
      } else if (category === 'hot') {
        url =
          url +
          `&chain_code=${industrial_list.code}&chain_name=${decodeURIComponent(
            industrial_list.name
          )}&category=${category}`;
      } else if (category === 'chainMap') {
        // 不是从产业链图片进来的弹窗还是之前那个
        url =
          url +
          `&chain_code=${industrial_list.code}&chain_name=${decodeURIComponent(
            industrial_list.name
          )}&filter_code=${chain_code}&filter_name=${decodeURIComponent(
            originalChainName
          )}`;
      }
    }
    // 可以根据不同的企业类型跳转到不同页面
    wx.navigateTo({
      url
    });
  },

  // 浮窗按钮点击 - 显示企业列表弹窗
  onShowEnterpriseList() {
    this.setData({
      showPop: true
    });
  },

  // 关闭企业列表弹窗
  onClose() {
    this.setData({
      showPop: false
    });
  },

  // 转换regionData为Map组件需要的格式
  convertToMapRegion(regionData) {
    let {level, name, code, parent} = regionData;
    if (code === 'All') {
      level = '1';
      name = 'china';
      code = '1000000';
    }
    return {
      code: code,
      name: name,
      level: level,
      parent: parent || ''
    };
  },

  // 获取地图数据---省份排行
  async getMapData() {
    const {
      mapRegion,
      externalFilterState: {industrial_list, classic_industry_code_list},
      category,
      industryData
    } = this.data;

    let chain_code = '';
    if (category === 'classic') {
      chain_code = classic_industry_code_list?.code;
    } else {
      chain_code = industrial_list?.code;
    }
    const chain_type = this._getChainType(category);

    try {
      const regionConfig = this._getRegionConfig(mapRegion);
      const apiResults = await this._fetchMapDataApis(
        chain_code,
        chain_type,
        regionConfig
      );
      const processedData = this._processMapData(
        apiResults,
        regionConfig,
        industryData
      );

      this._updateMapState(processedData, mapRegion);
    } catch (error) {
      console.error('获取地图数据失败:', error);
      this._setDefaultMapData();
    }
  },

  /**
   * 获取产业链类型
   * @param {string} category - 产业类型
   * @returns {number} 产业链类型编码
   */
  _getChainType(category) {
    const typeMap = {
      classic: 3, // 经典产业
      chainMap: 1, // 产业链图谱
      hot: 2 // 热点产业
    };
    return typeMap[category] || 1;
  },

  /**
   * 获取区域配置信息
   * @param {Object} mapRegion - 地图区域信息
   * @returns {Object} 区域配置
   */
  _getRegionConfig(mapRegion) {
    const municipalities = ['110000', '120000', '310000', '500000'];
    const isMunicipality = municipalities.includes(mapRegion.code);

    // 确定区域类型和层级
    let regionType,
      rankType,
      needIndustryData = false;

    if (mapRegion.code === '1000000') {
      // 全国
      regionType = null;
      rankType = {
        city: 2,
        province: 1
      };
    } else if (mapRegion.level === '1' && !isMunicipality) {
      // 省份
      regionType = 2; // 省->市
      rankType = {
        city: 2
      };
      needIndustryData = true;
    } else if (mapRegion.level === '2' || isMunicipality) {
      // 市级或直辖市
      regionType = 3; // 市->区
      rankType = {
        city: 3
      };
      needIndustryData = true;
    } else if (mapRegion.level === '3') {
      // 区县级
      regionType = null;
      rankType = null;
      needIndustryData = true;
    }

    return {
      regionType,
      rankType,
      needIndustryData,
      isMunicipality,
      regionCode: mapRegion.code,
      level: mapRegion.level
    };
  },

  /**
   * 并行获取所有需要的API数据
   * @param {string} chain_code - 产业链代码
   * @param {number} chain_type - 产业链类型
   * @param {Object} regionConfig - 区域配置
   * @returns {Object} API结果
   */
  async _fetchMapDataApis(chain_code, chain_type, regionConfig) {
    const {regionType, rankType, needIndustryData, regionCode, level} =
      regionConfig;
    const apiCalls = [];
    const apiKeys = [];

    // 地图数据API（除了区县级都需要）
    if (level !== '3') {
      const mapDataParams = {
        chain_code,
        chain_type
      };
      if (regionType) {
        mapDataParams.region_type = regionType; //如果是直辖市就传2
        mapDataParams.region_code = [regionCode];
      }
      apiCalls.push(appChainRegionEntDataAnalysisApi(mapDataParams));
      apiKeys.push('mapData');
    }

    // 排行数据API
    if (rankType) {
      if (rankType.city) {
        const cityParams = {
          chain_code,
          rank_type: rankType.city,
          chain_type,
          top: 10
        };
        if (regionCode !== '1000000') {
          cityParams.region_code = regionCode;
        }
        apiCalls.push(regionChainEntRankApi(cityParams));
        apiKeys.push('cityData');
      }

      if (rankType.province) {
        apiCalls.push(
          regionChainEntRankApi({
            chain_code,
            rank_type: rankType.province,
            chain_type,
            top: 10
          })
        );
        apiKeys.push('provinceData');
      }
    }

    // 产业数据API
    if (needIndustryData) {
      const industryRegionType = level === '1' ? 1 : level === '2' ? 2 : 3;
      apiCalls.push(
        singleChainKeyEntNumApi({
          chain_code,
          chain_type,
          region_code: regionCode,
          region_type: industryRegionType
          // region_type: ['110000', '120000', '310000', '500000'].includes(
          //   regionCode
          // )
          //   ? 2
          //   : industryRegionType // 直辖市传2 后续再看放不放开
        })
      );
      apiKeys.push('industryKeyData');
    }

    // 并行执行所有API调用
    const results = await Promise.allSettled(apiCalls);

    // 组装结果对象
    const apiResults = {};
    results.forEach((result, index) => {
      const key = apiKeys[index];
      if (result.status === 'fulfilled') {
        apiResults[key] = result.value;
      } else {
        console.warn(`API调用失败 - ${key}:`, result.reason);
        apiResults[key] = null;
      }
    });

    return apiResults;
  },

  /**
   * 处理API返回的数据
   * @param {Object} apiResults - API结果
   * @param {Object} regionConfig - 区域配置
   * @param {Array} industryData - 产业数据
   * @returns {Object} 处理后的数据
   */
  _processMapData(apiResults, regionConfig, industryData) {
    const {needIndustryData} = regionConfig;

    // 处理地图数据
    let mapData = [];
    if (apiResults.mapData?.data_list) {
      mapData = this._transformMapData(apiResults.mapData.data_list);
    }

    // 处理城市排行数据
    let cityData = [];
    if (apiResults.cityData) {
      cityData = this._transformRankData(apiResults.cityData);
    }

    // 处理省份排行数据
    let provinceBarData = [];
    if (apiResults.provinceData) {
      provinceBarData = this._transformRankData(apiResults.provinceData);
    }
    // 处理产业数据
    let updatedIndustryData = industryData;
    if (needIndustryData && apiResults.industryKeyData) {
      updatedIndustryData = this._updateIndustryData(
        industryData,
        apiResults.industryKeyData
      );
    }

    // 计算企业总量
    const totalCount = mapData.reduce(
      (sum, item) => sum + (item.value || 0),
      0
    );

    return {
      mapData,
      cityData,
      provinceBarData,
      industryData: updatedIndustryData,
      totalCount
    };
  },

  /**
   * 转换地图数据格式
   * @param {Array} dataList - 原始数据列表
   * @returns {Array} 转换后的数据
   */
  _transformMapData(dataList) {
    return dataList
      .map(item => ({
        name: item.region_name,
        value: item.count
      }))
      .sort((a, b) => a.value - b.value);
  },

  /**
   * 转换排行数据格式
   * @param {Array} rankData - 原始排行数据
   * @returns {Array} 转换后的数据
   */
  _transformRankData(rankData) {
    return rankData
      .map(item => ({
        name: item.region_name,
        value: item.total
      }))
      .sort((a, b) => a.value - b.value);
  },

  /**
   * 更新产业数据
   * @param {Array} industryData - 原始产业数据
   * @param {Object} keyEntNumData - 重点企业数量数据
   * @returns {Array} 更新后的产业数据
   */
  _updateIndustryData(industryData, keyEntNumData) {
    return industryData.map(item => ({
      ...item,
      count: keyEntNumData[item.key] || 0
    }));
  },

  /**
   * 更新地图状态
   * @param {Object} processedData - 处理后的数据
   * @param {Object} mapRegion - 地图区域信息
   */
  async _updateMapState(processedData, mapRegion) {
    const {mapData, cityData, provinceBarData, industryData, totalCount} =
      processedData;
    const {markPoints, total} = await this.generateMarkPoints(mapRegion);
    this.setData({
      heatMapData: mapData,
      totalCompanyCount: totalCount || total,
      markPoints: markPoints,
      provinceBarData: provinceBarData,
      cityBarData: cityData,
      industryData
    });
  },

  /**
   * 设置默认地图数据
   */
  _setDefaultMapData() {
    this.setData({
      heatMapData: [],
      totalCompanyCount: 0,
      markPoints: [],
      provinceBarData: [],
      cityBarData: []
    });
  },

  // 生成标记点数据（Level 3时使用）
  async generateMarkPoints(mapRegion, industryData) {
    const {externalFilterState, category} = this.data;
    const code =
      category !== 'classic'
        ? externalFilterState?.industrial_list?.code
        : externalFilterState?.classic_industry_code_list?.code;
    const industrial_list = {
      code,
      name: ''
    };
    // 只有Level 3时才生成标记点
    if (!mapRegion || mapRegion.level !== '3') {
      return {
        markPoints: [],
        total: 0
      };
    }

    try {
      const res = await chain.chainDetail({
        area_code_list: [mapRegion.code],
        current_page: 1,
        industrial_list: [industrial_list.code],
        page_size: 20
      });

      if (!res?.list?.length)
        return {
          markPoints: [],
          total: 0
        };

      // 返回对应区域的标记点，边界验证将在地图组件中进行
      return {
        markPoints: res.list.map(item => ({
          name: item.ent_name,
          coord: [parseFloat(item.location.lon), parseFloat(item.location.lat)],
          originalData: item
        })),
        total: res.total
      };
    } catch (error) {
      console.error('生成标记点失败:', error);
      return {
        markPoints: [],
        total: 0
      };
    }
  },

  // 处理弹窗内部滑动，阻止冒泡
  onPopupTouchMove() {
    // 阻止事件冒泡，允许弹窗内部滚动而不影响外部
    return false;
  }
});
