import dayjs from 'dayjs';

Component({
  properties: {
    // 容器高度
    wrapHeight: {
      type: String,
      value: '100vh'
    },
    // 左侧筛选数据
    leftList: {
      type: Array,
      value: []
    },
    // 右侧内容数据
    contentList: {
      type: Array,
      value: []
    },
    type: {
      type: String,
      value: 'hot' // 如果类型是2 就是产业链图谱
    },
    fixedCount: {
      type: Array,
      default: [0, 0, 0],
      observer: 'backfillCount'
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    // 滚动锚点
    idName: '',
    // 滚动位置
    scrollTop: 0,
    // 默认左侧筛选数据
    // defaultLeftList: [{title: '全部', isActive: true, key: 'all'}],
    // 默认内容数据 - 根据您的新数据结构
    // defaultContentList: [
    //   {
    //     key: 'all',
    //     title: '全部产业',
    //     list: [{id: 1, count: 1453, add: 23}]
    //   }
    // ],
    // 右侧固定内容
    rightFixedContent: [
      {title: '大类', num: '0', key: 'category'},
      {title: '领域', num: '0', key: 'field'},
      {title: '企业', num: '0', key: 'enterprise'}
    ],
    date: ''
  },
  lifetimes: {
    attached() {
      this.initData();
    }
  },
  methods: {
    // 初始化数据
    async initData() {},

    // 点击左侧筛选项
    onLeftItemClick(e) {
      const {item, index} = e.currentTarget.dataset;
      let {leftList} = this.data;

      // 重置所有选中状态
      leftList.forEach(listItem => {
        listItem.isActive = false;
      });

      // 设置当前选中状态
      leftList[index].isActive = true;

      // 先更新选中状态
      this.setData({
        leftList
      });

      // 使用 nextTick 确保 DOM 已更新，scroll-into-view 滚动
      wx.nextTick(() => {
        this.setData({
          idName: index === 0 ? 'top-anchor' : item.key
        });
      });

      // 触发父组件事件
      this.triggerEvent('leftItemClick', {
        item,
        index,
        key: item.key
      });
    },

    // 点击右侧卡片
    onCardClick(e) {
      const {item, index} = e.currentTarget.dataset;
      // 触发父组件事件
      this.triggerEvent('cardClick', {
        item,
        index
      });
    },
    // 数据更新
    backfillCount(val) {
      const type = this.properties.type;
      const date = dayjs().format('YYYY-MM-DD');
      let rightFixedContent = [];
      if (type == 'hot') {
        rightFixedContent = [
          {title: '大类', num: val[0], key: 'category'},
          {title: '领域', num: val[1], key: 'field'},
          {title: '企业', num: val[2], key: 'enterprise'}
        ];
      } else if (type == 'classic') {
        rightFixedContent = [
          {title: '大类', num: val[3], key: 'category'},
          {title: '领域', num: val[4], key: 'field'},
          {title: '企业', num: val[5], key: 'enterprise'}
        ];
      } else if (type == 'chainMap') {
        rightFixedContent = [
          {title: '大类', num: val[6], key: 'category'},
          {title: '领域', num: val[7], key: 'field'},
          {title: '企业', num: val[8], key: 'enterprise'}
        ];
      }
      // 获取当前年月日用dayjs
      this.setData({date, rightFixedContent});
    }
  }
});
