Component({
  properties: {
    // 最近查看列表
    recentList: {
      type: Array,
      value: []
    },
    // 标题
    title: {
      type: String,
      value: '最近查看'
    },
    // 数据项的唯一标识字段
    keyField: {
      type: String,
      value: 'id'
    },
    // 显示名称的字段
    nameField: {
      type: String,
      value: 'chain_name'
    },
    // 是否显示高亮（用于产业图谱等特殊场景）
    showHighlight: {
      type: Boolean,
      value: false
    }
  },

  data: {},

  methods: {
    // 点击最近查看项
    onItemClick(e) {
      const {item, type} = e.currentTarget.dataset;

      // 触发父组件事件
      this.triggerEvent('itemClick', {
        item,
        type // recentClear全部清除
      });
    }
  }
});
