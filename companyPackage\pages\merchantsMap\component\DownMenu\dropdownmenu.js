import {
  dataHelpers
} from '../../../../../components/ConfigurableForm/utils/helpers';
import {
  clearChildComponent,
  fillChildComponent,
  getChildComponentHasVal,
  getNameFromPop,
  handleMultiple,
  handleData
} from '../../../../../components/ConfigurableForm/utils/component-helpers';
var behavior = require('../../../../../template/menuhead/index');
const app = getApp();

// 删除不再需要的hunt相关变量

Component({
  behaviors: [behavior],
  properties: {
    dropDownMenuTitle: {
      type: Array,
      value: []
    },
    height: {
      //用于动态计算遮罩层高度
      type: Number || String,
      value: 0,
      observer: function (newVal, oldVal) {
        if (newVal != oldVal) {
          this.handleHeight();
        }
      }
    },
    isLocation: {
      // 是否已经授权
      type: Boolean,
      value: false
    },
    dropDownMenuDistrictData: {
      //地区
      type: Array,
      value: []
      // observer: function(newVal, oldVal) {处理数据
      //   let model = newVal[0] ? newVal[0] : null
      //   this.selectDefaltDistrictLeft(model)
      // }
    },

    dropDownMenuSourceData: {
      //全部行业
      type: Array,
      value: []
    },
    dropDownMenuFilterData: {
      //更多筛选
      type: Array,
      value: []
    },
    hideClass: {
      type: Boolean,
      value: false
    }
  },
  data: {
    isIphoneX: app.globalData.isIphoneX,
    sliderVal: 4,
    // 行业相关
    checkedList: [],
    oldData: '',
    district_val: false,
    source_val: true,
    filter_val: false,

    // 更多筛选相关 - 使用ConfigurableForm
    moreFilterParams: {}, // 更多筛选参数
    temParams: {}, //更多筛选改变的时候赋值

    selected_source_name: '5km',

    // 搜索框展开状态
    searchExpanded: false,
    searchVal: ''
  },
  observers: {
    checkedList: function (list) {
      console.log(list.length > 0);
      if (list.length > 0) {
        this.setData({
          district_val: true
        });
      } else {
        this.setData({
          district_val: false
        });
      }
    },
    sliderVal: function (val) {
      if (val >= 1) {
        this.setData({
          source_val: true,
          selected_source_name: val + 1 + 'km'
        });
      } else {
        this.setData({
          source_val: false,
          selected_source_name: ''
        });
      }
    },
    moreFilterParams: function (source) {
      this.setData({
        filter_val: getChildComponentHasVal(this, '#s-hunt')(source)
      });
    },
    filter_open: function (val) {
      if (!val) return;
      // 回填数据
      clearChildComponent(this, '#s-hunt')();
      fillChildComponent(this, '#s-hunt')(dataHelpers.deepClone(this.data.moreFilterParams));
    }
  },
  methods: {
    tapDistrictNav: function (e) {
      if (!this.data.isLocation) return;
      if (this.data.source_open || this.data.filter_open) return;
      if (this.data.district_open) {
        this.setData({
          district_open: false,
          source_open: false,
          filter_open: false,
          shownavindex: 0
        });
      } else {
        this.setData({
          district_open: true,
          source_open: false,
          filter_open: false,
          shownavindex: e.currentTarget.dataset.nav
        });
      }
    },
    tapSourceNav: function (e) {
      if (!this.data.isLocation) return;
      if (this.data.district_open || this.data.filter_open) return;

      if (this.data.source_open) {
        this.setData({
          source_open: false,
          district_open: false,
          filter_open: false,
          shownavindex: 0
        });
      } else {
        this.setData({
          source_open: true,
          district_open: false,
          filter_open: false,
          shownavindex: e.currentTarget.dataset.nav
        });
      }
    },
    tapFilterNav: function (e) {
      if (!this.data.isLocation) return;
      if (this.data.source_open || this.data.district_open) return;

      if (this.data.filter_open) {
        this.setData({
          source_open: false,
          district_open: false,
          filter_open: false,
          shownavindex: 0
        });
      } else {
        this.setData({
          source_open: false,
          district_open: false,
          filter_open: true,
          shownavindex: e.currentTarget.dataset.nav
        });
      }
    },

    getArea(e) {
      let arr = JSON.parse(JSON.stringify(e.detail)),
        name,
        codeList;
      name =
        arr
        .sort((a, b) => a.level - b.level)
        .map(i => i.name)
        .join(',')
        .slice(0, 4) + '...';
      codeList = handleMultiple(arr)
        .filter(item => item.status === 'checked')
        .map(item => item.code);
      if (name == '...') {
        name = '全部行业';
      }
      this.setData({
        district_open: false,
        oldData: arr,
        checkedList: arr,
        'dropDownMenuTitle[0]': name
      });
      // 使用最新的字段名
      this.triggerEvent('industry', {
        industry_code_list: codeList
      });
    },
    closeRegion(e) {
      this.closeHyFilter();
    },
    onSliderChange(e) {
      const val = e.detail.value;
      // 这里应该还要发请求 获取数据 渲染好多家相关企业
      this.setData({
        sliderVal: val
      });
      this.triggerEvent('slider', {
        slider: val + 1
      });
    },
    // ========== ConfigurableForm 相关方法 ==========

    // ConfigurableForm改变
    onConfigurableFormSubmit(e) {
      const {
        paramsData
      } = e.detail;
      this.setData({
        temParams: dataHelpers.deepClone(paramsData) // 深拷贝避免引用问题
      });
    },

    // ConfigurableForm VIP事件
    onConfigurableFormVip(e) {
      // 向上传递VIP事件
      this.triggerEvent('vip', e.detail);
    },

    // 确定更多筛选
    confirmMoreFilter() {
      const {
        temParams
      } = this.data;
      this.setData({
          moreFilterParams: dataHelpers.deepClone(temParams)
        },
        () => {
          this.backAll();
        }
      );
    },

    resetMoreFilter() {
      clearChildComponent(this, '#s-hunt')();
      this.setData({
        temParams: {}
      });
    },
    // 总的返回 - 简化版本
    backAll() {
      const {
        moreFilterParams
      } = this.data;
      const requestData = {
        ...handleData(moreFilterParams) // 使用ConfigurableForm处理的数据
      };
      this.triggerEvent('search', requestData);
      this.closeHyFilter();
    },

    /**
     * 搜索框获得焦点时展开
     */
    onSearchFocus() {
      this.setData({
        searchExpanded: true
      });

      // 关闭所有下拉菜单
      this.setData({
        district_open: false,
        source_open: false,
        filter_open: false,
        shownavindex: 0
      });
    },
    /**
     * 点击收起按钮
     */
    collapseSearch() {
      this.setData({
        searchExpanded: false
      });
    },
    // 点击取消
    onCancel() {
      this.setData({
        searchVal: ''
      });
      this.triggerEvent('searchIpt', {
        searchVal: ''
      });
    },
    /**
     * 搜索内容处理
     */
    searchContent(e) {
      let keyWord = e.detail.value;
      // console.log('搜索内容:', keyWord);
      // 触发搜索事件给父组件
      this.triggerEvent('searchIpt', {
        searchVal: keyWord
      });

      // 收起搜索框
      this.setData({
        searchExpanded: false,
        searchVal: keyWord
      });
    }
  },
  //组件生命周期函数，在组件实例进入页面节点树时执行
  attached: function () {
    this.handleHeight();
  },
  pageLifetimes: {
    hide() {
      this.setData({
        source_open: false,
        district_open: false,
        filter_open: false
      });
    }
  }
});