<!--权威榜单-->
<import src="/template/null/null"></import>
<import src="/template/more/more"></import>
<view class="definitive-list">
  <view class="search-wrapper">
    <h-input class="search-input" defaultVal="{{keyword}}" placeholder="榜单名称搜索" bindemit="handleInput"></h-input>
  </view>
  <!-- 筛选组件 -->
  <view class="merch-menu">
    <Select options="{{selectData}}" bindselect="onSelect" className="notes"></Select>
  </view>

  <view class="list-wrapper content_height  {{isNull ? 'no-margin':''}}">
    <scroll-view scroll-y refresher-enabled refresher-background="f7f7f7" bindrefresherrefresh="refresh" bindscrolltolower="loadMore" refresher-triggered="{{isTriggered}}" class="recommend-wrap {{isNull ? 'no-margin':''}}" style="height: {{scrollHeight}}px;">
      <block wx:if="{{!isNull}}">
        <block wx:for="{{list}}" wx:key="index">
          <view class="list-item">
            <view class="item-top">
              <image src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/definitive/type_icon.png" class="type_img" />
              <view class="name">{{item.list_type}}</view>
            </view>
            <view class="item-bottom" data-item="{{item}}" bindtap="go">
              <view class="label">
                <text wx:for="{{item.names}}" wx:for-item="name" wx:key="*this" class="{{name===params.keyword?'input_match':''}}">{{name}}</text>
              </view>
            </view>
          </view>
        </block>
        <view wx:if="{{list.length>=params.page_size}}">
          <template is='more' data="{{hasData:hasData}}"></template>
        </view>
      </block>
      <!--暂无数据 -->
      <view wx:else style="width: 100%;height: 100%;">
        <template is='null'></template>
      </view>
    </scroll-view>
  </view>
</view>
<Vip visible="{{visible}}"></Vip>