// Canvas相关的混入逻辑

// 导入常量配置
const {
  CANVAS_CONSTANTS,
  PERFORMANCE_CONSTANTS,
  UTILS_CONSTANTS,
  ERROR_MESSAGES
} = require('../config/constants.js');

const canvasMixin = {
  data: {
    // Canvas状态管理
    _canvasInitialized: false,
    _canvasInitializing: false
  },

  methods: {
    // 初始化Canvas（优化版本）
    initCanvas() {
      this.setData({_canvasInitializing: true});

      const query = wx.createSelectorQuery().in(this);
      const timeoutId = setTimeout(() => {
        console.warn('Canvas查询超时，降级到旧版API');
        this._handleCanvasInitError('查询超时');
      }, CANVAS_CONSTANTS.QUERY_TIMEOUT);

      query
        .select(CANVAS_CONSTANTS.SELECTOR_ID)
        .fields({node: true, size: true})
        .exec(res => {
          clearTimeout(timeoutId);

          if (this._validateCanvasQueryResult(res)) {
            this._initModernCanvas(res[0]);
          } else {
            this._handleCanvasInitError('查询结果无效');
          }
        });
    },

    // 验证Canvas查询结果
    _validateCanvasQueryResult(res) {
      return (
        res &&
        res[0] &&
        res[0].node &&
        typeof res[0].node.getContext === 'function'
      );
    },

    // 初始化现代Canvas API
    _initModernCanvas(canvasInfo) {
      try {
        const canvas = canvasInfo.node;
        const ctx = canvas.getContext('2d');

        if (!ctx) {
          throw new Error('无法获取Canvas上下文');
        }

        const {logicalWidth, logicalHeight, dpr} =
          this._getCanvasDimensions(canvasInfo);

        // 设置Canvas物理尺寸
        canvas.width = logicalWidth * dpr;
        canvas.height = logicalHeight * dpr;
        ctx.scale(dpr, dpr);

        this._setCanvasProperties(canvas, ctx, logicalWidth, logicalHeight);
        this._completeCanvasInit();
      } catch (error) {
        console.error('现代Canvas初始化失败:', error);
        this._handleCanvasInitError(error.message);
      }
    },

    // 获取Canvas尺寸信息（优化版本）
    _getCanvasDimensions(canvasInfo) {
      let windowWidth = 375;
      let pixelRatio = UTILS_CONSTANTS.DEFAULT_DPR;

      try {
        // 优先使用新的API
        if (wx.getWindowInfo) {
          const windowInfo = wx.getWindowInfo();
          windowWidth = windowInfo.windowWidth;
          pixelRatio = windowInfo.pixelRatio || UTILS_CONSTANTS.DEFAULT_DPR;
        } else if (wx.getDeviceInfo) {
          const deviceInfo = wx.getDeviceInfo();
          pixelRatio = deviceInfo.pixelRatio || UTILS_CONSTANTS.DEFAULT_DPR;
          // 如果没有窗口信息，使用默认宽度
          windowWidth = 375;
        } else if (wx.getSystemInfoSync) {
          // 降级到旧API
          console.warn('使用已废弃的wx.getSystemInfoSync，建议升级微信版本');
          const systemInfo = wx.getSystemInfoSync();
          windowWidth = systemInfo.windowWidth;
          pixelRatio = systemInfo.pixelRatio || UTILS_CONSTANTS.DEFAULT_DPR;
        }
      } catch (error) {
        console.warn('获取Canvas尺寸信息失败，使用默认值:', error);
      }

      return {
        logicalWidth: windowWidth,
        logicalHeight: canvasInfo.height,
        dpr: pixelRatio
      };
    },

    // 设置Canvas属性
    _setCanvasProperties(canvas, ctx, width, height) {
      this.canvas = canvas;
      this.ctx = ctx;
      this.isLegacyCanvas = false;

      this.setData({
        actualCanvasWidth: width,
        actualCanvasHeight: height,
        _canvasInitialized: true,
        _canvasInitializing: false
      });
    },

    // 完成Canvas初始化
    _completeCanvasInit() {
      setTimeout(() => {
        // 优先预加载图标，然后再绘制
        if (this.preloadIcons && typeof this.preloadIcons === 'function') {
          this.preloadIcons();
        } else if (this.drawTree && typeof this.drawTree === 'function') {
          this.drawTree();
        } else {
          console.warn('绘制方法不存在');
        }
      }, CANVAS_CONSTANTS.INIT_DELAY);
    },

    // 处理Canvas初始化错误
    _handleCanvasInitError(errorMsg) {
      console.warn(`Canvas初始化失败: ${errorMsg}，降级到旧版API`);
      this.setData({_canvasInitializing: false});
      this.initCanvasLegacy();
    },

    // 降级到旧版Canvas API（优化版本）
    initCanvasLegacy() {
      if (this.data._canvasInitialized) {
        console.log('Canvas已初始化');
        return;
      }

      try {
        const ctx = wx.createCanvasContext(CANVAS_CONSTANTS.CANVAS_ID, this);
        if (!ctx) {
          throw new Error('无法创建旧版Canvas上下文');
        }

        this._initLegacyCanvasProperties(ctx);
        this._setLegacyCanvasDimensions();
        this._completeLegacyCanvasInit();

        console.log('旧版Canvas初始化成功');
      } catch (error) {
        console.error('旧版Canvas初始化失败:', error);
        this._showCanvasError();
      }
    },

    // 初始化旧版Canvas属性
    _initLegacyCanvasProperties(ctx) {
      this.ctx = ctx;
      this.isLegacyCanvas = true;

      // 确保系统信息已初始化
      if (!this.data._systemInfo && this.initSystemInfo) {
        this.initSystemInfo();
      }
    },

    // 设置旧版Canvas尺寸
    _setLegacyCanvasDimensions() {
      const canvasWidthPx = this.rpxToPx
        ? this.rpxToPx(this.properties.canvasWidth || 750)
        : 375;
      const canvasHeightPx = this.rpxToPx
        ? this.rpxToPx(this.properties.canvasHeight || 1200)
        : 600;

      this.canvas = {width: canvasWidthPx, height: canvasHeightPx};

      this.setData({
        actualCanvasWidth: canvasWidthPx,
        actualCanvasHeight: canvasHeightPx,
        _canvasInitialized: true,
        _canvasInitializing: false
      });
    },

    // 完成旧版Canvas初始化
    _completeLegacyCanvasInit() {
      setTimeout(() => {
        if (this.preloadIcons && typeof this.preloadIcons === 'function') {
          this.preloadIcons();
        } else if (this.drawTree && typeof this.drawTree === 'function') {
          this.drawTree();
        } else {
          console.warn('绘制方法不存在');
        }
      }, CANVAS_CONSTANTS.INIT_DELAY);
    },

    // 显示Canvas错误
    _showCanvasError() {
      wx.showToast({
        title: 'Canvas初始化失败',
        icon: 'none',
        duration: 2000
      });
    },

    // 加载图片（优化版本）
    loadImage(imagePath) {
      return new Promise((resolve, reject) => {
        if (!imagePath) {
          reject(new Error('图片路径不能为空'));
          return;
        }

        if (!this.canvas) {
          reject(new Error('Canvas未初始化'));
          return;
        }

        try {
          const img = this.canvas.createImage();

          const timeoutId = setTimeout(() => {
            reject(new Error('图片加载超时'));
          }, 10000); // 10秒超时

          img.onload = () => {
            clearTimeout(timeoutId);
            resolve(img);
          };

          img.onerror = error => {
            clearTimeout(timeoutId);
            reject(new Error(`图片加载失败: ${error.message || '未知错误'}`));
          };

          img.src = imagePath;
        } catch (error) {
          reject(new Error(`创建图片对象失败: ${error.message}`));
        }
      });
    },

    // 重置Canvas状态
    resetCanvas() {
      this.canvas = null;
      this.ctx = null;
      this.isLegacyCanvas = false;

      this.setData({
        _canvasInitialized: false,
        _canvasInitializing: false,
        actualCanvasWidth: 0,
        actualCanvasHeight: 0
      });

      console.log('Canvas状态已重置');
    },

    // 检查Canvas是否可用
    isCanvasReady() {
      return this.data._canvasInitialized && this.canvas && this.ctx;
    }
  }
};

module.exports = {canvasMixin};
