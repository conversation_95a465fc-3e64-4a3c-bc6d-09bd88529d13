<import src="/template/null/null"></import>
<view class="rel-page">
  <view class="zhanwei"></view>
  <Navbar tabs="{{tabs}}" bindgetHeight="getHeight" defaultIndex="{{tabIdx}}" bindtabchange="tabchange" parent-class="rel-nav" isTitBlue>
    <view slot="atlas" class="atlas" style="height: {{canvasHeight}}px;">
      <view class="zhanwei2"></view>
      <block wx:if="{{!nulls}}">
        <!-- 图 -->
        <view style="width: 100%;height: {{canvasHeight}}px;">
          <chart id="chartID" seriesObj="{{connecObj}}" bindgetTempFilePath="getTempFilePath" hidden="{{!nulls}}" />
        </view>
        <!-- 有数据--按钮 -->
        <view class="btn-box">
          <view class="btn-group">
            <view class="btn-group-item flex_column_center">
              <view class="flex_all_center">
                <image src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/home/<USER>"></image>
              </view>
              <view>分享</view>
              <button class="share" size="mini" open-type="share"></button>
            </view>
            <view class="btn-group-item flex_column_center" style="border: none;" bindtap="saveImage">
              <view class="flex_all_center">
                <image src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/home/<USER>"></image>
              </view>
              <view>保存</view>
            </view>
          </view>
        </view>
        <!-- 引入暂无内容 -->
      </block>
      <template is="null" wx:if="{{nulls}}"></template>
    </view>
    <view slot="sol">
      <view style="height: {{restHeight-10}}px;" class="sol">
        <view>
          <!-- 文字空格占位 -->
          <view wx:if="{{isNull}}" class="titles-empty"></view>
          <block wx:if="{{!isNull}}">
            <view class="titles">
              共找到<text>{{allPathList.length}}</text>条对应关系
            </view>
            <!-- 卡片 -->
            <block wx:for="{{allPathList}}" wx:key="index" wx:for-item="obj">
              <view class="solCard">
                <view class="title">
                  <image src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/home/<USER>"></image>
                  <text>关系{{ index + 1}}</text>
                </view>
                <view class="con" bindtap="clickCard" data-item="{{obj}}">
                  <!-- 用一个数组来渲染这个 -->
                  <block wx:for="{{obj}}" wx:key="index">
                    <text>{{item.name}}</text>
                    <view class="arrow-box">
                      <view class="line" wx:if="{{!item.name}}"></view>
                      <view class="line-text" wx:if="{{!item.name}}">
                        {{item.Zb}}
                      </view>
                      <!-- 这个控制箭头方向 -->
                      <!-- <view class="arrow-box-ico-r" wx:if="{{!item.name}}"></view> -->
                      <view class="arrow-box-ico-r" wx:if="{{!item.name}}"></view>
                    </view>
                  </block>
                </view>
              </view>
            </block>
          </block>
          <view wx:if="{{isNull}}" style="height:  {{restHeight-10}}px;">
            <template data="{{errTips: '暂无关联路径'}}" is="null"></template>
          </view>
        </view>
      </view>
    </view>
  </Navbar>
  <!-- dialog -->
  <Dialog wx:if="{{openSet}}" bindsubmit="submit" bindclose="close"></Dialog>
</view>