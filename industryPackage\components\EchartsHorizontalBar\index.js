var echarts = require('../ec-canvas/echarts');
import {getPx} from '../../../utils/formate';
let horizontalBarChart, horizontalBarChart1;
Component({
  properties: {
    // 图表数据
    chartData: {
      type: Array,
      value: [],
      observer: 'onDataChange'
    },
    // 柱状图颜色 (渐变色)
    barColor: {
      type: Array,
      value: ['#2598E3', '#4AB8FF']
    },
    // X轴标题 (单位)
    xAxisTitle: {
      type: String,
      value: '单位：家'
    },
    isRepeat: {
      // 一个页面同时用一个组件的时候 会导致第一个不显示 --解决办法 这里智能解决一个页面用2次的清空
      type: Boolean,
      value: false
    },
    // 空状态显示文本
    emptyText: {
      type: String,
      value: '-暂无内容-'
    }
  },

  data: {
    ec: {},
    forceUseOldCanvas: false
    // myChart: null // 每个组件实例的私有图表实例
  },

  lifetimes: {
    attached() {
      // 检测开发者工具
      wx.getSystemInfo({
        success: res =>
          res.platform == 'devtools' &&
          this.setData({
            forceUseOldCanvas: true
          })
      });

      // 初始化 ec 对象
      this.setData({
        ec: {
          onInit: this.initChart.bind(this)
        }
      });
    },
    detached() {
      // 销毁图表实例
      if (horizontalBarChart) {
        horizontalBarChart.dispose();
        horizontalBarChart = null;
      }
      if (horizontalBarChart1) {
        horizontalBarChart1.dispose();
        horizontalBarChart1 = null;
      }
    }
  },

  methods: {
    // 数据变化时重新渲染图表
    onDataChange(newData, oldData) {
      // 无论数据是否为空都要更新图表（空数据时显示暂无内容）
      setTimeout(() => {
        const retryInstance = this.properties.isRepeat
          ? horizontalBarChart1
          : horizontalBarChart;

        console.log(newData, oldData, retryInstance);
        if (retryInstance) {
          this.updateChart(newData); // 直接传递新数据
        } else {
          // 如果实例为null，重新初始化
          console.log('图表实例为null，重新初始化');
          this._reinitChart(newData);
        }
      }, 100);
    },

    // 重新初始化图表
    _reinitChart(newData) {
      // 保存新数据，等初始化完成后使用
      this.pendingData = newData;

      // 重新触发图表初始化
      this.setData({
        ec: {
          onInit: this.initChart.bind(this)
        }
      });
    },

    // 初始化图表
    initChart(canvas, width, height, dpr) {
      const chartInstance = echarts.init(canvas, null, {
        width: width,
        height: height,
        devicePixelRatio: dpr
      });

      // 将图表实例保存到组件的 data 中 --会导致栈溢出
      // this.setData({
      //   myChart: chartInstance
      // });
      if (this.properties.isRepeat) {
        horizontalBarChart1 = chartInstance;
      } else {
        horizontalBarChart = chartInstance;
      }

      // 检查是否有待处理的数据
      if (this.pendingData) {
        console.log('使用待处理的数据更新图表:', this.pendingData);
        this.updateChart(this.pendingData);
        this.pendingData = null; // 清除待处理的数据
      } else {
        this.updateChart();
      }

      return chartInstance;
    },

    // 更新图表
    updateChart(chartData) {
      // 如果没有传入数据，则使用 properties 中的数据
      const data = chartData || this.properties.chartData;
      const {barColor, xAxisTitle} = this.properties;

      // 检查图表实例是否存在
      const chartInstance = this.properties.isRepeat
        ? horizontalBarChart1
        : horizontalBarChart;
      if (!chartInstance) return;

      // 处理空数据情况
      if (!data || data.length === 0) {
        this.showEmptyState(chartInstance);
        return;
      }

      // 隐藏loading状态（如果之前显示了的话）
      chartInstance.hideLoading();

      // 使用固定高度16rpx

      const option = {
        // 清除空状态的 graphic 元素
        graphic: {
          elements: []
        },
        grid: {
          left: '4%',
          right: '15%',
          top: '10%',
          bottom: '4%',
          containLabel: true
        },
        // 横向柱状图：X轴为数值轴
        xAxis: {
          type: 'value',
          axisLabel: {
            fontWeight: 400,
            fontSize: getPx(24),
            color: '#9B9EAC',
            formatter: function (value) {
              // 大于10000显示为万，保留1位小数
              if (value >= 10000) {
                return (value / 10000).toFixed(0) + '万';
              }
              return value;
            }
          },
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          },
          splitLine: {
            lineStyle: {
              color: '#eee',
              width: 0.5,
              type: 'dashed'
            }
          }
        },
        // 横向柱状图：Y轴为类目轴
        yAxis: {
          type: 'category',
          data: data.map(item => item.name),
          name: xAxisTitle,
          nameGap: 10, // 与轴线的距离
          nameTextStyle: {
            fontWeight: 400,
            fontSize: getPx(24),
            color: '#9B9EAC',
            align: 'right',
            padding: [0, 0, 0, 0] // 增加下边距
          },
          axisLabel: {
            fontWeight: 400,
            fontSize: getPx(24),
            color: '#74798C'
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: '#eee',
              width: 0.5
            }
          },
          axisTick: {
            show: false
          }
        },
        series: [
          {
            type: 'bar',
            data: data.map(item => item.value),
            barMaxWidth: getPx(16), // 固定宽度16rpx
            itemStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 1,
                y2: 0,
                colorStops: [
                  {
                    offset: 0,
                    color: barColor[0] || '#E72410'
                  },
                  {
                    offset: 1,
                    color: barColor[1] || '#F17B6F'
                  }
                ]
              },
              borderRadius: [0, 0, 0, 0] // 右侧圆角
            },
            label: {
              show: true,
              position: 'right',
              fontWeight: 400,
              fontSize: getPx(24),
              color: '#20263A',
              formatter: '{c}'
            }
          }
        ]
      };

      // 使用组件实例的图表对象
      // 使用 true 参数完全替换配置，确保清除空状态
      if (this.properties.isRepeat) {
        horizontalBarChart1.setOption(option, true);
      } else {
        horizontalBarChart.setOption(option, true);
      }
    },

    // 显示空状态
    showEmptyState(chartInstance) {
      const {emptyText} = this.properties;

      // 使用 graphic 组件显示空状态
      const emptyOption = {
        // 完全清除坐标轴
        xAxis: {
          show: false
        },
        yAxis: {
          show: false
        },
        // 清除所有系列数据
        series: [],
        // 清除网格
        grid: {
          show: false
        },
        // 清除标题
        title: null,
        // 清除图例
        legend: {
          show: false
        },
        // 显示空状态文本
        graphic: {
          elements: [
            {
              type: 'text',
              left: 'center',
              top: 'middle',
              style: {
                text: emptyText,
                fontSize: 14,
                fontWeight: 'normal',
                fill: '#999999'
              }
            }
          ]
        }
      };
      // 使用 true 参数，表示不合并配置，完全替换
      chartInstance.setOption(emptyOption, true);
    }
  }
});
