<import src="/template/menuhead/index"></import>
<view class="head">
  <view class="head_nav">
    <!-- 左侧下拉框区域 -->
    <view class="dropdown-section {{searchExpanded ? 'hidden' : ''}}" wx:if="{{!searchExpanded}}">
      <template is="menu-head" data="{{selected_source_name,dropDownMenuTitle,district_open,source_open,filter_open,selected_filter_name,selected_source_name,district_val,source_val,filter_val }}"></template>
    </view>

    <!-- 右侧搜索框区域 -->
    <view class="search-section {{searchExpanded ? 'expanded' : 'collapsed'}}">
      <view class="search-container">
        <view class="left" catchtap="onSearchFocus">
          <image src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/search.png" class="search-icon"></image>
          <input class="search-input" placeholder="搜索附近企业" placeholder-class="placeholder_class" confirm-type="search" bindconfirm="searchContent" value="{{searchVal}}" />
          <text class="cancel {{searchExpanded ? 'show' : 'hide'}}" bindtap="onCancel">取消</text>
        </view>
        <text class="collapse-btn {{searchExpanded ? 'show' : 'hide'}}" bindtap="collapseSearch">收起</text>
      </view>
    </view>
  </view>
  <view class="zhanwei"></view>
</view>

<!-- 地区 -->
<industry-select zIndex="{{1}}" visible="{{district_open}}" top="{{top}}" oldData="{{oldData}}" bindsubmit="getArea" dataType="eleseicAry" bindclose="closeRegion" />

<!-- 距离 -->
<view class="container container_hd {{source_open ? 'show' : 'disappear'}} " style="height: calc( 100vh - 240rpx );background-color: transparent;" ontap="closeHyFilter" capture-catch:touchmove="preventdefault">
  <view class="change_txt" catchtap="closeHyFilter" data-type="child">调整区域大小</view>
  <view class="tier" catchtap="closeHyFilter" data-type="child">
    <view class="slider">
      <!-- 这里的圆没办法变成其他颜色  min="{{1}}" //为了渲染不能加这个-->
      <slider bindchange="onSliderChange" max="{{9}}" step="1" activeColor="#E72410" backgroundColor="#f2f2f2" block-size="25" value="{{sliderVal}}" class="source-slider" />
      <!-- 原来的样式满足不了所以写一个覆盖掉 -->
      <view class="slider-item">
        <view class="slider-line">
          <view class="slider-line-b">
            <!-- 实际控制这个进度条宽高 -->
            <view class="slider-active" style="width: {{(sliderVal)/9*100}}\%;">
              <view class="circle slider-active-block "></view>
            </view>
          </view>
        </view>
      </view>
    </view>
    <view>半径{{sliderVal+1}}KM</view>
  </view>
</view>
<!-- 更多筛选 - 使用ConfigurableForm -->
<view class="container container_hd {{filter_open ? 'show' : 'disappear'}} " style="height: {{computedHeight}}px;border-top: 1px solid #f7f7f7;" ontap="closeHyFilter">
  <view class="z-height" catchtap="closeHyFilter" data-type="child" catchtap="_disabledPenetrate">
    <view style="height: {{computedHeight-(isIphoneX ? 85:55)}}px;" class="searWrap">
      <!-- ConfigurableForm 组件 -->
      <ConfigurableForm id="s-hunt" variant="map" excludeFields="{{['ent_name', 'area_code_list', 'industry_code_list']}}" bind:submit="onConfigurableFormSubmit" bind:vip="onConfigurableFormVip" wrapHeight="{{computedHeight-(isIphoneX ? 85:55)}}px" />
    </view>
    <view class="footer" style="height: {{isIphoneX?'85px':'55px'}};">
      <text class="reset" bindtap="resetMoreFilter">重置</text>
      <text bindtap="confirmMoreFilter">确定</text>
    </view>
  </view>
</view>