@import '../../../../../template/null/null.scss';

.suggest {
  width: 100%;
  min-height: 100vh;
  background: #f7f7f7;
  position: relative;
  padding-bottom: 168rpx;
  padding-top: 20rpx;
}

.suggest-title {
  width: 100%;
  padding: 28rpx 0 24rpx 24rpx;
  background: #fff;
  text-align: LEFT;
  font-size: 28rpx;
  font-family: PingFang SC-Semibold, PingFang SC;
  font-weight: 600;
  color: #20263A;
  line-height: 33rpx;
}

/*  */
.area {
  background: #fff;
  padding: 0 24rpx;
  width: 100%;
  min-height: 122rpx;
}

.area-pla,
.textarea {
  font-size: 28rpx;
  font-family: PingFang SC, PingFang SC-Regular;
  font-weight: 400;
  text-align: LEFT;
}

.area-pla {
  color: #74798c;
}

.textarea {
  color: #20263a;
  width: 100%;
  height: 168rpx;
  /* text-indent: 4rpx; */
  /* border: 1px solid red; */
}

.word-number {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  height: 40rpx;
  text-align: right;
  font-size: 28rpx;
  font-family: PingFang SC, PingFang SC-Regular;
  font-weight: 400;
  padding: 24rpx 0 26rpx;
  color: #9b9eac;
}

/* <!-- 上传图片 --> */
.sug-imgbox-t {
  padding: 26rpx 24rpx 0;
  font-size: 28rpx;
  font-family: PingFang SC-Semibold, PingFang SC;
  font-weight: 600;
  color: #20263A;
  line-height: 33rpx;
  text-align: left;
  background: #fff;
}

.sug-imgbox {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  padding: 0 0 28rpx;
  background: #fff;
}


.sug-img {
  position: relative;
  width: 120rpx;
  height: 120rpx;
  margin-right: 24rpx;
  margin-top: 28rpx;
}

.sug-img .delet {
  position: absolute;
  top: 4rpx;
  right: 4rpx;
  width: 32rpx;
  height: 32rpx;
}

.sug-upload {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 120rpx;
  height: 120rpx;
  border: 2rpx solid #dedede;
  border-radius: 4rpx;
  margin-top: 28rpx;
}

.sug-upload .img {
  width: 40rpx;
  height: 40rpx;
  margin-bottom: 8rpx;
}

.sug-upload .text {
  font-size: 24rpx;
  font-family: PingFang SC, PingFang SC-Regular;
  font-weight: 400;
  text-align: LEFT;
  color: #9b9eac;
}

/* 联系方式  */
.sug-phone {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 24rpx;
  width: 750rpx;
  height: 92rpx;
  background: #ffffff;
  border: 1px solid #f7f7f7;
}

.sug-phone-l {
  display: flex;
  align-items: center;
  height: 100%;
}

.sug-phone-l .sug-text {
  font-size: 28rpx;
  font-family: PingFang SC-Semibold, PingFang SC;
  font-weight: 600;
  color: #20263A;
  line-height: 33rpx;
}

.sug-input {
  flex: 1;
  /* width: 300rpx; */
  /* border: 1px solid red; */
  height: 100%;
  font-size: 28rpx;
  font-family: PingFang SC, PingFang SC-Regular;
  font-weight: 400;
  text-align: RIGHT;
  color: #20263a;
}

.input-pla {
  font-size: 28rpx;
  font-family: PingFang SC, PingFang SC-Regular;
  font-weight: 400;
  text-align: RIGHT;
  color: #9b9eac;
}

.arrow-r {
  width: 24rpx;
  height: 24rpx;
  margin-left: 12rpx;
}

/* 按钮 */
.sug-btm {
  position: absolute;
  bottom: 0;
  width: 750rpx;
  height: 168rpx;
  background: #ffffff;
  box-shadow: 8rpx 0rpx 8rpx 0rpx rgba(204, 204, 204, 0.20);
}

.sug-btm .sug-btn {
  position: relative;
  left: 50%;
  transform: translateX(-50%);
  -webkit-transform: translateX(-50%);
  margin-top: 10rpx;
  width: 702rpx;
  height: 80rpx;
  line-height: 80rpx;
  background: linear-gradient(135deg, #F56E60  0%, #E72410 100%);
  border-radius: 8rpx;
  font-size: 34rpx;
  font-family: PingFang SC, PingFang SC-Semibold;
  font-weight: 600;
  text-align: CENTER;
  color: #ffffff;
}

/* 选择联系人---底部弹窗 */
.addperson {
  display: flex;
  align-items: center;
  height: 40rpx;
}

.addperson view:nth-child(2) {
  font-size: 28rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #E72410;
  line-height: 33rpx;
}

.addperson .img {
  width: 32rpx;
  height: 32rpx;
  margin-right: 12rpx;
  border-radius: 50%;
  overflow: hidden;
}

.person-box {
  max-height: 790rpx;
  min-height: 376rpx;
  border-top: 1px solid #f7f7f7;
  border-bottom: 1px solid #f7f7f7;
  /* padding: 0 24rpx; */
}