import {
  singleChainKeyEntNumApi,
  getReportPageListApi,
  getStaticApi,
  appChainEntTrendApi
} from '../../../service/industryApi';

import {formatLargeNumberAry} from '../../../utils/util';

const app = getApp();

Page({
  data: {
    text: '生物化学合成',
    // 头部统计数据
    statsData: [
      {
        value: '0',
        unit: '家',
        label: '企业总数',
        key: 'ent_cnt'
      },
      {
        value: '0',
        unit: '件',
        label: '专利总量',
        key: 'patent_cnt'
      },
      {
        value: '0',
        unit: '%', //
        label: '发明专利占比',
        key: 'invent_prop'
      }
    ],
    industryData: [
      {
        name: '上市企业',
        img: 'https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/cyt_v2/image/industry/i_ent1.png',
        count: 0,
        key: 'listed_ent_cnt'
      },
      {
        name: '小巨人',
        img: 'https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/cyt_v2/image/industry/i_ent2.png',
        count: 0,
        key: 'little_giant'
      },
      {
        name: '单项冠军',
        img: 'https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/cyt_v2/image/industry/i_ent3.png',
        count: 0,
        key: 'single_champ'
      },
      {
        name: '高新技术企业',
        img: 'https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/cyt_v2/image/industry/i_ent4.png',
        count: 0,
        key: 'high_tech_ent_cnt'
      },
      {
        name: '专精特新中小企业',
        img: 'https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/cyt_v2/image/industry/i_ent5.png',
        count: 0,
        key: 'specialized_ent_cnt'
      },
      {
        name: '创新型中小企业',
        img: 'https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/cyt_v2/image/industry/i_ent6.png',
        count: 0
      },
      {
        name: '科技型企业',
        img: 'https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/cyt_v2/image/industry/i_ent7.png',
        count: 0,
        key: 'technology_ent_cnt'
      }
    ],
    category: 'classic', // 经典产业链
    // 上拉刷新相关
    showLoadingFooter: false,
    loadingState: 'loading', // loading, nomore
    reportList: [],
    currentPage: 1,
    page_size: 10,
    hasMoreData: true,
    isLoading: false, // 添加加载状态标志，防止重复请求
    title: '',
    reportTotal: 0,
    //地图
    defaultMapType: 'city', // 地图类型
    mapData: [], //地图数据
    FiveChartData: [
      // 增长潜力（近五年）
      // {year: '2019', value: 120},
    ]
  },

  onLoad(options) {
    const {chain_code, chain_name, category} = options;
    // 获取沉浸式导航栏组件实例
    this.navbar = this.selectComponent('#navbar');

    this.setData(
      {
        title: decodeURIComponent(chain_name),
        chain_code,
        category
      },
      () => {
        this.getInit();
      }
    );
  },

  onShow() {},
  async getInit() {
    let {chain_code, category, industryData, FiveChartData, statsData} =
      this.data;
    let chain_type = category === 'classic' ? 3 : 2;
    app.showLoading('加载中');
    const [getStatic, singleChainKeyEntNum, appChainEntTrend] =
      await Promise.allSettled([
        getStaticApi({
          chain_code,
          chain_type,
          type: category
        }),
        singleChainKeyEntNumApi({
          chain_code,
          chain_type,
          region_type: 0
        }),
        appChainEntTrendApi({
          chain_code,
          chain_type,
          region_type: 0
        })
      ]);
    // 企业名单上面统计数量;
    if (getStatic.status === 'fulfilled') {
      const getStaticData = getStatic.value;
      statsData = statsData.map(item => {
        return {
          ...item,
          value: formatLargeNumberAry(getStaticData?.[item.key] || 0, 1, true)
        };
      });
    }
    // 重点企业;
    if (singleChainKeyEntNum.status === 'fulfilled') {
      const singleChainKeyEntNumData = singleChainKeyEntNum.value;
      industryData = industryData.map(item => {
        return {
          ...item,
          count: singleChainKeyEntNumData[item.key] || 0
        };
      });
    }
    // 柱状图;
    if (appChainEntTrend.status === 'fulfilled') {
      // date value
      const appChainEntTrendData = appChainEntTrend.value;
      FiveChartData = appChainEntTrendData
        .map(item => {
          return {
            year: item.date,
            value: item.value
          };
        })
        .sort((a, b) => a.year - b.year);
    }

    // 研报第一页数据
    await this.loadReportData();
    wx.hideLoading();
    this.setData({
      industryData,
      statsData,
      FiveChartData
    });
  },

  // 头部按钮点击事件
  onHeadButtonClick(e) {
    const {chain_code, category} = this.data;
    // console.log('点击查看企业名单', e.detail);
    const url = `/industryPackage/pages/businessList/index?chain_code=${chain_code}&chain_name=${decodeURIComponent(
      this.data.title
    )}&category=${category}`;
    app.route(this, url);
  },

  // 企业类型卡片点击事件
  onEnterpriseItemClick(e) {
    const {
      item: {name}
    } = e.detail;
    const {chain_code, category} = this.data;
    // 可以根据不同的企业类型跳转到不同页面
    wx.navigateTo({
      url: `/industryPackage/pages/businessList/index?chain_code=${chain_code}&title=${encodeURIComponent(
        name
      )}&chain_name=${decodeURIComponent(this.data.title)}&category=${category}`
    });
  },

  // 滚动到底部事件
  onScrollToLower(e) {
    // 如果正在加载中，给用户提示
    if (this.data.isLoading) {
      // console.log('正在加载中，请稍候...');
      return;
    }

    this.loadMoreReportData();
  },

  // 加载研报数据
  async loadReportData() {
    const {chain_code, category, title} = this.data;

    try {
      const params = {
        type: category === 'classic' ? 'classic' : 'hot', // 使用产业图谱类型的研报
        page_index: 1,
        page_size: this.data.page_size
      };
      if (category === 'classic') {
        params.classic_industry_id = chain_code;
      } else {
        params.hot_industry_code = chain_code;
        params.hot_industry_name = title;
      }
      const response = await getReportPageListApi(params);

      if (response && response.list) {
        const transformedData = this.transformReportData(response.list);
        const total = response.total;

        this.setData({
          reportList: transformedData,
          currentPage: 1,
          hasMoreData: total > this.data.page_size,
          showLoadingFooter: false,
          isLoading: false,
          reportTotal: total,
          loadingState: total
            ? total > this.data.page_size
              ? 'loading'
              : 'nomore'
            : 'empty'
        });
      } else {
        this.setData({
          reportList: [],
          currentPage: 1,
          hasMoreData: false,
          showLoadingFooter: false,
          isLoading: false
        });
      }
    } catch (error) {
      console.error('加载研报数据失败:', error);
      this.setData({
        reportList: [],
        currentPage: 1,
        hasMoreData: false,
        showLoadingFooter: false,
        isLoading: false
      });
    }
  },

  // 加载更多研报数据
  async loadMoreReportData() {
    const {
      hasMoreData,
      currentPage,
      isLoading,
      chain_code,
      category,
      title,
      reportTotal,
      page_size
    } = this.data;

    // 如果没有更多数据或正在加载中，直接返回
    if (!hasMoreData || isLoading) {
      return;
    }

    // if (reportTotal === 0 || reportTotal <= page_size * currentPage) {
    //   return;
    // }

    // 设置加载状态，防止重复请求
    this.setData({
      isLoading: true,
      showLoadingFooter: true
    });

    try {
      const nextPage = currentPage + 1;
      const params = {
        type: 'original',
        page_index: nextPage,
        page_size: this.data.page_size,
        type: category === 'classic' ? 'classic' : 'hot'
      };
      if (category === 'classic') {
        params.classic_industry_id = chain_code;
      } else {
        params.hot_industry_code = chain_code;
        params.hot_industry_name = title;
      }
      const response = await getReportPageListApi(params);

      if (response && response.list) {
        const newData = this.transformReportData(response.list);
        const updatedList = [...this.data.reportList, ...newData];
        const total = response.total;
        this.setData({
          reportList: updatedList,
          currentPage: nextPage,
          showLoadingFooter: true,
          hasMoreData: total > updatedList.length,
          isLoading: false,
          loadingState: total
            ? total > updatedList.length
              ? 'loading'
              : 'nomore'
            : 'empty'
        });
      } else {
        // 没有更多数据
        this.setData({
          hasMoreData: false,
          loadingState: 'nomore',
          showLoadingFooter: false,
          isLoading: false
        });
      }
    } catch (error) {
      console.error('加载更多研报数据失败:', error);
      this.setData({
        showLoadingFooter: false,
        isLoading: false
      });
    }
  },

  // 转换API数据格式
  transformReportData(apiList) {
    if (!Array.isArray(apiList)) {
      return [];
    }

    return apiList.map(item => {
      // 处理产业链标签
      const tags = [];
      if (item.chains && Array.isArray(item.chains)) {
        // 取前2个产业链作为标签
        tags.push(...item.chains.slice(0, 2).map(chain => chain.name));
      }

      return {
        id: item.id,
        title: item.report_name || '--',
        size: item.file_size || '-',
        tags: tags,
        organization: item.publish_org || '未知机构',
        date: this.formatDate(item.publish_time),
        pdfUrl: item.report_oss_url || '',
        imgTit: item.report_type === 'REPORT_TYPE_1' ? '撼地智库' : '产业专题',
        page_num: item.page_num,
        // 保留原始数据以备后用
        originalData: item
      };
    });
  },

  // 格式化日期
  formatDate(dateString) {
    if (!dateString) return '未知日期';
    try {
      const date = new Date(dateString);
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    } catch (error) {
      return dateString; // 如果格式化失败，返回原始字符串
    }
  },
  onStatsClick(e) {
    const {label} = e.detail;
    const {chain_code, category} = this.data;
    let url = '';
    if (label == '企业总数') {
      //跳转到列表页面
      url = `/industryPackage/pages/businessList/index?chain_code=${chain_code}&chain_name=${decodeURIComponent(
        this.data.title
      )}&category=${category}`;
    }
    app.route(this, url);
  }
});
