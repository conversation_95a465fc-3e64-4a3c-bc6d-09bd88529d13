// childSubpackage/components/ReportCard/index.js
Component({
  properties: {
    // 研报列表数据
    reportList: {
      type: Array,
      value: []
    },
    // 最大显示条数，0表示不限制
    maxCount: {
      type: Number,
      value: 0
    }
  },

  data: {},

  methods: {
    // 点击研报事件
    onReportClick(e) {
      const index = e.currentTarget.dataset.index;
      const item = this.properties.reportList[index];

      // 向父组件传递点击事件
      this.triggerEvent('reportclick', {
        item,
        index
      });
    },

    // 获取显示的研报列表
    getDisplayList() {
      const {reportList, maxCount} = this.properties;
      if (maxCount > 0) {
        return reportList.slice(0, maxCount);
      }
      return reportList;
    },

    // 获取显示的数量
    getDisplayCount() {
      const {reportList, maxCount} = this.properties;
      if (maxCount > 0 && reportList.length > maxCount) {
        return maxCount;
      }
      return reportList.length;
    }
  }
});
