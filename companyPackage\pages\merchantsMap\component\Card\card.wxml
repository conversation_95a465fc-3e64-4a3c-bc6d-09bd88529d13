<import src="/template/null/null"></import>
<scroll-view class="list" bindscrolltolower="loadMore" scroll-y="{{true}}" bindscroll="bindscroll">
  <view class="search-card" wx:for="{{souceData}}" wx:key="index">
    <view class="card-boxs">
      <view class="card-logo" bindtap="goDetail" data-item="{{item}}">
        <image src="{{item.logo || item.ent_logo || 'https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/bussiness/logo-a.png'}}" mode="aspectFit"></image>
      </view>
      <view class="card-head">
        <view class="card_h">
          <view class="card_h_l text-ellipsis" bindtap="goDetail" data-item="{{item}}">
            <text wx:for="{{item.chainArr}}" wx:key="index" class="{{item===search?'input_match':''}}">{{item}}</text>
          </view>
          <view class="card_h_r" bindtap="collect" data-item="{{item}}" data-index="{{index}}">
            <!-- 后面根据具体传进来的字段判断 -->
            <view class="card_h_r_img">
              <image src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/home/<USER>" wx:if="{{!item.collect}}"></image>
              <image src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/home/<USER>" wx:else></image>
            </view>
          </view>
        </view>
        <view class="card_tag">
          <view class="card_tag_box">
            <text class="{{tagItem.tagColor}}" wx:for="{{item.tags}}" wx:for-item="tagItem" wx:key="tagName">{{tagItem.tagName}}</text>
          </view>
        </view>
      </view>
    </view>
    <view class="card_ico">
      <text class="distance_txt">距您{{item.distance}}KM</text>
      <view class="card_ico_i" bindtap="relation" data-item="{{item}}">
        <view class="card_ico_i_img">
          <image src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/home/<USER>"></image>
        </view>
        <view>
          联系方式
        </view>
      </view>
      <view wx:if="{{item.official_website}}" class="card_ico_i" bindtap="official" data-item="{{item}}">
        <view class="card_ico_i_img">
          <image src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/home/<USER>"></image>
        </view>
        <view>
          官网
        </view>
      </view>
      <view class="card_ico_i" bindtap="site" data-item="{{item}}">
        <view class="card_ico_i_img">
          <image src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/home/<USER>"></image>
        </view>
        <view>
          地址
        </view>
      </view>
    </view>
  </view>
  <view wx:if="{{souceData.length}}" style="height: 80rpx;"></view>
  <!--暂无数据 -->
  <view wx:if="{{!souceData.length}}" class="empty-box" style="width: 100%; height: {{emptyBoxHeight}}; opacity: {{!emptyBoxHeight ? 0 : 1}};">
    <template is='null' data="{{errTips:'暂无数据'}}"></template>
  </view>
  <!-- 未登录只能查看5条数据 -->
  <view class="login-tip" wx:if="{{!isLogin && souceData.length >= 5}}">
    <vloginOverlay bindsubmit="login"></vloginOverlay>
  </view>
</scroll-view>