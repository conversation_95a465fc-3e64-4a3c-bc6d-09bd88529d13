@import '../../../template/more/more.scss';
@import '../../../template/null/null.scss';
.definitive-list {
  width: 100%;
  background: #f7f7f7;
  height: 100vh;
  overflow: hidden;
  padding-bottom: env(safe-area-inset-bottom);
}
.search-wrapper {
  background: #fff;
  padding: 20rpx 24rpx;
}
.recommend-wrap {
  overflow-x: hidden;
  background-color: #f7f7f7;
}
/* 联系方式弹窗 */
.contact_box {
  position: relative;
  width: 100%;
  height: 96rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
}

.contact_box::after {
  content: ' ';
  width: 100%;
  height: 1px;
  background: #eee;
  position: absolute;
  bottom: 0;
  /* left: -50%; */
  transform: scaleY(0.5);
}

.contact_box:last-child::after {
  content: ' ';
  width: 100%;
  height: 1px;
  background: transparent;
  position: absolute;
  bottom: 0;
  /* left: -50%; */
  transform: scaleY(0.5);
}

.contact_left {
  display: flex;
  align-items: end;
  padding: 28rpx 0;
}

.contact_left image {
  width: 32rpx;
  height: 32rpx;
  margin-right: 12rpx;
}

.contact_number {
  font-size: 28rpx;
  font-family: PingFang SC, PingFang SC-Regular;
  font-weight: 400;
  text-align: LEFT;
  color: #076ee4;
}

.contact_right {
  font-size: 28rpx;
  font-family: PingFang SC, PingFang SC-Regular;
  font-weight: 400;
  text-align: CENTER;
  color: #74798c;
}
/* dialog文字样式 */
.dialog-con {
  font-size: 28rpx;
  font-family: PingFang SC, PingFang SC-Regular;
  font-weight: 400;
  text-align: CENTER;
  color: #74798c;
}

.dialog-con .map {
  position: relative;
  width: 100%;
  height: 112rpx;
  line-height: 112rpx;
  text-align: center;
  font-size: 34rpx;
  font-family: PingFang SC-Medium, PingFang SC;
  font-weight: 500;
  color: #076ee4;
}

.weui-dialog__title {
  font-weight: 500 !important;
  color: #000000 !important;
  line-height: 40rpx;
  font-size: 34rpx !important;
}

.light_bd {
  padding: 0 !important;
}

.dialog-con .map::after {
  content: ' ';
  width: 200%;
  height: 1rpx;
  background: #e5e5e5;
  position: absolute;
  top: 0;
  left: 0%;
  transform: scaleY(0.5);
}

.dialog-con .cancel {
  position: relative;
  height: 112rpx;
  line-height: 112rpx;
  text-align: center;
  font-size: 34rpx;
  font-family: PingFang SC-Medium, PingFang SC;
  font-weight: 500;
  color: #000000;
}

.dialog-con .cancel::after {
  content: ' ';
  width: 200%;
  height: 1rpx;
  background: #e5e5e5;
  position: absolute;
  top: 0;
  left: 0%;
  transform: scaleY(0.5);
}

/* 登录缺省页面 */
.card-login {
  position: relative;
  display: flex;
  flex-direction: column;
  /* justify-content: center; */
  align-items: center;
  width: 100%;
  height: 100vh;
  background: #f7f7f7;
}
.card-login image {
  width: 404rpx;
  height: 346rpx;
  margin-top: 170rpx;
}

.card-login .txt {
  padding-top: 40rpx;
  padding-bottom: 48rpx;
  font-size: 28rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #20263a;
  line-height: 33rpx;
}

.card-login .btn {
  margin: 0;
  padding: 0;
  width: 340rpx;
  height: 80rpx;
  line-height: 80rpx;
  background: linear-gradient(135deg, #4ab8ff 0%, #076ee4 100%);
  border-radius: 8rpx 8rpx 8rpx 8rpx;
  font-size: 32rpx;
  font-family: PingFang SC-Semibold, PingFang SC;
  font-weight: 400;
  color: #ffffff;
  text-align: center;
}
.top {
  height: 174rpx;
  background: #ffffff;
  padding: 0 24rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
  border-top: 1rpx solid #eeeeee;
}
.top .name {
  font-size: 32rpx;
  font-family: PingFang SC-Semibold, PingFang SC;
  font-weight: 600;
  color: #20263a;
  line-height: 38rpx;
  margin-bottom: 16rpx;
}
.top .inline {
  display: flex;
}
.top .inline .time {
  font-size: 24rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #74798c;
  line-height: 28rpx;
}
.top .inline .publish {
  flex-shrink: 0;
}
.top .inline .source {
  padding-left: 40rpx;
}
/* .source-text{
  width: 80rpx;
} */

.search-card {
  margin-top: 0 !important;
  margin-bottom: 20rpx;
}
.no-margin {
  margin: 0;
  padding: 0;
}
.weui-loadmore {
  width: 65%;
  margin: 0 auto;
  font-size: 14px;
  text-align: center;
  min-height: 80rpx !important;
  line-height: 80rpx;
  padding-bottom: 96rpx !important;
}
