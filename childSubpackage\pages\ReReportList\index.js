import {getHeight} from '../../../utils/height';
import {getReportPageListApi, addBevHis} from '../../../service/industryApi';

const app = getApp();

Page({
  data: {
    isLogin: app.isLogin(),
    // 研报数据
    reportList: [],
    listRequestParams: {
      type: 'chainMap' // 热门研报
    },
    RequestUrlFn: getReportPageListApi,
    // 列表数据
    company_num: 0,
    listScrollHeight: 600, // 列表滚动高度，设置默认值
    // 高度计算相关
    isHeightCalculated: false // 是否已计算过高度
  },
  onShow() {
    const {login} = app.globalData;
    this.setData(
      {
        isLogin: login
      },
      () => {
        this.calculateAllHeights();
      }
    );
  },

  onLoad(options) {
    this.setData({
      pageOptions: options
    });
  },

  onReady() {
    // 页面渲染完成后一次性计算所有高度
    this.calculateAllHeights();
  },

  // 点击搜索框跳转到搜索页面
  navigateToSearch() {
    // 构建跳转URL，传递当前页面的参数
    let url = '/childSubpackage/pages/ResearchReportList/index';
    const params = [];
    const pageOptions = this.data.pageOptions;
    // 传递原始页面参数
    if (pageOptions) {
      Object.keys(pageOptions).forEach(key => {
        if (pageOptions[key]) {
          params.push(`${key}=${encodeURIComponent(pageOptions[key])}`);
        }
      });
    }
    if (params.length > 0) {
      url += '?' + params.join('&');
    }
    wx.navigateTo({
      url: url
    });
  },

  // 一次性计算所有高度
  calculateAllHeights() {
    getHeight(this, ['.searchs'], data => {
      const {screeHeight, res} = data;

      // 搜索框高度
      const barH = res[0]?.height || 0;

      // 计算列表区域高度
      const listScrollHeight = screeHeight - barH;
      this.setData({
        listScrollHeight: listScrollHeight,
        isHeightCalculated: true
      });
    });
  },

  // 列表数据变化回调
  onListDataChange(e) {
    const {list, total} = e.detail;
    // 转换API数据格式为ReportCard组件期望的格式
    const transformedList = this.transformReportData(list);
    this.setData({
      reportList: transformedList,
      company_num: total
    });
  },

  // 转换API数据格式
  transformReportData(apiList) {
    if (!Array.isArray(apiList)) {
      return [];
    }

    return apiList.map(item => {
      // 处理产业链标签
      const tags = [];
      if (item.chains && Array.isArray(item.chains)) {
        // 取前2个产业链作为标签
        tags.push(...item.chains.slice(0, 2).map(chain => chain.name));
      }
      return {
        id: item.id,
        title: item.report_name || '--',
        size: item.file_size || '-',
        tags: tags,
        organization: item.publish_org,
        date: this.formatDate(item.publish_time),
        pdfUrl: item.report_oss_url || '',
        imgTit: item.report_type === 'REPORT_TYPE_1' ? '撼地智库' : '产业专题',
        page_num: item.page_num,
        // 保留原始数据以备后用
        originalData: item
      };
    });
  },

  // 格式化日期
  formatDate(dateString) {
    if (!dateString) return '未知日期';
    try {
      const date = new Date(dateString);
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    } catch (error) {
      return dateString; // 如果格式化失败，返回原始字符串
    }
  },

  // 点击研报事件
  onReportClick(e) {
    const {item} = e.detail;
    // 显示操作选择弹窗
    this.showReportActionSheet(item);
  },

  // 添加研报到浏览历史
  addReportToHistory(reportItem) {
    try {
      // 发送浏览历史到服务器
      addBevHis({
        enterprise_name: reportItem.title,
        enterprise_id: reportItem.id, // 如果没有id，使用title
        model_type: 'RESEARCH_REPORT_SEARCH',
        extra_param: JSON.stringify(reportItem.originalData)
      });
    } catch (error) {
      console.error('添加浏览历史失败:', error);
    }
  },

  // 显示研报操作选择
  showReportActionSheet(reportItem) {
    const itemList = ['在线预览', '下载到本地'];
    const that = this;
    console.log(222, reportItem);
    wx.showActionSheet({
      itemList,
      success: res => {
        switch (res.tapIndex) {
          case 0:
            this.previewReport(reportItem);
            // 添加到浏览历史
            that.addReportToHistory(reportItem);
            break;
          case 1:
            this.downloadReport(reportItem);
            // 添加到浏览历史
            that.addReportToHistory(reportItem);
            break;
        }
      },
      fail: () => {
        console.log('用户取消操作');
      }
    });
  },

  // 在线预览研报
  previewReport(reportItem) {
    wx.showLoading({
      title: '正在加载...'
    });

    // 获取PDF URL
    const pdfUrl =
      reportItem.pdfUrl ||
      'https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf';

    // 使用微信内置PDF预览
    wx.downloadFile({
      url: pdfUrl,
      success: res => {
        wx.hideLoading();
        if (res.statusCode === 200) {
          wx.openDocument({
            filePath: res.tempFilePath,
            fileType: 'pdf',
            success: () => {
              console.log('PDF预览成功');
            },
            fail: error => {
              console.error('PDF预览失败:', error);
              wx.showToast({
                title: '预览失败，请稍后重试',
                icon: 'none'
              });
            }
          });
        }
      },
      fail: error => {
        wx.hideLoading();
        console.error('下载PDF失败:', error);
        wx.showToast({
          title: '加载失败，请检查网络',
          icon: 'none'
        });
      }
    });
  },

  // 下载研报到本地
  downloadReport(reportItem) {
    wx.showLoading({
      title: '正在下载...'
    });

    // 获取PDF URL
    const pdfUrl =
      reportItem.pdfUrl ||
      'https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf';

    wx.downloadFile({
      url: pdfUrl,
      success: res => {
        wx.hideLoading();
        if (res.statusCode === 200) {
          // 保存到相册或文件管理器
          wx.saveFile({
            tempFilePath: res.tempFilePath,
            success: saveRes => {
              wx.showToast({
                title: '下载成功',
                icon: 'success'
              });
              console.log('文件保存路径:', saveRes.savedFilePath);
            },
            fail: error => {
              console.error('保存文件失败:', error);
              wx.showToast({
                title: '保存失败',
                icon: 'none'
              });
            }
          });
        }
      },
      fail: error => {
        wx.hideLoading();
        console.error('下载失败:', error);
        wx.showToast({
          title: '下载失败，请稍后重试',
          icon: 'none'
        });
      }
    });
  }
});
