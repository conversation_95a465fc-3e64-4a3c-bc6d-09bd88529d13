@import '../../../../template/null/null.scss';

.chain {
  width: 100%;
  height: 100vh;
}

.china-nav {
  position: relative;
  border-bottom: 20rpx solid #f2f2f2;
  height: 116rpx !important;

}

.china-nav::after {
  content: " ";
  width: 200%;
  height: 1rpx;
  background: #f2f2f2;
  position: absolute;
  top: 0;
  left: 0;
  transform: scaleY(0.5);
}

.chain-wrap {
  padding: 12rpx 32rpx 34rpx;
}

.chain-wrap-box {
  position: relative;
  padding: 28rpx 28rpx 40rpx;
  margin-top: 20rpx;
  /* background: linear-gradient(180deg, rgba(74, 184, 255, 0.05) 0%, #FFFFFF 57%); */
  /* opacity: 0.1; */
  /* border: 1px solid rgba(3.5, 55, 114, 0.1); */
  min-height: 356rpx;
}

.chain-wrap-box .img {
  position: absolute;
  right: 0;
  top: 0;
  width: 244rpx;
  height: 356rpx;
}

.chain-wrap-box .img1 {
  position: absolute;
  width: 100%;
  height: 100%;
  right: 0;
  top: 0;
}

.chain-wrap-t {
  display: flex;
  align-items: center;
}

.chain-wrap-t>image {
  width: 52rpx;
  height: 52rpx;
  margin-right: 20rpx;
}

.chain-wrap-t>text {
  text-align: left;
  font-size: 32rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #20263A;
}

.chain-wrap-cont {
  position: relative;
  display: flex;
  flex-wrap: wrap;
  padding-top: 6rpx;
  z-index: 10;
}

.chain-wrap-cont>view {
  background: #FFFFFF;
  border-radius: 4rpx 4rpx 4rpx 4rpx;
  border: 2rpx solid #DFE7F5;

  font-size: 28rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #525665;
  padding: 20rpx 32rpx 20rpx 32rpx;
  margin-top: 20rpx;
  margin-right: 20rpx;
}

.chain-wrap-cont>.active {
  color: #20263A;
  background: #EEEEEE;
  /* border: none; */
}