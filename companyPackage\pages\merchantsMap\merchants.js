import {
  setTagColor,
  preventActive,
  getSetting
} from '../../../utils/util';
import {
  map
} from '../../../service/api';
import {
  collect
} from '../../../utils/mixin/collect';
import {
  getHeight
} from '../../../utils/height';
var mapMixin = require('./mapMixin.js');
const app = getApp();

// 默认位置常量（北京）
const DEFAULT_LOCATION = {
  latitude: 39.9042,
  longitude: 116.4074,
  locationName: '北京市',
  adcode: '110100'
};
const DISMAP = {
  1: 14,
  2: 13,
  3: 12,
  4: 12,
  5: 12,
  6: 11,
  7: 11,
  8: 11,
  9: 11,
  10: 11
};
Page({
  behaviors: [mapMixin()],
  data: {
    scrollHeight: 'auto',
    statusBarHeight: 'auto',
    y: '668rpx',
    params: {
      shape: 'CIRCLE',
      radius: 5000,
      page_index: 1,
      page_size: 25
    },
    searchVal: '',
    count: 0,
    chainScroll: false,
    startY: 0,
    hideClass: false,
    hasData: true, // 判断接口是否还有数据返回
    requestData: [],
    // 地图相关 - 高德地图key
    key: 'ea478181d97e3d92be0150c05603ddb6',
    scale: 12, //8-16 
    latitude: DEFAULT_LOCATION.latitude,
    longitude: DEFAULT_LOCATION.longitude,
    markers: [],
    circles: [],
    isLocation: true, //是否展示该页面
    // 默认中心点设置为北京
    currentCenter: {
      latitude: DEFAULT_LOCATION.latitude,
      longitude: DEFAULT_LOCATION.longitude,
      locationName: DEFAULT_LOCATION.locationName,
      adcode: DEFAULT_LOCATION.adcode
    },

    // 弹窗相关
    popType: '',
    showVisible: false, //是否显示弹窗
    title: '',
    content: '',
    cancelBtnText: '取消',
    confirmBtnText: '删除',
    cnacelEnt: '', //缓存取消收藏企业id
    popIndex: 0, //缓存取消收藏企业下标
    // 联系方式
    showContact: false,
    contactList: [], //联系方式列表
    activeEntId: '', // 当前点击企业的id
    // 地址
    showAddress: false,
    addmarkers: [],
    locationTxt: '',
    locationMap: {},
    location: {
      lat: '',
      lon: ''
    },
    isAn: false,
    isLogin: app.isLogin(),
    emptyBoxHeight: '', // 空容器高度
    // 授权相关状态
    authLoading: false,
    authStatus: 'not_determined' // 'granted', 'denied', 'not_determined', 'error'
  },
  onLoad: function () {
    this.getEmptyBoxHeight();
    const info = wx.getSystemInfoSync();
    if (info.platform === 'android') {
      this.setData({
        isAn: true
      });
    }
  },
  onShow: function () {
    this.setData({
        isLogin: app.isLogin()
      },
      () => {
        // 1.初始化
        this._initializeMap();
        // 2.检查授权状态并处理
        this.checkAuthAndInitialize();
      }
    );
  },

  // 检查授权状态并初始化
  checkAuthAndInitialize() {
    // 检查当前授权状态
    wx.getSetting({
      success: res => {
        const locationAuth = res.authSetting['scope.userLocation'];
        const wasAuthorized = this.data.isLocation;

        if (locationAuth === true) {
          // 已授权
          if (!wasAuthorized) {
            // 之前未授权，现在已授权，需要重新初始化
            this.setData({
              isLocation: true,
              authStatus: 'granted'
            });
            this._initializeLocation();
          } else {
            // 之前就已授权，正常初始化
            this.handleAuth();
          }
        } else {
          // 未授权或拒绝授权
          this.handleAuth();
        }
      },
      fail: () => {
        // 获取设置失败，使用原有逻辑
        this.handleAuth();
      }
    });
  },
  handleAuth() {
    // 先设置加载状态
    this.setData({
      authLoading: true
    });

    getSetting((success, authStatus) => {
      if (!success) {
        // 授权失败时，设置默认北京位置
        this.setData({
          isLocation: false,
          authLoading: false,
          authStatus: authStatus,
          currentCenter: {
            latitude: DEFAULT_LOCATION.latitude,
            longitude: DEFAULT_LOCATION.longitude,
            locationName: DEFAULT_LOCATION.locationName,
            adcode: DEFAULT_LOCATION.adcode
          },
          latitude: DEFAULT_LOCATION.latitude,
          longitude: DEFAULT_LOCATION.longitude,
          'params.grids': [{
            lon: DEFAULT_LOCATION.longitude,
            lat: DEFAULT_LOCATION.latitude
          }]
        });
        // 设置默认圆圈
        this.updateCircle(
          DEFAULT_LOCATION.latitude,
          DEFAULT_LOCATION.longitude
        );

        // 安卓设备额外强制设置
        if (this.data.isAn) {
          setTimeout(() => {
            this.forceRefreshMapForAndroid();
          }, 300);
        }
        return;
      }

      // 授权成功
      this.setData({
        isLocation: true,
        authLoading: false,
        authStatus: authStatus
      });
      this._initializeLocation();
    });
  },
  goMap() {
    const {
      locationMap
    } = this.data;
    wx.openLocation(locationMap);
  },
  onCloseAddress() {
    this.setData({
      showAddress: false
    });
  },
  // 动态获取页面高度
  scrollH() {
    var that = this;
    wx.getSystemInfo({
      success: res => {
        let statusBarHeight = wx.getSystemInfoSync().statusBarHeight;
        let screeHeight = wx.getSystemInfoSync().windowHeight;
        //   通过query获取其余盒子的高度
        let query = wx.createSelectorQuery().in(that);
        query.select('.merch').boundingClientRect();
        query.select('.merch-menu').boundingClientRect();
        // 通过query.exec返回数组
        query.exec(res => {
          let h1 = res[0].height;
          let h2 = res[1].height;
          // 浏览历史的滚动高度
          let scrollHeight = screeHeight - h1 - h2;
          that.setData({
            scrollHeight: scrollHeight,
            statusBarHeight: statusBarHeight
          });
        });
      }
    });
  },
  searchContent(e) {
    const keyWord = e.detail.searchVal;
    this.setData({
        searchVal: keyWord,
        hasData: true,
        'params.ent_name': keyWord,
        y: keyWord ? '0' : this.data.y,
        hideClass: true,
        'params.page_index': 1,
        requestData: []
      },
      () => {
        this.getHot()
        this.getList();
      }
    );
  },
  async getList(bl = true) {
    wx.showLoading({
      title: '加载中...'
    })
    let {
      hasData,
      requestData,
      searchVal
    } = this.data;
    const params = JSON.parse(JSON.stringify(this.data.params));
    hasData = true;
    // 处理参数 这里参数后续要确定一下 todo
    const newParams = {
      ...params,
      points: params.grids,
      distance: params.radius + '',
      current_page: params.page_index,
      geo_shape: 1
    };
    delete newParams.grids;
    delete newParams.radius;
    delete newParams.page_index;
    delete newParams.shape;

    let res = await map.mapList(newParams);
    const temAry = res.list.map(item => {
      item.tags = setTagColor(item.tags);
      item.distance = this.distance(item.location.lat, item.location.lon);
      return item;
    });
    temAry.forEach(item => {
      item.chainArr = this.arrfiy(searchVal, item.ent_name);
      item.tags = item.tags.splice(0, 3);
    });
    if (temAry.length < params.page_size || res.total == params.page_size)
      hasData = false;
    // console.log("企业列表结果", res);
    const reqParams = {
      count: res.total,
      searchVal,
      hasData,
      requestData: bl ? requestData.concat(temAry) : temAry
    };
    if (params.page_index == 1 && bl !== 'noScall') {
      const map = DISMAP;
      reqParams['scale'] = map[newParams.distance / 1000];
      reqParams['temScale'] = '';
    }
    this.setData(reqParams, () => {
      this.getHot();
    });
    wx.hideLoading();
  },
  arrfiy(key, chain) {
    return chain.replace(new RegExp(`${key}`, 'g'), `%%${key}%%`).split('%%');
  },
  Rad: function (d) {
    //根据经纬度判断距离
    return (d * Math.PI) / 180.0;
  },
  distance: function (lat2, lng2) {
    let {
      latitude: lat1,
      longitude: lng1
    } = this.data;
    var radLat1 = this.Rad(lat1);
    var radLat2 = this.Rad(lat2);
    var a = radLat1 - radLat2;
    var b = this.Rad(lng1) - this.Rad(lng2);
    var s =
      2 *
      Math.asin(
        Math.sqrt(
          Math.pow(Math.sin(a / 2), 2) +
          Math.cos(radLat1) * Math.cos(radLat2) * Math.pow(Math.sin(b / 2), 2)
        )
      );
    s = s * 6378.137;
    s = Math.round(s * 10000) / 10000;
    return s;
  },
  // 开始滑动
  touchstart(e) {
    let {
      chainScroll
    } = this.data;
    this.setData({
      // mainHeight:"calc( 100vh - 622rpx)",
      chainScroll: false
    });
    // console.log("滑动开始", e.changedTouches[0])
    this.setData({
      startY: e.changedTouches[0].pageY
    });
  },
  // 结束滑动
  touchend(e) {
    let {
      startY,
      chainScroll,
      y
    } = this.data;
    if (chainScroll) {
      // console.log("产业链内滚动触发，外层无动作");
      this.setData({
        y
      });
    } else {
      // console.log("滑动结束", e.changedTouches[0])
      let endY = e.changedTouches[0].pageY;
      console.log('endY < startY', endY, startY);
      if (endY < startY) {
        this.setData({
          y: '0',
          hideClass: true,
          emptyBoxHeight: '100%'
        });
      } else if (endY > startY) {
        this.setData({
          y: '668rpx',
          hideClass: false
        });
        this.getEmptyBoxHeight();
      }
    }
  },
  // 获取数据为空时空容器的高度
  getEmptyBoxHeight() {
    getHeight(
      this,
      ['.merch', '.br_line', '.merch-menu', '.move_box'],
      data => {
        const {
          screeHeight,
          res
        } = data;
        const h1 = res[0]?.height || 0;
        const h2 = res[1]?.height || 0;
        const h3 = res[2]?.height || 0;
        const h4 = res[3]?.height || 0;
        this.setData({
          emptyBoxHeight: screeHeight - h1 - h2 - h3 - h4 + 'px'
        });
      }
    );
  },
  // 产业链滚动
  bindscroll() {
    // console.log("传递事件-产业链");
    this.setData({
      chainScroll: true
    });
  },
  loadMore() {
    // console.log("触发loadMore");
    let {
      params,
      hasData
    } = this.data;
    if (!hasData) {
      wx.showToast({
        title: '已经到底啦',
        icon: 'error'
      });
      return;
    }
    params.page_index += 1;
    this.setData({
      params
    });
    wx.showLoading({
      title: '加载中...'
    });
    this.getList();
  },
  // 跳转到地图搜索页面
  goSearch() {
    app.route(this, '/companyPackage/pages/mapSearchList/index');
    // 弹窗回调
  },
  // 弹窗-----------回调
  onIndustryChange(e) {
    let {
      params
    } = this.data;
    params.industry_code_list = e.detail.industry_code_list;
    params.page_index = 1;
    this.setData({
        hasData: true,
        'params.page_index': 1,
        y: '0',
        hideClass: true,
        requestData: []
      },
      () => {
        this.getHot();
        this.getList(false);
      }
    );
  },
  onSliderChange(e) {
    // console.log("父页面滑块改变", e);
    const val = e.detail.slider;
    // 圆变大的时候 -- 地图变小一点
    let scall = this.data.scale;
    const map = DISMAP;
    scall = map[val];

    const newRadius = val * 1000;
    console.log('onSliderChange: 滑块改变', {
      val,
      newRadius,
      scall
    });

    // 安全地更新圆圈，不直接设置 circles[0].radius
    const updateData = {
      scale: scall,
      'params.radius': newRadius,
      'params.page_index': 1,
      y: '668rpx',
      hideClass: false,
      hasData: true,
      requestData: []
    };

    // 如果当前有圆圈，重新创建整个圆圈数组
    if (this.data.circles && this.data.circles.length > 0) {
      const currentCircle = this.data.circles[0];
      updateData.circles = [{
        latitude: parseFloat(currentCircle.latitude),
        longitude: parseFloat(currentCircle.longitude),
        color: '#00000000',
        fillColor: '#E7241024',
        radius: newRadius,
        strokeWidth: 1.8
      }];
    }

    this.setData(updateData, () => {
      this.getList(false);
    });
  },
  onSearchChange(e) {
    // 更新圆圈
    let {
      params
    } = this.data;
    let paramsNew = e.detail;
    // console.log('onSearchChange', e, params);
    // 这里不能在用Object.assign 需要将params里面的参数拿出来 因为paramsNew的关系
    let {
      grids = [],
        page_index,
        page_size,
        radius,
        shape,
        industry_code_list = [],
        ent_name = ''
    } = params;
    params = {
      ...paramsNew,
      grids,
      page_index,
      page_size,
      radius,
      shape,
      industry_code_list,
      ent_name
    };
    params.page_index = 1;
    this.setData({
        params,
        y: '0',
        hideClass: true,
        requestData: []
      },
      () => {
        this.getHot();
        this.getList(false);
      }
    );
  },

  // 卡片点击回调
  async onCard(data) {
    let that = this;
    preventActive(this, async () => {
      // 地址  "site"  联系方式 "relation" 官网 "official" 收藏 "collect" 四个字段对呀四个方法逻辑-具体见设计图
      const type = data.detail.type;
      let comDetail = data.detail.data;
      // 处理收藏
      if (type == 'collect') {
        comDetail.tags = comDetail.tags.map(tag => tag.tagName);
        collect(that, comDetail, 'requestData');
      } else if (type == 'relation') {
        this.setData({
          activeEntId: comDetail.ent_id,
          showContact: true
        });
      } else if (type === 'site') {
        this.setData({
          location: {
            lat: +comDetail.location.lat,
            lon: +comDetail.location.lon
          },
          locationTxt: comDetail.register_address,
          addmarkers: [{
            id: 1,
            latitude: +comDetail.location.lat,
            longitude: +comDetail.location.lon,
            iconPath: 'https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/marker.png',
            width: 20,
            height: 20
          }],
          showAddress: true,
          locationMap: {
            latitude: +comDetail.location.lat, //维度
            longitude: +comDetail.location.lon, //经度
            name: comDetail.register_address, //目的地定位名称
            // scale: 15, //缩放比例
            address: comDetail.register_address //导航详细地址
          }
        });
      }
    });
  },
  onCloseContact() {
    this.setData({
      showContact: false
    });
  },
  makeCall(e) {
    const item = e.target.dataset['item'] || e.currentTarget.dataset['item'];
    wx.makePhoneCall({
      phoneNumber: item.contact_data
    });
  },
  vipPop(val) {
    this.setData({
      vipVisible: val
    });
  }
});