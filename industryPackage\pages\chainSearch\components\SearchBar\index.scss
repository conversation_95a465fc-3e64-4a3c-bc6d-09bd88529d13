// 搜索容器
.search-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
  background: #fff;
  height: 112rpx;
  padding: 20rpx 24rpx;
  z-index: 20;

  // 搜索输入框区域
  .search-input-wrapper {
    display: flex;
    align-items: center;
    width: 100%;
    height: 72rpx;
    background: #f4f4f4;
    border-radius: 8rpx;
    box-sizing: border-box;
    margin-right: 24rpx;
    padding-left: 24rpx;

    .search-icon {
      min-width: 40rpx;
      height: 40rpx;
      margin-right: 20rpx;
      flex-shrink: 0;

      image {
        width: 100%;
        height: 100%;
      }
    }

    .search-input {
      flex: 1;
      height: 40rpx;
      color: #20263a;
      font-size: 28rpx;
      font-family:
        PingFang SC,
        PingFang SC-Regular;
      font-weight: 400;
      border: none;
      outline: none;
      background: transparent;
    }

    .placeholder {
      // font-size: 26rpx;
      font-size: 24rpx;
      font-family:
        PingFang SC,
        PingFang SC-Regular;
      font-weight: 400;
      color: #9b9eac;
    }
  }

  // 筛选按钮 - 参考 hIndusty 样式
  .filter {
    flex-shrink: 0;
    font-weight: 400;
    font-size: 28rpx;
    color: #20263a;
    padding-right: 24rpx;
    background: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAYAAACNiR0NAAAAAXNSR0IArs4c6QAAAO5JREFUOE9jZKAyYKSyeQyjBlIeoihhuGDp7s0MDP99SDP2/5aEaHdfmB40A3fuZmBgdCHRwD0J0e6uWA2cvWSbDCsD83EGRkYZ4gz9/+T3/7+WqTFeT7AaCBJcsGSXNQMDwx4GRgYOvIb+Z/jBwMDgkhDjdhRZHdZ0uHDZrvT//xlm4DOQkZEhIz7KbSa6GpwJe8GSXdMZGBkycBg6IyHaLRObHE4D58+fz8HIJr0b6H0bNI1H/v966pqYmAjyMgbAm/UwIwkzEoj2MkwhPJIgAhiRQLKBIA2Llu80B9Fxke4nCSWn0dKGUAgRlgcAnPdGFbNmkawAAAAASUVORK5CYII=")
      no-repeat right center;
    background-size: 20rpx 20rpx;

    transition: all 0.3s ease;

    &.active {
      background: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABUAAAAUCAYAAABiS3YzAAAAAXNSR0IArs4c6QAAAShJREFUOE9jZKABYKSBmQzD0NBXWqISf3/+dp9598PiBgaGf/iCjSjvX9ViYBP+JfDyPwODwH9Ghhap2x9qKTL0vzED64sPAkcZGBlMYQYxMjL6Stx+vwWXwXhd+p+BgemFisBEBgaGHHQD/jH/1ZC++fkmNoPxGvpSWSDuHyPDQmwa/zMwvP3967+q/KOP79HlcRr6XI3PjOEf00l8YfefgeHUV4YPdqp3GH4iq8Nq6FsVIZlfDP+uMTAw8BLKHP8ZGGdL3XmfhtfQ+woCApwsDEf+MzBoEzIQJv+fkTFT6vb7GfCIRNf4QkVg3n8GhkRiDYSp+/OXUV/2/vtLID6G95+pCM5iZPifSpKhjAxf//xhtMJpKEmG4VBMVI4i1aJRQ0kNMcLqAbAOVBVGyXfJAAAAAElFTkSuQmCC")
        no-repeat right center;
      background-size: 20rpx 20rpx;
      color: #e72410;
    }
  }
}

// 下拉遮罩层
.dropdown-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 11;
  background: rgba(0, 0, 0, 0.3);

  // 下拉筛选面板
  .filter-dropdown {
    position: absolute;
    top: 110rpx;
    left: 0;
    right: 0;
    background: #ffffff;
    border-bottom-left-radius: 16rpx;
    border-bottom-right-radius: 16rpx;
    box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
    overflow: hidden;
    animation: slideDown 0.3s ease-out;

    // 筛选列表 - 参考 hIndusty 样式
    .filter_list {
      max-height: 600rpx;
      padding: 0 32rpx 0 24rpx;
      font-size: 28rpx;
      color: #20263a;

      .filter_item {
        margin-top: 32rpx;

        transition: all 0.2s ease;

        &:last-child {
          margin-bottom: 32rpx;
        }

        &:hover {
          color: #e72410;
        }

        &.active {
          background: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAAAR5JREFUWEftlj0OgkAQRmekoVHslM4ohdcwFhY23kcTLbT0LrZWXsArkFD506mVzTpmNSQkrDALu1oINeS9+WD2A+HHF/6YD5VAlcD/JHDo1PvoOGM/vKyTm/eVBN7w2g4Q20g0a4fXVSxhXSAJJ6K7I2jSim7brwjkwaWEtQQ4cGsCXLgVAR24cQFduFLg1PWG9MDQjy6RTlMWgacEzkFjJAA3SHgCAQOuRFF4SuDQ86aIuHxNThBxJMrAla/gGDTnALDgSJSFf/wIORIm4JlbkCVhCp67hioJAuHGxaI623U2J1dA3pCSAHJlq5mAswRSEnJBFK2mO7l2G8ZJmISzE4ht5TnhCNon+7zo5NoJlAV9et7a/wBXuBKoEngCQAv7IS1L+uMAAAAASUVORK5CYII=")
            no-repeat right center;
          background-size: 32rpx 32rpx;
          font-weight: 600;
          color: #e72410;
        }
      }
    }
  }
}

// 下拉动画
@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
