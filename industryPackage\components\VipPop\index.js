import {applyOpenOriginalIndustryApi} from '../../../service/industryApi';
Component({
  properties: {
    // 控制弹窗显示/隐藏
    visible: {
      type: Boolean,
      value: false
    },
    // 是否点击遮罩关闭
    maskClosable: {
      type: Boolean,
      value: true
    },

    // z-index层级
    zIndex: {
      type: Number,
      value: 1000
    },
    chain_code: {
      type: String,
      value: ''
    },
    chain_name: {
      type: String,
      value: ''
    }
  },

  data: {},

  methods: {
    onMaskTap() {
      if (this.properties.maskClosable) {
        this.triggerEvent('close', {
          type: 'mask'
        });
      }
    },

    /**
     * 按钮点击事件
     */
    async onButtonTap() {
      const {chain_code, chain_name} = this.properties;
      const res = await applyOpenOriginalIndustryApi({
        chain_name,
        chain_code,
        apply_source: '小程序'
      });
      this.triggerEvent('send');
    },

    /**
     * 阻止事件冒泡
     */
    preventBubble() {
      // 阻止事件冒泡到遮罩层
    },

    /**
     * 阻止触摸移动
     */
    preventTouchMove() {
      // 阻止背景滚动
      return false;
    }
  }
});
