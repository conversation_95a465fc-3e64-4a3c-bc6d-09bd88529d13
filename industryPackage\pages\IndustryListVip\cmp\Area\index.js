import {
  regionChainEntRankApi, // 排名
  appChainRegionEntDataAnalysisApi //地图数据
} from '../../../../../service/industryApi';

const app = getApp();
Component({
  options: {
    multipleSlots: true
  },

  properties: {
    // 默认地图类型
    defaultMapType: {
      type: String,
      value: 'city'
    },
    // 是否显示切换按钮
    showTypeSwitch: {
      type: Boolean,
      value: true
    },
    // 表格最大显示行数
    maxTableRows: {
      type: Number,
      value: 10
    },
    industryType: {
      type: String,
      value: 'chainMap' // 热点hot 经典classic 产业图谱chainMap
    },
    chianName: {
      type: String,
      value: ''
    },
    chianCode: {
      type: String,
      value: '',
      observer: 'initComponent'
    }
  },

  data: {
    // 当前地图类型
    mapActive: 'city',
    // 当前显示的地区数据
    currentAreaList: [],
    // 所有地区数据
    allAreaData: [],
    // 地图区域配置
    region: {
      code: '1000000',
      name: 'china',
      level: '1'
    },
    provinceData: [],
    cityData: [],
    provinceMapData: [], // 省份地图数据
    cityMapData: [], // 城市地图数据
    mapData: [] // 当前显示的地图数据
  },

  lifetimes: {
    attached() {}
  },

  methods: {
    // 初始化组件
    initComponent() {
      const {defaultMapType, chianCode} = this.properties;
      if (!chianCode) return;
      this.setData(
        {
          mapActive: defaultMapType
        },
        () => {
          // 根据类型填充数据
          this.getInit();
        }
      );
    },
    // 获取排行数据和地图数据
    async getInit() {
      const {chianCode, industryType} = this.properties;
      const chain_type =
        industryType === 'classic' ? 3 : industryType === 'chainMap' ? 1 : 2; //产业链类型 1产业链图谱2热点产业3经典产业
      try {
        const [provinceRankData, cityRankData, provinceMapData] =
          await Promise.all([
            regionChainEntRankApi({
              chain_code: chianCode,
              rank_type: 1, // 这里固定死   1 省份排名  2 城市排名
              top: 8,
              chain_type
            }),
            regionChainEntRankApi({
              chain_code: chianCode,
              rank_type: 2,
              top: 8,
              chain_type
            }),
            // 这里切换没有区县 1省份 2城市 3区县
            appChainRegionEntDataAnalysisApi({
              chain_code: chianCode,
              chain_type,
              region_type: 1
            })
            // 这里是全国地图 按照城市返回的数据渲染不了 所以这里切换也是用的省
            // appChainRegionEntDataAnalysisApi({
            //   chain_code: chianCode,
            //   chain_type,
            //   region_type: 1
            // })
          ]);

        // 格式化数据
        const formattedData = {
          provinceData: this._formatRegionData(provinceRankData, 'province'),
          cityData: this._formatRegionData(cityRankData, 'city'),
          provinceMapData: this._formatMapData(provinceMapData),
          // cityMapData: this._formatMapData(cityMapData)
          cityMapData: this._formatMapData(provinceMapData)
        };

        this.setData(formattedData, () => {
          this.processAreaData();
        });
      } catch (error) {
        console.error('获取区域数据失败:', error);
        // 设置默认空数据
        this.setData({
          provinceData: [],
          cityData: [],
          provinceMapData: [],
          cityMapData: []
        });
      }
    },
    // 处理地区数据
    processAreaData() {
      const {mapActive, provinceData, cityData, provinceMapData, cityMapData} =
        this.data;

      this.setData({
        currentAreaList: mapActive === 'province' ? provinceData : cityData,
        mapData: mapActive === 'province' ? provinceMapData : cityMapData
      });
    },
    // 切换地图类型
    changeMapActive(e) {
      const {type} = e.currentTarget.dataset;
      if (type === this.data.mapActive) return;
      const {industryType} = this.properties;
      if (industryType === 'chainMap') {
        this.triggerEvent('mapTypeChange', {
          type,
          fn: () => {
            // 筛选数据-外面通过了在切换
            this.setData(
              {
                mapActive: type
              },
              () => {
                this.processAreaData();
              }
            );
          }
        });
        return;
      }

      this.setData(
        {
          mapActive: type
        },
        () => {
          this.processAreaData();
        }
      );
    },

    // 地图点击事件
    onMapClick(e) {
      // this.triggerEvent('mapClick', {
      //   ...e.detail,
      //   currentType: this.data.mapActive,
      //   currentData: this.data.currentAreaList
      // });
    },

    // 表格行点击事件
    onTableRowClick(e) {
      // this.triggerEvent('rowClick', {
      //   ...e.detail,
      //   mapType: this.data.mapActive
      // });
    },

    // 点击地图标题 跳转到地图页面
    goMap() {
      let url = `/industryPackage/pages/businessMapList/index?category=${
        this.properties.industryType
      }&chain_code=${this.properties.chianCode}&chain_name=${encodeURIComponent(
        this.properties.chianName
      )}`;
      if (this.properties.industryType === 'chainMap') {
        url = url + `&isIndustryMap=true`;
      }
      console.log(url);

      app.route(this, url);
    },
    /**
     * 格式化区域数据
     * @param {Array} data - 区域数据
     * @param {string} regionType - 区域类型 ('province' | 'city')
     * @returns {Array} 格式化后的区域数据
     */
    _formatRegionData(data, regionType) {
      if (!Array.isArray(data)) {
        return [];
      }

      return data.map(item => ({
        name: item.region_name,
        value: item.total,
        region: regionType,
        ...item
      }));
    },

    /**
     * 格式化地图数据
     * @param {Object} data - 地图数据
     * @returns {Array} 格式化后的地图数据
     */
    _formatMapData(data) {
      if (!data || !Array.isArray(data.data_list)) {
        return [];
      }

      return data.data_list.map(item => ({
        name: item.region_name,
        value: item.count
      }));
    }
  }
});
