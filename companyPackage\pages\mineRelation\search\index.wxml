<import src="/template/null/null"></import>
<import src="/template/more/more"></import>
<view class="pages">
  <!-- input -->
  <view class="searchs">
    <view class="s-input">
      <view class="s-input-img">
        <image src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/search.png" mode="aspectFit"></image>
      </view>
      <view class="s-input-item">
        <input class="s-input-item-i" type="text" placeholder="请输入企业名称关键词" placeholder-class="placeholder" value="{{ents_name}}" focus="{{inputShowed}}" bindinput="onInput" confirm-type="search" />
        <view hidden="{{ent_name.length <= 0}}" catchtap="onClear" class="input-clear">
          <view class="clearIcon"></view>
        </view>
      </view>
    </view>
    <view class="search-cancel" bindtap="goBack" bindtap="goBack">取消</view>
  </view>
  <view>
    <!-- 筛选条件 -->
    <DropDownMenu height="{{filtrateHeight}}" dropDownMenuTitle="{{dropDownMenuTitle}}" excludeFields="{{['area_code_list', 'industry_code_list','ent_name']}}" class="drop-menu" bindsubmit="onFlitter" bindvip="vipPop" />
    <block>
      <!-- 卡片容器 -->
      <view wx:if="{{!bazaarIsNull}}">
        <scroll-view bindrefresherrefresh="bazaarRefresher" refresher-triggered="{{bazaarIsTriggered}}" refresher-enabled bindscrolltolower="bazaarloadMore" scroll-y style="height: {{cardHeight}}px;">
          <view class="bus-card">
            <!--  -->
            <block wx:for="{{bazaarlist}}" wx:key="ent_id">
              <view class="zhanwei" wx:if="{{index==0}}"></view>
              <!-- 卡片内容 -->
              <view class="rcard">
                <view class="head">
                  <view class="head-l">
                    <image src="{{item.ent_logo || 'https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/quesen.png'}}" mode="aspectFit">
                    </image>
                    <!-- <view class="title">{{item.ent_name}}</view> -->
                    <view class="title">
                      <text class="{{ v.type === 'HIGH'?'text-high':'' }}" wx:for="{{item.label}}" wx:key="item.label" wx:for-item="v">{{ v.text }}</text>
                    </view>
                  </view>
                  <view class="head-r" bindtap="onsure" data-item="{{item}}">
                    <image src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/add_1.png"></image>
                    <text>选择</text>
                  </view>
                </view>
                <!-- TODO 董监高的数量需每个调用接口实现 暂且是静态数据 -->
                <view class="cont" bindtap="getPerson" data-item="{{item}}">
                  <text>选择董监高</text>
                  <image src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/bussiness/arrow-r.png" mode="aspectFit" />
                </view>
              </view>
            </block>
            <view wx:if="{{bazaarlist.length>=bazaarParms.page_size}}" style="width: 100%;">
              <template is="more" data="{{hasData:bazaarHasData}}"></template>
            </view>
          </view>
          <view class="vip" wx:if="{{!isLogin && bazaarlist.length >= 5}}">
            <vloginOverlay bindsubmit="login"></vloginOverlay>
          </view>
        </scroll-view>
      </view>
      <!--暂无数据 -->
      <view wx:if="{{bazaarIsNull}}" style="width: 100%;height: 600rpx;">
        <template is="null"></template>
      </view>
    </block>
  </view>
  <!-- 底部弹窗 -->
  <half-screen-pop position="bottom" visible="{{isShowPop}}" showFooter="{{false}}" zIndex="{{99}}">
    <view slot="customhead">
      <view class="pop-header">
        <view class="pop-cancel" bindtap="getItem"> 取消 </view>
        <view class="pop-name">选择董监高</view>
        <view class="pop-ok" bindtap="getItemOK"> 确定 </view>
      </view>
    </view>
    <view class="con" slot="customContent">
      <!-- 这里面最大高度400rpx也就是最大能放四个,多出四个就可以滚动 -->
      <scroll-view class="list" scroll-with-animation enable-back-to-top scroll-y>
        <!-- active -->
        <block wx:for="{{popList}}" wx:key="index">
          <view class="list-item {{ activeIdx==index ? 'active':''}}" bindtap="getItem" data-item="{{item}}" data-index="{{index}}">
            {{item.name}}
          </view>
        </block>
      </scroll-view>
    </view>
  </half-screen-pop>
</view>
<!-- vip弹窗 -->
<VipPop visible="{{vipVisible}}"></VipPop>