import {project, pub, daily, common} from '../../../service/api';
import {getHeight} from '../../../utils/height';
import {hasPrivile} from '../../../utils/route';
import {formatDate} from '../../../utils/formate';
var tabBeh = require('../../../template/tabBar/index');
const app = getApp();
const statusList = ['未跟进', '有效项目', '在谈项目', '到访项目', '签约项目'];
const typeList = ['其他项目', '普通项目', '重点项目'];
Page({
  behaviors: [tabBeh],
  data: {
    socialContact: [],
    isFirst: true,
    selectObj: {},
    dialogDetail: '',
    showDialog: false,
    contactList: [],
    memberList: [], // 成员列表
    memberVisible: false, // 项目成员弹窗
    footerList: [
      {
        name: '写跟进',
        icon: 'https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/cyt_v2/image/business/do_track.png',
        url: '../projectManage/funPage/pubFollow/index'
      },
      {
        name: '写计划',
        icon: 'https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/cyt_v2/image/business/do_plan.png',
        url: '../projectManage/funPage/pubPlan/index'
      },
      {
        name: '新增联系方式',
        icon: 'https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/cyt_v2/image/business/add_contact.png',
        url: '../projectManage/funPage/pubAdduser/index'
      }
    ],
    trackPlanList: [],
    trackDateList: [],
    selectList: {},
    visible: false,
    openSet: false,
    showMore: false, // 相关资讯查看更多
    isshowAll: true,
    navHeight: 0,
    obj: {}, //当前详情页面的对象
    history: [], // 融资历程
    information: [], // 相关资讯
    contentH: 0, //内容滚动高度
    contentT: 450, //
    tabs: [
      {
        title: '跟进记录',
        name: 'information',
        type: '',
        activeCode: 'INDUSTRY_NEWS' //这里后面换成对应的字段
      },
      {
        title: '联系方式',
        name: 'rb',
        activeCode: ''
      }
    ],
    scrollTop: 0,
    // tab栏相关数据
    activeIndex: '0',
    contentTop: 0,
    selfIsTeamVip: false // 自己是否是团队vip
  },
  onLoad(options) {
    app.showLoading('加载中...');

    let {id} = options;
    this.setData(
      {
        navHeight: app.globalData.navBarHeight,
        projectId: id
      },
      () => {
        this.init();
      }
    );
  },
  // 滚动
  getRestHeight() {
    getHeight(this, ['#navigationBar'], data => {
      let {screeHeight, res} = data;
      // console.log('navigationBarHB', res[0].height);
      this.setData({
        contentH: screeHeight - res[0].height //内容滚动高度
        // navigationBarH: res[0].height,
        //导航顶部高度
      });
    });
  },
  // 滚动
  getHeight() {
    getHeight(this, ['.h_head', '#navbars', '.navigationBar'], data => {
      let {screeHeight, res, statusBarHeight} = data;
      console.log('navBar', JSON.stringify(res[1]));
      let navigationBarH = res[0]?.top; //自定义状态栏高度
      let navBarH = res[1]?.height; //tab栏自身高度
      let navTopH = res[1]?.top - navigationBarH; // nab高度
      // let resH = screeHeight - navBarH - res[1]?.top + 180
      let popResH = screeHeight - navBarH - res[2].height;
      // console.log(navBarH, res[2].height)
      this.setData({
        // navigationBarH,
        navTopH,
        // resH,
        popTopH: navBarH + res[2].height - 4,
        popResH: popResH + 4
      });
    });
  },
  onShow() {
    this.isVip();
    if (!this.data.isFirst) {
      this.init();
    }
  },
  async isVip() {
    const res = await hasPrivile({
      packageType: true
    });
    const {person_vip_type, self_flag} = wx.getStorageSync('userData');
    this.setData({
      isvip:
        res !== '游客' && res !== '普通VIP' && res !== '个人VIP' && !self_flag,
      selfIsTeamVip: self_flag && person_vip_type == 2
    });
  },
  tabClick(e) {
    let {title: detail} = e.currentTarget.dataset;
    if (!detail) return;
    let idx = detail == '跟进记录' ? 0 : 1;
    this.getTabWidth(idx);
    // 把所有弹窗关闭
    this.setData(
      {
        activeIndex: idx
        // scrollTop: this.data.navTopH
      },
      () => {
        this.getHeight();
      }
    );
  },
  onScroll(e) {
    const {navTopH} = this.data;
    var scrollTop = e.detail.scrollTop;
    if (scrollTop >= navTopH) {
      if (this.data.isFixed) return;
      this.setData({
        isFixed: true
      });
    } else {
      if (!this.data.isFixed) return;
      this.setData({
        isFixed: false
      });
    }
  },
  openFuncUrl(e) {
    if (e.currentTarget.dataset.item) {
      let projectObj = encodeURIComponent(JSON.stringify(this.data.obj));
      app.route(
        this,
        e.currentTarget.dataset.item + `?projectObj=${projectObj}`
      );
    }
  },
  selectValue(e) {
    console.log(e);
    let tempObj = this.data.obj;
    if (this.data.selectList.label == '项目状态') {
      if (statusList.indexOf(e.currentTarget.dataset.item) != -1) {
        tempObj.project_process = statusList.indexOf(
          e.currentTarget.dataset.item
        );
      }
    } else {
      tempObj.project_type = typeList.indexOf(e.currentTarget.dataset.item);
    }
    project.changeProjectManage(tempObj).then(res => {
      console.log(6213123, res);
      this.setData({
        visible: false
      });
      this.init();
    });
  },
  closeDialog() {
    this.setData({
      showDialog: false
    });
  },
  editTag(e) {
    console.log(e);
    if (e.currentTarget.dataset.item == 'track_status') {
      this.setData({
        selectList: {
          list: statusList,
          select: this.data.obj.project_process,
          label: '项目状态'
        },
        visible: true
      });
    } else {
      this.setData({
        selectList: {
          list: typeList,
          select: this.data.obj.project_type,
          label: '项目类型'
        },
        visible: true
      });
    }
  },
  onCloseAddress() {
    this.setData({
      showAddress: false
    });
  },
  goDetail() {
    const url = encodeURIComponent(
      `https://reporth5.handidit.com?entId=${this.data.obj.ent_id}`
    );
    app.route(this, `/subPackage/pages/webs/index?url=${url}`);
  },
  // 选择项目成员
  selectMember({detail: memberInfo}) {
    const params = {
      track_user_id: memberInfo.user_id,
      track_user_name: memberInfo.user_name,
      projects: [
        {
          id: this.data.obj.id,
          ent_id: this.data.obj.ent_id,
          track_user_id: this.data.obj.track_user_id
        }
      ]
    };
    project.completeDistribute(params).then(res => {
      this.setData({
        memberVisible: false
      });
      app.showToast('项目交接完成');
      // 返回上一页并刷新列表
      let pages = getCurrentPages();
      let currPage = null;
      if (pages.length) {
        currPage = pages[pages.length - 2]; //1->當前路由 2为上级路由
      }
      setTimeout(() => {
        wx.navigateBack({
          delta: 1,
          success: () => {
            currPage?.bazaarRefresher && currPage?.bazaarRefresher();
          }
        });
      }, 1500);
    });
  },
  clickFunBox(e) {
    if (e.currentTarget.dataset.item == 'push') {
      this.getProjectMemberList();
    } else if (e.currentTarget.dataset.item == 'website') {
      daily.headInfo(this.data.obj.ent_id).then(res => {
        if (res.official_website) {
          wx.setClipboardData({
            data: res.official_website,
            success(res) {
              wx.showToast({
                title: '复制成功',
                icon: 'none'
              });
            }
          });
        } else {
          wx.showToast({
            title: '该企业暂无官网',
            icon: 'none'
          });
        }
      });
    } else if (e.currentTarget.dataset.item == 'site') {
      daily.headInfo(this.data.obj.ent_id).then(res => {
        this.setData({
          location: res.location,
          locationTxt: res.register_address,
          markers: [
            {
              id: 1,
              latitude: res.location.lat,
              longitude: res.location.lon,
              iconPath:
                'https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/marker.png',
              width: 20,
              height: 20
            }
          ],
          showAddress: true
        });
      });
    } else if (e.currentTarget.dataset.item == 'report') {
      this.goDetail();
    }
  },
  // 获取项目成员列表
  getProjectMemberList() {
    app.showLoading('加载中');
    const {user_id} = wx.getStorageSync('userData');
    project.getProjectMemberList().then(data => {
      console.log(data);
      let temparr = [];
      data.map(item => {
        if (item.user_id != user_id) {
          temparr.push(item);
        }
      });
      if (!temparr.length) {
        app.showToast('请添加团队成员!', 'none', 1500);
        return;
      }
      this.setData({
        memberList: temparr,
        memberVisible: true,
        memberTitle: '请选择交接人'
      });
      wx.hideLoading();
    });
  },
  async init() {
    wx.hideLoading();
    const this_ = this;
    const {projectId} = this.data;
    let information = [];
    await Promise.all([
      project.getProjectDetail(projectId),
      project.getProjectTrack(projectId),
      project.getProjectTrackPlan(projectId),
      pub.getPersonList(projectId)
    ])
      .then(res => {
        console.log(res);
        const trackDateList = res[2].items.map(item => ({
          ...item,
          _create_time: formatDate(item.create_time, 'yyyy-MM-dd hh:mm:ss')
        }));
        this.setData({
          obj: res[0],
          trackPlanList: res[1].items.filter(item => !item.completed),
          trackDateList,
          contactList: res[3].items
        });
        common
          .contact(this.data.obj.ent_id, encodeURI('types=1,2'))
          .then(res => {
            if (res.length) {
              this.setData({
                socialContact: res
              });
              this.setData({
                isFirst: false
              });
            }
          });
      })
      .finally(() => {
        this.getRestHeight();
        this.getHeight();
        wx.hideLoading();
      });
  },
  openPlanDialog(e) {
    let data = e.currentTarget.dataset.item;
    if (data.type == 'finishPlan') {
      this.setData({
        dialogDetail: '是否确认计划已完成',
        showDialog: true,
        selectObj: data.obj,
        operatType: 'finishPlan'
      });
    } else if (data.type == 'deletePlan') {
      this.setData({
        dialogDetail: '是否删除该计划',
        showDialog: true,
        selectObj: data.obj,
        operatType: 'deletePlan'
      });
    } else if (data.type == 'deleteContact') {
      this.setData({
        dialogDetail: '是否删除该联系人',
        showDialog: true,
        selectObj: data.obj,
        operatType: 'deleteContact'
      });
    } else if (data.type == 'editContact') {
      this.setData({
        dialogDetail: '是否编辑该联系人',
        showDialog: true,
        selectObj: data.obj,
        operatType: 'editContact'
      });
    }
  },
  dialogFun() {
    if (this.data.operatType == 'finishPlan') {
      let params = this.data.selectObj;
      params.completed = true;
      project.changeProjectPlan(params).then(res => {
        app.showToast('修改成功!');
        this.setData({
          showDialog: false
        });
        this.init();
      });
    } else if (this.data.operatType == 'deletePlan') {
      let params = this.data.selectObj;
      project
        .deleteProjectPlan([
          {
            id: params.id
          }
        ])
        .then(res => {
          app.showToast('删除成功!');
          this.setData({
            showDialog: false
          });
          this.init();
        });
    } else if (this.data.operatType == 'deleteContact') {
      let params = this.data.selectObj;
      project
        .deleteContact([
          {
            id: params.id
          }
        ])
        .then(res => {
          app.showToast('删除成功!');
          this.setData({
            showDialog: false
          });
          this.init();
        });
    } else if (this.data.operatType == 'editContact') {
      let params = encodeURIComponent(JSON.stringify(this.data.selectObj));
      let projectObj = encodeURIComponent(JSON.stringify(this.data.obj));
      this.setData({
        showDialog: false
      });
      app.route(
        this,
        `../projectManage/funPage/pubAdduser/index?isEdite=${true}&projectObj=${projectObj}&params=${params}`
      );
    }
  },
  gocurDetail(e) {
    let {
      item: {projectId}
    } = e.currentTarget.dataset;
    if (!projectId) {
      return;
    }
    let url = `../detail/index?projectId=${projectId}`;
    app.route(this, url);
  },
  // 查看全部标签
  getTagList() {
    this.setData({
      openSet: true
    });
  },
  //  关闭
  tagClose() {
    this.setData({
      openSet: false
    });
  },
  //  跳转企业详情
  goToEntDetail(e) {
    let {item} = e.currentTarget.dataset;
    if (item.ent_id) {
      const url = encodeURIComponent(
        `https://reporth5.handidit.com?entId=${item.ent_id}`
      );
      app.route(this, `/subPackage/pages/webs/index?url=${url}`);
    }
  },
  //  跳转企业官网
  goToWebsite(e) {
    const {item} = e.currentTarget.dataset;
    if (item.website) {
      wx.setClipboardData({
        data: item.website,
        success(res) {
          wx.showToast({
            title: '地址复制成功,请在浏览器粘贴打开!',
            icon: 'none'
          });
        }
      });
    }
  },
  // 查看资讯
  showInfo(e) {
    const {item} = e.currentTarget.dataset;
    if (item.url) {
      wx.setClipboardData({
        data: item.url,
        success(res) {
          wx.showToast({
            title: '地址复制成功,请在浏览器粘贴打开!',
            icon: 'none'
          });
        }
      });
    }
  },
  //  查看更多资讯
  showAllInfo() {
    const {obj} = this.data;
    if (obj.ent_id) {
      const url = encodeURIComponent(
        `https://reporth5.handidit.com/feelings?entid=${obj.ent_id}`
      );
      app.route(this, `/subPackage/pages/webs/index?url=${url}`);
    }
  }
});
