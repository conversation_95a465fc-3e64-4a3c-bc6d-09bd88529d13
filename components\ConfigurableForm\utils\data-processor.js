/**
 * 数据处理工具
 * 专门处理多选数据和参数转换
 */

const {
  clone
} = require('../../../utils/util');
const {
  SEARCH_CONSTANTS
} = require('../config/fields');

/**
 * 处理多选数据 - 有父级时，子级不需要的情况
 * 优化逻辑：提高性能，增加错误处理，支持配置化
 * @param {Array} ary - 原始数据数组
 * @param {Object} options - 配置选项
 * @returns {Array} 处理后的数组
 */
function handleMultiple(ary = [], options = {}) {
  // 参数验证
  if (!Array.isArray(ary) || ary.length === 0) {
    return [];
  }

  const {
    statusField = 'status',
      checkedValue = 'checked',
      isChildrenField = 'ischildren',
      codeField = 'code',
      parentField = 'parent'
  } = options;

  try {
    // 深度克隆避免修改原数组
    let tempArray = clone(ary);

    // 获取所有选中的项
    const checkedItems = tempArray.filter(
      item => item[statusField] === checkedValue
    );

    // 如果没有选中项，返回原数组
    if (checkedItems.length === 0) {
      return ary;
    }

    // 递归获取某个节点的所有子孙节点
    function getAllDescendants(parentCode, allItems) {
      const descendants = new Set();

      // 找到直接子节点
      const directChildren = allItems.filter(
        item => item[parentField] === parentCode
      );

      directChildren.forEach(child => {
        descendants.add(child[codeField]);
        // 递归获取子节点的子孙节点
        const childDescendants = getAllDescendants(child[codeField], allItems);
        childDescendants.forEach(desc => descendants.add(desc));
      });

      return descendants;
    }

    // 找出需要移除的所有节点（包括多级子孙节点）
    const nodesToRemove = new Set();

    checkedItems.forEach(checkedItem => {
      // 获取该选中项的所有子孙节点
      const descendants = getAllDescendants(checkedItem[codeField], tempArray);
      descendants.forEach(desc => nodesToRemove.add(desc));
    });

    // 移除所有需要删除的节点
    tempArray = tempArray.filter(item => !nodesToRemove.has(item[codeField]));
    return tempArray.filter(i => i.status === 'checked');
  } catch (error) {
    console.error('handleMultiple 处理失败:', error);
    return ary; // 出错时返回原数组
  }
}

/**
 * 处理搜索参数数据 - 简化版本
 * @param {Object} params - 原始参数对象
 * @param {Object} options - 配置选项
 * @returns {string} JSON字符串格式的处理结果
 */
function handleData(params, options = {}) {
  if (!params || typeof params !== 'object') {
    return JSON.stringify({});
  }

  const {
    returnString = false
  } = options;

  try {
    // 深度克隆避免修改原对象
    let result = clone(params);

    // 处理弹窗字段 - 提取选中项的code
    const popupFields = SEARCH_CONSTANTS.POPUP_FIELDS;

    popupFields.forEach(field => {
      if (result[field] && Array.isArray(result[field])) {
        // 提取选中项的code
        result[field] = handleMultiple(result[field])
          .filter(item => item.status === 'checked' || item.active === true)
          .map(item => item.code);
      }
    });
    // 处理所有范围输入字段，移除 special 和 name 属性
    SEARCH_CONSTANTS.RANGE_INPUT_FIELDS.forEach(field => {
      if (result[field] && Array.isArray(result[field]) && result[field].length > 0) {
        result[field] = result[field].map(item => {
          // 只保留 start 和 end 属性
          return {
            start: item?.start || "",
            end: item?.end || ""
          };
        });
      }
    });
    // 处理布尔值字段
    const booleanFields = SEARCH_CONSTANTS.RADIO_FIELDS;

    booleanFields.forEach(field => {
      if (result[field] && Array.isArray(result[field])) {
        const value = result[field][0];
        if (value === 'true' || value === 'false') {
          result[field] = JSON.parse(value);
        }
      }
    });
    // 移除值为 [] 的字段
    Object.keys(result).forEach(key => {
      if (Array.isArray(result[key]) && result[key].length === 0) {
        delete result[key];
      }
    });

    return returnString ? JSON.stringify(result) : result;
  } catch (error) {
    console.error('handleData 处理失败:', error);
    return returnString ? JSON.stringify(params) : params;
  }
}
// 返回名字 
function getNameFromPop(arr, options = {}) {
  if (!Array.isArray(arr) || arr.length === 0) return '';
  const arrName = arr.map(item => item.name).join(',');
  if (options.slice && arrName.length > 4) {
    return arrName.slice(0, 4) + '...';
  }
  return arrName;
}

/**
 * 处理回填数据 - 用于数据回显，不做多选处理
 * @param {Object} params - 参数对象
 * @returns {Object} 处理后的数据
 */
function handleBackfillData(params) {
  if (!params || typeof params !== 'object') {
    return {};
  }

  // 回填数据不需要做多选处理，保持原始结构
  return handleData(params, {
    returnString: false,
    processMultiple: false
  });
}
module.exports = {
  handleMultiple,
  handleData,
  handleBackfillData,
  getNameFromPop
};