<import src="/template/menuhead/index"></import>
<view class="head">
  <template is='menu-head' data="{{selected_source_name,dropDownMenuTitle,district_open,source_open,filter_open,selected_filter_name,selected_source_name,district_val,source_val,filter_val }}"></template>
</view>

<!-- 具体内容 -->
<!-- 地区 -->
<region-selection visible="{{district_open}}" top="{{top}}" bindsubmit="getRegion" bindclose="closeRegion" oldData="{{regionData}}" />

<!-- 行业 -->
<industry-select visible="{{source_open}}" top="{{top}}" oldData="{{industryData}}" bindsubmit="getIndustry" dataType='eleseicAry' bindclose="closeRegion" />

<!-- 更多筛选 -->
<TagMultipleChoice
  visible="{{filter_open}}"
  dataList="{{[regCapital, regTime, entScale, benefitAssess]}}"
  top="{{top}}"
  bindsubmit='handleMoreFilter'
  bindhandleregTime="handleregTime"
  bindsetVipVisible="setVipVisible"
  bindclose="close"
>
  <!-- 注册资本文本框 -->
  <view slot="regCapital" class="regCapital textBox" >  
    <input class="year" type="number" data-type="viewMinCapital" bindinput="handleInput" model:value="{{viewMinCapital}}" ></input>
    <text class="unit">万</text>
    <text class="short-line">—</text>
    <input class="year" type="number" data-type="viewMaxCapital" bindinput="handleInput" model:value="{{viewMaxCapital}}" ></input>
    <text class="unit">万</text>
  </view>
  <!-- 注册年限文本框 -->
  <view slot="regTime" class="regTime textBox" >  
    <view class="year" data-type="regTime-viewStartTime" bindtap="showDatePicker">{{viewStartTime ||'开始时间'}}</view>
    <text class="short-line">—</text>
    <view class="year" data-type="regTime-viewEndTime" bindtap="showDatePicker">{{viewEndTime || '结束时间'}}</view>
  </view>
  <view slot="tip2" bindtap="showTip" data-type="entScale" class="tip-wrap">
    <view class="tip-icon"></view>
    <view class="vip-icon"></view>
  </view>
  <view slot="tip3" bindtap="showTip" data-type="benefitAssess" class="tip-wrap">
    <view class="tip-icon"></view>
    <view class="vip-icon"></view>
  </view>
</TagMultipleChoice>

<!-- 时间选择 -->
<DatePicker
  visible="{{showPicker}}"
  _date="{{backfillDate}}"
  dateType="{{dateType}}"
  bindsetDate="setDate"
  title="{{title}}"
>
</DatePicker>

<!-- 提示弹窗 -->
<half-screen-pop title="提示" position="bottom" zIndex="{{99}}" showCancelBtn="{{false}}" showCancelCls visible="{{tipVisible}}" confirmBtnText="知道了">
  <view class="expPop" slot="customContent">
    {{tipContent}}
  </view>
</half-screen-pop>


<!-- capture-catch:touchmove="preventdefault" -->