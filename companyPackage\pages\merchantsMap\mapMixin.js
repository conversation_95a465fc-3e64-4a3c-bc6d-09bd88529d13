// 引入高德地图微信小程序SDK
var amapFile = require('../../lib/amap-wx130.js');
import {map} from '../../../service/api';

// 默认位置常量（北京）
const DEFAULT_LOCATION = {
  latitude: 39.9042,
  longitude: 116.4074,
  locationName: '北京市',
  adcode: '110100'
};

module.exports = function (params) {
  const DISMAP = {
    1: 14,
    2: 13,
    3: 12,
    4: 12,
    5: 12,
    6: 11,
    7: 11,
    8: 11,
    9: 11,
    10: 11
  };
  return Behavior({
    data: {
      markers: [], //实际渲染的markers
      // 圈内markers集合
      insideMarkes: [],
      // 圈外markers
      outMarkes: [],
      // 如果当前是5k米 就显示一共有好多家
      fixMarkes: [],
      circles: [],
      mapCtx: null,
      // 防抖相关
      _debounceTimer: null,
      _isUpdating: false,

      // 中心点管理
      originalCenter: {
        latitude: '',
        longitude: '',
        locationName: '当前位置',
        adcode: ''
      }, // 原始中心点（功能：用户点击回到当前位置）
      currentCenter: {
        latitude: '',
        longitude: '',
        locationName: '',
        adcode: ''
      }, // 当前中心点（拖动或缩放后的中心点）--请求以及渲染

      // 业务回调函数
      _onCenterChange: null, // 中心点变化回调
      _onDataRefresh: null // 数据刷新回调
    },

    lifetimes: {
      attached() {},

      ready() {
        if (!this.mapCtx) {
          this.mapCtx = wx.createMapContext('map');
        }
      },

      detached() {
        this._cleanup();
      }
    },

    methods: {
      /**
       * 初始化地图
       */
      _initializeMap() {
        if (!this.mapCtx) {
          this.mapCtx = wx.createMapContext('map');
        }
        if (!this.myAmapFun) {
          // 初始化高德地图SDK
          this.myAmapFun = new amapFile.AMapWX({
            key: 'ea478181d97e3d92be0150c05603ddb6'
          });
        }
      },

      // 初始化定位服务
      _initializeLocation() {
        // 本地有就用本地
        const mapSearchItem = wx.getStorageSync('mapSearchItem');
        if (mapSearchItem) {
          const item = JSON.parse(mapSearchItem);
          this.updateCurrentCenter(
            item.location.lat,
            item.location.lng,
            item.title,
            this.getCityCode(item.adcode)
          );
          wx.removeStorageSync('mapSearchItem');
          return;
        }

        // 检查授权状态
        if (!this.data.isLocation) {
          // 没有授权，直接使用默认北京位置，不尝试获取当前位置
          this.updateCurrentCenter(
            DEFAULT_LOCATION.latitude,
            DEFAULT_LOCATION.longitude,
            DEFAULT_LOCATION.locationName,
            DEFAULT_LOCATION.adcode
          );
          return;
        }

        // 有授权，获取当前位置
        this.getCurrentLocation((latitude, longitude, name, adcode) => {
          this.updateCurrentCenter(latitude, longitude, name, adcode);
        });
      },
      // 获取当前位置
      getCurrentLocation(callback) {
        const that = this;
        this.myAmapFun.getRegeo({
          success: function (data) {
            // 区域编码
            const adcode = data[0].regeocodeData.addressComponent.adcode;
            const latitude = data[0]?.latitude;
            const longitude = data[0]?.longitude;
            const name = data[0]?.name;
            // 设置原始中心点
            that.setOriginalCenter(
              latitude,
              longitude,
              name,
              that.getCityCode(adcode)
            );
            // 将经纬度抛出去 用于请求
            callback &&
              callback(latitude, longitude, name, that.getCityCode(adcode));
          },
          fail: function (info) {
            const {originalCenter} = that.data;
            if (originalCenter.latitude) {
              callback &&
                callback(
                  originalCenter.latitude,
                  originalCenter.longitude,
                  originalCenter.name,
                  originalCenter.adcode
                );
              return;
            }
            // 如果获取位置失败，使用默认位置（北京）
            callback &&
              callback(
                DEFAULT_LOCATION.latitude,
                DEFAULT_LOCATION.longitude,
                DEFAULT_LOCATION.locationName,
                DEFAULT_LOCATION.adcode
              );
          }
        });
        // wx.getLocation 这个小程序不能一致掉
      },
      // 更新当前中心点
      updateCurrentCenter(latitude, longitude, name, adcode, isRest = true) {
        if (!latitude || !longitude || !name) return;

        // 确保数据类型正确
        const lat = parseFloat(latitude);
        const lng = parseFloat(longitude);

        // 将地图移动到当前中心点
        this.setMapCenter(lat, lng);

        // 首先设置当前中心点以及名称
        const currentCenter = {
          latitude: lat,
          longitude: lng,
          locationName: name,
          adcode
        };

        // 设置相关参数
        this.setData(
          {
            currentCenter,
            'params.page_index': 1,
            'params.grids': [
              {
                lon: lng,
                lat: lat
              }
            ],
            hasData: true,
            requestData: []
          },
          () => {
            // 请求列表 - 需要同时检查登录状态和授权状态
            if (!this.data.isLocation) {
              return;
            }
            // 更新圆圈
            this.updateCircle(lat, lng);
            this.getHot();

            this.getList(isRest ? 'noScall' : '');
          }
        );
      },
      // 清理资源
      _cleanup() {
        // 清除防抖定时器
        if (this._debounceTimer) {
          clearTimeout(this._debounceTimer);
          this._debounceTimer = null;
        }
      },
      // 处理marks数据
      mergeSameLocationMarkers(markers) {
        const mergedMap = new Map();

        for (const marker of markers) {
          const key = `${marker.latitude},${marker.longitude}`;

          if (!mergedMap.has(key)) {
            // 初始化
            mergedMap.set(key, {
              ...marker,
              count: 1,
              calloutContents: [marker.callout.content]
            });
          } else {
            const existing = mergedMap.get(key);
            existing.count += 1;
            existing.id = marker.id; // 用最后一个的 id
            existing.calloutContents.push(marker.callout.content);
            existing.callout.content = `${marker.callout.content}（等${existing.count}家）`;
            existing.label.content = existing.count;
          }
        }
        // 清理合并信息（删除 calloutContents）
        const result = Array.from(mergedMap.values())
          .map(marker => {
            const {calloutContents, count, ...rest} = marker;
            return rest;
          })
          .map(i => {
            return {
              ...i,
              label: {
                ...i.label,
                content: i.label.content + '',
                anchorY: this.data.isAn ? '-24' : '-24',
                anchorX: this.data.isAn ? '-9' : 0
              }
            };
          });
        return result;
      },
      // 获取区县markes坐标参数
      async getOutMarkes() {
        // 获取当前区域编码
        const {
          currentCenter: {adcode},
          params,
          exitAdcode,
          outMarkes
        } = this.data;
        // 处理参数 这里参数后续要确定一下 todo
        const newParams = {
          ...params,
          level: 3,
          area_code_list: [adcode] // 这里只需要编码和其他筛选条件
        };
        // 下面这些传上去也不影响 反正没取
        delete newParams.grids;
        delete newParams.radius;
        delete newParams.distance;
        delete newParams.page_size;
        delete newParams.shape;

        const res = await map.mapHots({
          ...newParams
        });
        this.setData({
          exitAdcode: adcode
        });
        return res
          .filter(i => i.location && i.total > 0)
          .map(item => {
            const {location, region_id, region_name, total} = item;
            const [longitude, latitude] = location.split(',');
            return {
              id: region_id,
              width: 0,
              height: 0,
              latitude,
              longitude,
              alpha: 0,
              label: {
                content: `${region_name}(等${total}家)`, // Use name if available, otherwise default
                color: '#fff',
                fontSize: 13,
                display: 'ALWAYS',
                borderRadius: 4,
                padding: 6,
                bgColor: '#F03F2D',
                textAlign: 'center',
                anchorY: this.data.isAn ? '-40' : '-40',
                anchorX: this.data.isAn ? '-80' : 0
              }
            };
          });
      },
      // 获取数据生成marks
      async getHot() {
        // 逻辑 1.中心经纬度==后端(确定区县)=》获取两份数据： 一份是圈内数据 ，一份是圈外数据（市下---》区县名称以及经纬度）
        // 2.将数据保存起来 判断当前缩放程度决定渲染那套markers
        // 监听缩放，当缩放到一定程度展示不停markers 大于区县就圈外数据，小于区县就圈内数据
        if (!this.data.isLogin) {
          return;
        }
        const {params, currentCenter, count, scale, requestData, temScale} =
          this.data;
        const centerLat = currentCenter.latitude;
        const centerLng = currentCenter.longitude;
        // const radius = params.radius; // 圆圈半径（米）
        // 圈内mark
        const tempMarkes = requestData
          .filter(i => i.location?.lat && i.location?.lon)
          .map(i => {
            return {
              latitude: i.location.lat,
              longitude: i.location.lon,
              id: i.ent_id,
              iconPath:
                'https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/cyt_v2/image/companyPackageImg/m_ico.png', // 替换为你的图标路径
              width: 18,
              height: 24,
              callout: {
                content: `${i.ent_name}`, // Use name if available, otherwise default
                color: '#fff',
                fontSize: 13,
                display: 'ALWAYS',
                borderRadius: 4,
                padding: 6,
                bgColor: '#F03F2D',
                textAlign: 'center',
                anchorY: '-3'
              },
              label: {
                content: 1,
                color: '#fff',
                fontSize: 12,
                bgColor: 'transparent',
                anchorX: '0',
                anchorY: this.data.isAn ? '-0' : '-26',
                textAlign: 'center',
                width: 18,
                height: 24
              }
            };
          });
        const insideMarkes = this.mergeSameLocationMarkers(tempMarkes);
        // 圈外mark
        const outMarkes = await this.getOutMarkes();
        // 5km默认的时候
        const fixMarkes = [
          {
            id: 0,
            width: 0,
            height: 0,
            latitude: +centerLat,
            longitude: +centerLng,
            alpha: 0,
            label: {
              content: `附近${params.radius / 1000}km找到${count}家相关企业`,
              color: '#fff',
              fontSize: 13,
              display: 'ALWAYS',
              borderRadius: 4,
              padding: 6,
              bgColor: '#F03F2D',
              textAlign: 'center',
              anchorY: '-80',
              anchorX: this.data.isAn ? '-100' : 0
            }
          }
        ];
        // 为了解决地图放大缩小值回弹 用的临时缩放
        const temScales = temScale ? temScale : scale;

        const circles =
          temScales < 11
            ? []
            : [
                {
                  latitude: +centerLat,
                  longitude: +centerLng,
                  color: '#00000000',
                  fillColor: '#E7241024',
                  radius: params.radius,
                  strokeWidth: 1.8
                }
              ];
        const dis = DISMAP[params.radius / 1000];
        this.setData({
          circles: circles,
          // markers: temScales < 11 ?
          //   outMarkes : temScales >= 11 && temScales <= DISMAP[params.radius / 1000] ?
          //   fixMarkes : insideMarkes,
          markers:
            temScales < 11
              ? outMarkes
              : temScales >= 11 && temScales <= (dis === 11 ? 12 : dis)
              ? fixMarkes
              : insideMarkes,
          insideMarkes: insideMarkes,
          outMarkes: outMarkes,
          fixMarkes: fixMarkes
        });
      },
      /**
       * 防抖函数 - 防止频繁调用，只在停止触发后执行
       * @param {Function} func 要执行的函数
       * @param {number} delay 延迟时间（毫秒）
       */
      // 修改防抖函数，使用队列机制而不是直接忽略
      _debounce(func, delay = 1000) {
        // 清除之前的定时器
        if (this._debounceTimer) {
          clearTimeout(this._debounceTimer);
        }

        // 设置新的定时器，不再检查 _isUpdating
        this._debounceTimer = setTimeout(() => {
          try {
            func();
          } catch (error) {
            console.error('防抖函数执行出错:', error);
          } finally {
            this._debounceTimer = null;
          }
        }, delay);
      },
      // 将区县转市 区域编码
      getCityCode(code) {
        const str = String(code).padStart(6, '0');

        // 特殊直辖市映射（写死）
        const special = {
          110000: '110100',
          120000: '120100',
          310000: '310100',
          500000: '500000'
        };

        if (special[str]) return special[str];

        // 如果是重庆的区县（5001XX），特殊处理为 500000
        if (str.startsWith('5001')) return '500000';

        // 如果是地市级（以00结尾但非0000）
        if (str.endsWith('00') && !str.endsWith('0000')) return str;

        // 普通县级码 → 返回其归属市级
        return str.slice(0, 4) + '00';
      },

      //  经纬度 -> 地址名称（逆向解析）- 高德地图SDK
      // 修改 getLocationName 方法，添加重试机制
      getLocationName(longitude, latitude, callback, retryCount = 0) {
        const that = this;
        const maxRetries = 2; // 最大重试次数

        // 参数验证
        if (!longitude || !latitude || typeof callback !== 'function') {
          console.error('getLocationName: 参数无效');
          callback && callback('');
          return;
        }
        // 使用高德地图SDK进行逆向地理编码
        this.myAmapFun.getRegeo({
          location: `${longitude},${latitude}`,
          success: data => {
            const adcode = data[0].regeocodeData.addressComponent.adcode;
            // 这里可能未空数组 --国外 或者说没解析到
            if (!adcode.length) {
              wx.showToast({
                title: '获取定位失败！',
                icon: 'none'
              });
              callback('未知位置', '');
              return;
            }
            // 获取格式化地址
            let address = '';
            let regeocode = null;

            if (Array.isArray(data) && data.length > 0) {
              regeocode = data[0].regeocodeData;
            } else if (data && data.regeocodeData) {
              regeocode = data.regeocodeData;
            } else if (data && data.regeocode) {
              regeocode = data.regeocode;
            }

            if (regeocode) {
              if (regeocode.formatted_address) {
                address = regeocode.formatted_address;
              } else if (regeocode.addressComponent) {
                const addr = regeocode.addressComponent;
                address = `${addr.province || ''}${addr.city || ''}${
                  addr.district || ''
                }${addr.township || ''}`;
              }
            }

            callback(address || '未知位置', that.getCityCode(adcode));
          },
          fail: error => {
            console.error(`高德逆地理编码失败 (第${retryCount + 1}次):`, error);

            // 如果还有重试次数，则重试
            if (retryCount < maxRetries) {
              setTimeout(() => {
                that.getLocationName(
                  longitude,
                  latitude,
                  callback,
                  retryCount + 1
                );
              }, 1000 * (retryCount + 1)); // 递增延迟时间
            } else {
              // 重试次数用完，返回未知位置
              console.error('重试次数用完，返回未知位置');
              callback('未知位置');
            }
          }
        });
      },
      // 地图区域变化事件处理（拖动和缩放时触发）
      regionchange(e) {
        const that = this;
        // 拖拽处理----发请求但是圈不重置
        if (e.type === 'end' && e.causedBy === 'drag') {
          const {centerLocation} = e.detail;

          // 减少防抖时间，提高响应速度
          this._debounce(() => {
            if (!this.mapCtx) {
              console.error('mapCtx 未初始化');
              return;
            }

            // 添加加载状态提示
            that.setData({
              'currentCenter.locationName': '定位中...'
            });
            that.getLocationName(
              centerLocation.longitude,
              centerLocation.latitude,
              (locationName, adcode) => {
                // 确保地址更新成功
                if (locationName && locationName !== '未知位置') {
                  that.updateCurrentCenter(
                    centerLocation.latitude,
                    centerLocation.longitude,
                    locationName,
                    adcode,
                    true
                  );
                } else {
                  // 如果获取地址失败，至少更新坐标
                  that.setData({
                    'currentCenter.locationName': '位置获取失败',
                    'currentCenter.latitude': centerLocation.latitude,
                    'currentCenter.longitude': centerLocation.longitude
                  });
                }
              }
            );
          }, 500);
        }

        // 缩放处理
        if (e.type === 'end' && e.causedBy === 'scale') {
          const {scale: rawScale} = e.detail;
          const currentScale = this.data.scale;

          // 智能取整：放大向上取整，缩小向下取整
          let smartScale;
          let operation;

          if (rawScale > currentScale) {
            // 放大操作：向上取整
            smartScale = Math.ceil(rawScale);
            operation = '放大';
          } else if (rawScale < currentScale) {
            // 缩小操作：向下取整
            smartScale = Math.floor(rawScale);
            operation = '缩小';
          } else {
            // 相等：保持不变
            smartScale = currentScale;
            operation = '无变化';
          }

          // 只有当智能取整后的值与当前值不同时才更新
          if (smartScale !== currentScale) {
            // 更新缩放级别并重新渲染
            this._debounce(() => {
              that.setData(
                {
                  scale: smartScale,
                  temScale: smartScale
                },
                () => {
                  // 重新渲染标记和圆圈
                  that.getHot();
                }
              );
            }, 300); // 缩放响应更快
          } else {
          }
        }

        // 处理安卓设备可能的事件差异
        if (e.type === 'end' && !e.causedBy) {
          // 某些安卓设备可能不提供 causedBy 参数
          const {scale: rawScale} = e.detail;

          if (rawScale && typeof rawScale === 'number') {
            const currentScale = this.data.scale;

            // 使用同样的智能取整逻辑
            let smartScale;
            let operation;

            if (rawScale > currentScale) {
              // 放大操作：向上取整
              smartScale = Math.ceil(rawScale);
              operation = '放大';
            } else if (rawScale < currentScale) {
              // 缩小操作：向下取整
              smartScale = Math.floor(rawScale);
              operation = '缩小';
            } else {
              // 相等：保持不变
              smartScale = currentScale;
              operation = '无变化';
            }

            if (smartScale !== currentScale) {
              this._debounce(() => {
                that.setData(
                  {
                    scale: smartScale,
                    temScale: smartScale
                  },
                  () => {
                    that.getHot();
                  }
                );
              }, 300);
            }
          }
        }
      },

      /**
       * 手动设置地图中心
       * @param {number} latitude 纬度
       * @param {number} longitude 经度
       */
      setMapCenter(latitude, longitude) {
        const {currentCenter} = this.data;
        if (
          latitude == currentCenter.latitude &&
          longitude == currentCenter.longitude
        )
          return;
        if (!this.mapCtx) {
          console.error('mapCtx 未初始化');
          return;
        }

        if (!latitude || !longitude) {
          console.error('setMapCenter: 经纬度参数无效');
          return;
        }

        this.mapCtx.moveToLocation({
          latitude,
          longitude,
          success: () => {
            this.setData({
              latitude,
              longitude
            });
          },
          fail: err => {
            console.error('设置地图中心失败:', err);
          }
        });
      },

      //   设置数据刷新回调函数
      setDataRefreshCallback(callback) {
        if (typeof callback === 'function') {
          this._onDataRefresh = callback;
        } else {
          console.error('setDataRefreshCallback: callback必须是函数');
        }
      },
      // 设置原始中心点（用户当前位置）
      setOriginalCenter(
        latitude,
        longitude,
        locationName = '当前位置',
        adcode
      ) {
        const originalCenter = {
          latitude,
          longitude,
          locationName,
          adcode
        };
        this.setData({
          originalCenter
        });
      },

      /**
       * 回到原始中心点
       */
      backToOriginalCenter() {
        const that = this;
        that.getCurrentLocation((latitude, longitude, name, adcode) => {
          that.updateCurrentCenter(latitude, longitude, name, adcode);
        });
      },
      /**更新圆圈和*/
      updateCircle(latitude, longitude) {
        this.setData({
          circles: [
            {
              latitude: +latitude,
              longitude: +longitude,
              color: '#00000000',
              fillColor: '#E7241024',
              radius: this.data.params.radius,
              strokeWidth: 1.8
            }
          ]
        });
      }
    }
  });
};
