
@import '../../../../template/null/null.scss';

.issue-aninvoice-wrapper {
  width: 100%;
}

.issue-aninvoice-wrapper .apply-for-wrapper,
.issue-aninvoice-wrapper .apply-record-wrapper {
  width: 100%;
  height: auto;
  position: relative;
  overflow-y: auto;
}

.issue-aninvoice-wrapper .apply-for-wrapper .apply-item {
  background: #fff;
  padding: 32rpx;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  margin-top: 20rpx;
}

.issue-aninvoice-wrapper .apply-record-wrapper .record-item {
  background: #fff;
  padding: 32rpx 24rpx;
  padding-bottom: 0;
  margin-top: 20rpx;
}

/* 底部 button */
.apply-for-wrapper .confirm-wrapper {
  width: 100%;
  height: 168rpx;
  padding: 10rpx 24rpx 78rpx;
  position: fixed;
  left: 0;
  bottom: 0;
  z-index: 2;
  background: #fff;
}

.apply-for-wrapper .confirm-wrapper button {
  width: 100%;
  background: linear-gradient(90deg, #F56E60 0%, #E72410 100%);
  font-weight: 100;
  line-height: 44rpx;
  font-size: 32rpx;
}

/* list -item 发票申请--- */
.apply-item .checked-box {
  width: 32rpx;
  height: 32rpx;
  border-radius: 50%;
  margin-right: 32rpx;
}

.apply-item .checked-box.show {
  border: 2rpx solid #DEDEDE;
}

.apply-item .checked-box image {
  width: 32rpx;
  height: 32rpx;
  vertical-align: text-top;
  box-sizing: border-box;
}

.apply-item .apply-content .title {
  line-height: 44rpx;
  font-size: 32rpx;
  font-family: PingFang SC-Semibold, PingFang SC;
  font-weight: 600;
  color: #20263A;
  margin-bottom: 24rpx;
}

.apply-item .apply-content .order,
.apply-item .apply-content .time {
  line-height: 40rpx;
  font-size: 28rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  color: #74798C;
}

.apply-item .apply-content .order {
  margin-bottom: 16rpx;
}

.apply-item .apply-content text {
  line-height: 40rpx;
  font-size: 28rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  color: #20263A;
}

.apply-item .apply-money {
  line-height: 40rpx;
  font-size: 32rpx;
  font-family: PingFang SC-Semibold, PingFang SC;
  font-weight: 600;
  color: #E72410;
  margin-left: auto;
}

/* list -item 发票记录--- */
.record-item {
  line-height: 40rpx;
  font-size: 28rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  color: #74798C;
}

.record-item .record-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.record-item .record-header .title {
  line-height: 44rpx;
  font-size: 32rpx;
  font-weight: 600;
  color: #20263A;
}

.record-item .record-header .record-label {
  padding: 0 10rpx;
  line-height: 36rpx;
  border-radius: 4rpx;
  font-size: 24rpx;
  margin-bottom: 24rpx;
}
.record-label.padding {
  border: 1rpx solid #FD9331;
  color: #FD9331;
}
.record-label.complete {
  border: 1rpx solid #26C8A7;
  color: #26C8A7;
}


.record-item .record-content {
  padding: 24rpx 0 32rpx;
  border-bottom: 1rpx solid #EEEEEE;
}

.record-item .record-content .record-single {
  display: flex;
  justify-content: flex-start;
  margin-bottom: 16rpx;
}
.record-item .record-content .record-single>text {
  display: inline-block;
}

.record-content .record-single .label {
  min-width: 140rpx;
}

.record-content .record-single .content {
  flex: 1;
  color: #20263A;
}
.record-content .record-single .content  .copy {
  width: 128rpx;
  height: 40rpx;
  padding: 2rpx 16rpx;
  font-size: 24rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #20263A;
  background: #EEEEEE;
  border-radius: 8rpx;
  margin-left: 24rpx;

}
/* footer */
.record-item .record-footer {
  padding: 24rpx 0;
}

.record-item .record-footer text {
  line-height: 40rpx;
  font-size: 32rpx;
  font-family: PingFang SC-Semibold, PingFang SC;
  font-weight: 600;
  color: #E72410;
}