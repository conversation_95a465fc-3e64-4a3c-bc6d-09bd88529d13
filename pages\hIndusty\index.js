import {getHeight} from '../../utils/height';
import {hasPrivile} from '../../utils/route';
import {formatLargeNumberAry} from '../../utils/util';
import {
  hotStatisticsApi,
  classicStatisticsApi,
  originalStatisticsApi,
  hotIndustrySimpleListApi,
  classicIndustrySimpleListApi,
  originalIndustrySimpleListApi,
  userBrowseHistoryApi
} from '../../service/industryApi';

const app = getApp();
const filterData = [
  {
    chain_code: 'all',
    chain_name: '全部'
  },
  {
    chain_code: 'hot',
    chain_name: '热点产业名单'
  },
  {
    chain_code: 'classic',
    chain_name: '经典产业名单'
  },
  {
    chain_code: 'chainMap',
    chain_name: '产业链图谱'
  }
];
Page({
  data: {
    filterList: [], // 头部筛选列表
    visible: false,
    startDistance: 0,
    selectChain: {},
    recentlyViewedList: [], // 最近查看列表
    isLogin: app.isLogin(), // 是否登录
    hotStatsData: {
      categories: 0,
      fields: 0,
      companies: '0'
    },
    classicStatsData: {
      categories: 0,
      fields: 0,
      companies: '0'
    },
    originalStatsData: {
      categories: 0,
      fields: 0,
      companies: '0'
    },
    hotCardList: [],
    classicCardList: [],
    originalCardList: []
  },

  onLoad: async function (options) {
    this.scrollH();
    // 简化后的页面加载逻辑
    this.setData({
      filterList: filterData,
      selectChain: {
        chain_name: filterData[0].chain_name,
        chain_code: filterData[0].chain_code
      }
    });
  },

  onReady: function () {},

  onShow: function () {
    this.setData(
      {
        isLogin: app.isLogin()
      },
      () => {
        const {isLogin} = this.data;
        if (!isLogin) return; // 未登录不调用接口
        this.initData();
      }
    );
  },
  async initData() {
    try {
      const results = await Promise.allSettled([
        hotStatisticsApi(),
        classicStatisticsApi(),
        originalStatisticsApi(),
        hotIndustrySimpleListApi(),
        classicIndustrySimpleListApi(),
        originalIndustrySimpleListApi(),
        userBrowseHistoryApi()
      ]);

      // 提取成功的结果，失败的使用默认值
      const res = results.map((result, index) => {
        if (result.status === 'fulfilled') {
          return result.value;
        } else {
          console.warn(`API call ${index} failed:`, result.reason);
          // 根据不同的API返回适当的默认值
          switch (index) {
            case 0: // hotStatisticsApi
            case 1: // classicStatisticsApi
            case 2: // originalStatisticsApi
              return {
                cluster_total_count: 0,
                chain_node_count: 0,
                enterprise_amount: 0,
                type_total_count: 0,
                list_count: 0,
                enterprise_count: 0,
                chain_total_count: 0
              };
            case 3: // hotIndustrySimpleListApi
            case 4: // classicIndustrySimpleListApi
            case 5: // originalIndustrySimpleListApi
              return {
                list: []
              };
            case 6: // userBrowseHistoryApi
              return [];
            default:
              return null;
          }
        }
      });

      this.setData({
        hotStatsData: {
          categories: res[0]?.cluster_total_count || 0,
          fields: res[0]?.chain_node_count || 0,
          companies: this.formatToWan(res[0]?.enterprise_amount || 0)
        },
        classicStatsData: {
          categories: res[1]?.type_total_count || 0,
          fields: res[1]?.list_count || 0,
          companies: this.formatToWan(res[1]?.enterprise_count || 0)
        },
        originalStatsData: {
          categories: res[2]?.chain_total_count || 0,
          fields: res[2]?.chain_node_count || 0,
          companies: this.formatToWan(res[2]?.enterprise_amount || 0)
        },
        recentViewedList: (res[6] || []).map(item => ({
          chain_code: item.relation_key,
          chain_name: item.relation_name,
          model_type: item.model_type || ''
        })),
        hotCardList: (res[3]?.list || [])
          .map(i => ({
            id: i.chain_code,
            title: i.chain_name,
            fieldCount: i.chain_node_count,
            companyCount: formatLargeNumberAry(i.enterprise_amount)[0],
            unit: formatLargeNumberAry(i.enterprise_amount)[1]
          }))
          .slice(0, 3),
        // 经典简单列表
        classicCardList: (res[4]?.list || [])
          .map(i => ({
            id: i.classic_industrial_id,
            title: i.classic_industrial_name,
            fieldCount: i.node_count,
            companyCount: formatLargeNumberAry(i.enterprise_count)[0],
            unit: formatLargeNumberAry(i.enterprise_count)[1]
          }))
          .slice(0, 3),
        originalCardList: (res[5]?.list || [])
          .map(i => ({
            id: i.chain_code,
            title: i.chain_name,
            fieldCount: i.chain_node_count,
            companyCount: formatLargeNumberAry(i.enterprise_amount)[0],
            unit: formatLargeNumberAry(i.enterprise_amount)[1],
            purchased: i.purchased,
            types: 'chainMap' // 标识
          }))
          .slice(0, 6)
      });
    } catch (error) {
      console.log(error);
    }
  },

  scrollH() {
    getHeight(this, ['.navigationBar', '.h_head'], data => {
      const {screeHeight, res} = data;
      let h1 = res[0]?.height || 0;
      let h2 = res[1]?.height || 0;
      let scrollHeight = screeHeight - h1 - h2;
      this.setData({
        scrollHeight,
        startDistance: h1 + h2
      });
    });
  },

  // 头部筛选箭头切换
  switchFilterArrow() {
    app.route(this, '/industryPackage/pages/chainSearch/index');
    return;
    const {visible} = this.data;
    this.setData({
      visible: !visible
    });
  },
  // 数据筛选
  handleFilter({
    currentTarget: {
      dataset: {item: selectChain}
    }
  }) {
    this.setData({
      selectChain,
      visible: false
    });
  },
  // 关闭弹窗
  close() {
    this.setData({
      visible: false
    });
  },
  // 处理跳转点击事件
  async handleClick(e) {
    const {
      selectChain,
      isLogin,
      hotStatsData,
      classicStatsData,
      originalStatsData
    } = this.data;
    const constant = e.currentTarget.dataset?.type || e.detail.type;
    const {item = {}, category = '', type} = e.detail;
    if (!isLogin) {
      app.route(this, '/pages/login/login');
    }
    const fixedUrl1 = '/industryPackage/pages/chainSearch/index',
      fixedUrl2 = '/industryPackage/pages/allChainList/index',
      fixedUrl3 = '/industryPackage/pages/IndustryListMasonry/index', // 产业名单图谱
      fixedUrl4 = '/industryPackage/pages/IndustryListVip/index'; // 产业名单vip
    const obj = e.target.dataset.item;
    let url = '';
    switch (constant) {
      case 'search':
        url = `${fixedUrl1}?filter_code=${selectChain.chain_code}`;
        break;
      case 'all':
        const arr = [
          String(hotStatsData.categories),
          String(hotStatsData.fields),
          String(hotStatsData.companies),
          String(classicStatsData.categories),
          String(classicStatsData.fields),
          String(classicStatsData.companies),
          String(originalStatsData.categories),
          String(originalStatsData.fields),
          String(originalStatsData.companies)
        ];
        const str = encodeURIComponent(JSON.stringify(arr));
        url = `${fixedUrl2}?category=${category}&selectChainCode=${selectChain.chain_code}&statsData=${str}`;
        break;
      case 'card':
        if (item.types === 'chainMap') {
          url = `${fixedUrl3}?chain_code=${
            item.id
          }&chain_name=${encodeURIComponent(item.title)}&purchased=${
            item.purchased
          }`;
        } else {
          url = `${fixedUrl4}?chain_code=${
            item.id
          }&category=${category}&chain_name=${encodeURIComponent(item.title)}`;
        }
        break;
      case 'history': //'历史记录点击'
        const {model_type, chain_code, chain_name} = obj;
        if (model_type === 'ORIGINAL_INDUSTRY') {
          // 产业链图谱
          url = `${fixedUrl3}?chain_code=${chain_code}&chain_name=${encodeURIComponent(
            chain_name
          )}`;
        } else {
          url = `${fixedUrl4}?chain_code=${chain_code}&chain_name=${encodeURIComponent(
            chain_name
          )}`;
        }
        break;
      default:
        break;
    }
    if (
      (constant === 'history' && obj.model_type !== 'ORIGINAL_INDUSTRY') ||
      (category === 'hot' && type !== 'all') ||
      (category === 'classic' && type !== 'all')
    ) {
      const bol = await this._checkVipStatus();
      if (!bol) return;
    }
    app.route(this, url);
  },
  formatToWan(num) {
    if (!num || isNaN(num)) return '0';
    return `${Math.floor(num / 10000)}万`;
  },
  async _checkVipStatus() {
    let str = await hasPrivile({
      packageType: true
    });
    if (str == '游客') {
      app.route(this, '/pages/login/login');
      return false;
    } else if (str == '普通VIP') {
      this.setData({
        vipVisible: true
      });
      return false;
    }
    return true;
  },
  vipPop(val) {
    if (val?.type === 'close') {
      this.setData({
        vipVisible: false
      });
      return;
    }
    this.setData({
      vipVisible: val
    });
  }
});
