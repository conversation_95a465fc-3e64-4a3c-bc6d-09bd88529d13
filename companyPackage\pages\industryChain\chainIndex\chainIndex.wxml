<view class="chain_index">
  <CustomNavbar
    text="产业链"
    id="navigationBar"
    showNav="{{true}}"
    class="navigationBar"
    textPositon="center"
    navBg0="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/home/<USER>"
    navBg="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/home/<USER>"
    navColor="{{['rgba(231,36,16,1)', 'rgba(231,36,16,0.1)']}}"
  >
  </CustomNavbar>
  <view class="h_head">
    <image
      src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/companyPackage/image/chain/chain_bg.png"
    ></image>
    <view class="search_wrap">
      <!-- 假的input框 ，因为i要跳转主要是做样式 -->
      <navigator
        url="/companyPackage/pages/industryChain/chainSearch/chainSearch"
        hover-class="none"
        class="h_head_input"
      >
        <view class="h_search">
          <image
            src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/search.png"
          ></image>
        </view>
        <view>请输入产业链名称</view>
      </navigator>
      <!-- 筛选 -->
      <view
        class="filter text-ellipsis {{visible && 'active'}}"
        bindtap="switchFilterArrow"
        >{{selectChain.chain_name}}</view
      >
    </view>
  </view>

  <HalfScreenPop
    showFooter="{{false}}"
    visible="{{visible}}"
    position="top"
    bindclose="close"
    startDistance="{{startDistance}}"
    zIndex="{{10}}"
  >
    <view slot="customContent">
      <scroll-view scroll-y class="filter_list">
        <view
          class="filter_item {{selectChain.chain_code === item.chain_code && 'active'}}"
          wx:for="{{filterList}}"
          wx:key="chain_code"
          data-item="{{item}}"
          bindtap="handleFilter"
        >
          {{item.chain_name}}
        </view>
      </scroll-view>
    </view>
  </HalfScreenPop>
  <scroll-view
    scroll-y
    style="height: {{scrollHeight}}px; padding-bottom: 40rpx"
  >
    <!-- 最近查看 -->
    <view
      wx:if="{{isLogin && recentViewedList.length}}"
      class="history chain_card"
    >
      <view class="tit"> 最近查看 </view>
      <view class="history_item_wrap">
        <view
          class="history_item"
          wx:for="{{recentViewedList}}"
          wx:key="id"
          data-item="{{item}}"
          catchtap="handleChain"
        >
          {{item.chain_name}}
        </view>
      </view>
    </view>
    <!-- 所有产业链 -->
    <view
      class="chain_card"
      wx:for="{{chainList}}"
      wx:key="chain_code"
      data-item="{{item}}"
    >
      <view
        class="chain_head"
        data-item="{{item}}"
        data-index="{{index}}"
        bindtap="handleSwitch"
      >
        <text
          data-item="{{item}}"
          catchtap="handleChain"
          >{{item.chain_name}}</text
        >
        <view
          wx:if="{{item.chains.length}}"
          class="arrow {{item.hidden && 'down_arrow'}}"
        ></view>
      </view>
      <view
        wx:if="{{item.chains.length}}"
        class="chain_box {{ item.hidden && 'hidden' }}"
        style="height: {{item.hidden ? 0 : item.height}}"
      >
        <view
          class="chain_item_box"
          wx:for="{{item.chains}}"
          wx:for-item="childItem"
          wx:key="chain_code"
          data-item="{{childItem}}"
          bindtap="handleChain"
        >
          <view class="chain_item">
            <view class="name text-num-ellipsis">
              {{childItem.chain_name}}
            </view>
          </view>
        </view>
      </view>
    </view>
  </scroll-view>
</view>
