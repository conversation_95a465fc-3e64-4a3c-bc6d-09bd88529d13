<import src="/template/null/null"></import>

<view class="issue-aninvoice-wrapper">
  <Navbar
    isBlack="{{true}}"
    tabs="{{tabs}}"
    bindgetHeight="barHeight"
    defaultIndex="a"
    isShowSwiper="{{true}}"
    bindviewchange="viewchange"
    isTitBlue
    bindtabchange="viewchange"
  >
    <!-- 发票申请 -->
    <view slot="a" class="apply-for-wrapper">
      <scroll-view
        scroll-y
        style="height: {{scrollHeight}}px;"
        bindrefresherrefresh="bazaarRefresher"
        refresher-triggered="{{bazaarIsTriggered}}"
        refresher-enabled
        bindscrolltolower="bazaarloadMore"
      >
        <block wx:if="{{ list.length }}">
          <view
            class="apply-item"
            wx:for="{{list}}"
            wx:key="item"
            data-item="{{item}}"
            data-ind="{{index}}"
            bindtap="onHandlerTap"
          >
            <view class="checked-box {{!item.checked && 'show'}}">
              <image
                hidden="{{!item.checked}}"
                src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/cyt_v2/image/mine/invoice_select_active.png"
              />
            </view>
            <view class="apply-content">
              <view class="title">{{ item.subject }}</view>
              <view class="order"
                >{{ index }}订单号码：<text>{{ item.pay_code }}</text></view
              >
              <view class="time"
                >下单时间：<text>{{ item._pay_time }}</text></view
              >
            </view>
            <view class="apply-money"> ￥{{ item.money }} </view>
          </view>
        </block>
        <!--暂无数据-->
        <view wx:else style="width: 100%;height: {{scrollHeight}}px;">
          <template is="null"></template>
        </view>
      </scroll-view>

      <view class="confirm-wrapper">
        <button type="primary" bindtap="onHandlerClick">立即开票</button>
      </view>
    </view>
    <!-- 发票记录 -->
    <view slot="b" class="apply-record-wrapper">
      <scroll-view
        style="height: {{scrollHeight2}}px;"
        scroll-y
        scroll-with-animation
        scroll-with-animation
      >
        <block wx:if="{{invoiceHisArr.length}}">
          <view
            class="record-item"
            wx:for="{{invoiceHisArr }}"
            wx:key="{{index}}"
          >
            <view class="record-header">
              <view class="title">{{ item.invoice_type}}</view>
              <view
                class="record-label {{ item.apply_status === '处理中' ?'padding' : 'complete'}} "
                >{{ item.apply_status }}</view
              >
            </view>
            <view class="record-content">
              <block
                wx:for="{{item.recordDict}}"
                wx:for-index="{{obj.label}}"
                wx:for-item="obj"
              >
                <view wx:if="{{obj.value}}" class="record-single">
                  <view class="label">{{ obj.label }}：</view>
                  <view class="content">
                    {{ obj.value }}
                    <text
                      wx:if="{{obj.label === '物流信息'}}"
                      class="copy"
                      data-item="{{obj.value }}"
                      bindtap="handleCopy"
                    >
                      复制单号
                    </text>
                  </view>
                </view>
              </block>
            </view>
            <view class="record-footer">
              付款金额：
              <text>￥{{ item.invoice_amount}}</text>
            </view>
          </view>
        </block>
        <!--暂无数据-->
        <view wx:else style="width: 100%;height:100vh;">
          <template is="null"></template>
        </view>
      </scroll-view>
    </view>
  </Navbar>
</view>
