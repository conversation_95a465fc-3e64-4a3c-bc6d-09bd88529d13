<!-- 沉浸式标题栏 -->
<immersive-navbar
  id="navbar"
  title="{{title}}"
  show-back-btn="{{true}}"
  show-loading-footer="{{showLoadingFooter}}"
  loading-state="{{loadingState}}"
  bindscrolltolower="onScrollToLower"
>
  <!-- 页面内容 -->
  <view slot="content" class="content">
    <!-- 头部组件 -->
    <head-component
      title="领域概况"
      stats-data="{{statsData}}"
      bind:buttonClick="onHeadButtonClick"
      bind:stats="onStatsClick"
    ></head-component>
    <!-- 内容 -->
    <view class="box">
      <!-- 重点企业 -->
      <EnterpriseList
        bind:itemClick="onEnterpriseItemClick"
        source="{{industryData}}"
      />
      <!-- 区域分布 -- 另外一个页面逻辑和这里一样 就类型不同  后面估计还是要放进去 直接传类型就行  -->
      <Area
        industryType="{{category}}"
        chianCode="{{chain_code}}"
        chianName="{{title}}"
      />
      <!-- 柱状图 -->
      <CardLayout title="增长潜力（近五年）" wx:if="{{FiveChartData.length}}">
        <view slot="content">
          <echarts-bar chartData="{{FiveChartData}}" />
        </view>
      </CardLayout>
      <!-- 创新活力 饼图 + list + 柱状图 这里直接用的组件内部发请求-->
      <CardLayout title="创新活力">
        <view slot="content">
          <Innovation
            chain_code="{{chain_code}}"
            chain_type="{{category === 'classic' ? 3 : 2}}"
            isShowBar
          />
        </view>
      </CardLayout>
      <!--  -->
      <ResearchReport
        report-list="{{reportList}}"
        reportTotal="{{reportTotal}}"
        industry-name="{{title}}"
      />
    </view>
  </view>
</immersive-navbar>
