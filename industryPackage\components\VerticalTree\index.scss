/* industryPackage/components/VerticalTree/index.wxss */
.tree-scroll-container {
  width: 100vw;
  height: 100%; // 高度由父容器决定
  overflow: hidden;
  position: relative;
  background-color: #f7f7f7;

  .tree-controls {
    position: absolute;
    top: 20rpx;
    right: 20rpx;
    z-index: 10;
    display: flex;
    gap: 20rpx;

    .control-btn {
      padding: 16rpx 24rpx;
      border-radius: 8rpx;
      font-size: 24rpx;
      color: #fff;
      text-align: center;

      transition: all 0.3s ease;
      box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);

      &.expand-btn {
        background-color: #1e75db;

        &:active {
          background-color: #1565c0;
          transform: scale(0.95);
        }
      }

      &.collapse-btn {
        background-color: #ff6b35;

        &:active {
          background-color: #e55a2b;
          transform: scale(0.95);
        }
      }

      &.center-btn {
        background-color: #9c27b0;

        &:active {
          background-color: #7b1fa2;
          transform: scale(0.95);
        }
      }

      &.export-btn {
        background-color: #4caf50;

        &:active {
          background-color: #45a049;
          transform: scale(0.95);
        }
      }
    }
  }

  .tree-canvas {
    width: 100%;
    height: 100%;
    /* 支持触摸拖拽 */
    touch-action: none; /* 禁用默认触摸行为，完全由JS控制 */
    background-color: #f7f7f7; /* Canvas背景色 */
  }

  // 权限授权弹窗样式
  .permission-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;

    .modal-mask {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.5);
    }

    .modal-content {
      position: relative;
      width: 600rpx;
      background-color: #fff;
      border-radius: 16rpx;
      padding: 40rpx;
      box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.2);

      .modal-title {
        font-size: 32rpx;
        font-weight: 600;
        color: #333;
        text-align: center;
        margin-bottom: 20rpx;
      }

      .modal-text {
        font-size: 28rpx;
        color: #666;
        text-align: center;
        line-height: 1.5;
        margin-bottom: 40rpx;
      }

      .modal-buttons {
        display: flex;
        gap: 20rpx;

        .modal-btn {
          flex: 1;
          height: 80rpx;
          border-radius: 8rpx;
          font-size: 28rpx;
          text-align: center;
          line-height: 80rpx;
          transition: all 0.3s ease;

          &.cancel-btn {
            background-color: #f5f5f5;
            color: #666;

            &:active {
              background-color: #e0e0e0;
              transform: scale(0.95);
            }
          }

          &.confirm-btn {
            background-color: #1e75db;
            color: #fff;

            &:active {
              background-color: #1565c0;
              transform: scale(0.95);
            }
          }
        }
      }
    }
  }
}
