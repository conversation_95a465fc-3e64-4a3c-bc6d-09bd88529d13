import {
  hotIndustryTypeSelectorApi, // 热点
  classicIndustryExpandListApi, // 经典
  originalIndustryClusterSelectorApi, // 产业图谱
  originalIndustryPurchasedListApi // 产业链图谱 - 已购买的产业链
} from '../../../service/industryApi';

// Tab 选项配置
const TAB_OPTIONS = [
  {
    code: 'hot',
    name: '热点产业名单'
  },
  {
    code: 'classic',
    name: '经典产业名单'
  },
  {
    code: 'emerging',
    name: '产业链图谱'
  }
];

// 移除全局变量，数据将存储在组件的 data 中
const app = getApp();

Component({
  properties: {
    visible: {
      type: Boolean,
      value: false
    },
    position: {
      type: String,
      value: 'top'
    },
    startDistance: {
      type: String,
      value: '0px'
    },
    defaultCode: {
      type: null,
      value: ''
    },
    top: {
      type: Number,
      value: 0
    },
    zIndex: {
      type: Number,
      value: 10
    }
  },

  data: {
    parentList: [],
    activeChildList: [],
    selectedPath: {
      parent: null,
      child: null
    },
    finalSelection: null,
    readyToShow: false,
    tabs: TAB_OPTIONS,
    activeTabIndex: 0, // 默认选中第一个tab（热点产业名单）
    currentDataSource: [],
    // 数据源映射
    dataSourceMap: {
      hot: [],
      classic: [],
      emerging: []
    },
    // 状态记忆：只记录已确认的选择（点击第二级时确认）
    confirmedSelection: {
      tabIndex: 0, // 已确认选择的Tab索引
      selectedCode: null, // 已确认选择的code
      hasConfirmed: false // 是否有已确认的选择
    }
  },

  lifetimes: {
    async attached() {
      this.setData({readyToShow: false});

      // 初始化数据源 - 从 API 获取真实数据
      await this.initializeDataSources();

      // 使用默认数据源初始化组件
      this.initializeData(this.data.currentDataSource);
    }
  },

  observers: {
    visible: function (bl) {
      if (bl) {
        const {confirmedSelection} = this.data;

        // 确保数据已初始化
        if (this.data.parentList.length === 0) {
          this.initializeData(this.data.currentDataSource);
        }

        // 优先级：defaultCode > confirmedSelection > 默认显示
        if (this.data.defaultCode) {
          // 有传入defaultCode，使用defaultCode
          this.setData({readyToShow: false});
          const code = this.extractCodeFromDefaultCode(this.data.defaultCode);
          if (code) {
            this.setDefaultSelectWithTab(code);
          } else {
            this.setData({readyToShow: true});
          }
        } else if (
          confirmedSelection.hasConfirmed &&
          confirmedSelection.selectedCode
        ) {
          // 没有defaultCode但有已确认的选择，回填已确认的选择
          this.setData({readyToShow: false});
          // 切换到已确认选择的Tab
          if (confirmedSelection.tabIndex !== this.data.activeTabIndex) {
            const {dataSourceMap} = this.data;
            this.setData({
              activeTabIndex: confirmedSelection.tabIndex,
              currentDataSource:
                dataSourceMap[TAB_OPTIONS[confirmedSelection.tabIndex].code]
            });
            this.initializeData(this.data.currentDataSource);
          }
          // 回填选择状态，并同时更新临时选择
          setTimeout(() => {
            this.setDefaultSelect(confirmedSelection.selectedCode);
            // 回填时，将已确认选择赋值给临时选择
            this.setData({
              'tempSelection.tabIndex': confirmedSelection.tabIndex,
              'tempSelection.selectedCode': confirmedSelection.selectedCode,
              'tempSelection.hasSelection': true
            });
          }, 50);
        } else {
          // 没有任何已确认选择，显示默认状态
          this.setData({readyToShow: true});
        }
      }
    },
    defaultCode: function (code) {
      if (code && this.data.visible) {
        this.setData({readyToShow: false});
        const extractedCode = this.extractCodeFromDefaultCode(code);
        // 只有当提取出有效的code时才设置默认选择
        if (extractedCode) {
          this.setDefaultSelectWithTab(extractedCode);
        } else {
          // 没有有效code时直接显示
          this.setData({readyToShow: true});
        }
      }
    }
  },

  methods: {
    /**
     * 初始化数据源 - 从 API 获取真实数据
     */
    async initializeDataSources() {
      try {
        // 并行请求四个 API
        const [hotData, classicData, originalData, purchasedData] =
          await Promise.all([
            hotIndustryTypeSelectorApi(),
            classicIndustryExpandListApi(),
            originalIndustryClusterSelectorApi(),
            originalIndustryPurchasedListApi()
          ]);

        // 格式化数据并更新组件数据
        const dataSourceMap = {
          hot: this.formatApiDataToComponentFormat(hotData, 'hot'),
          classic: this.formatApiDataToComponentFormat(classicData, 'classic'),
          emerging: [
            ...this.formatApiDataToComponentFormat(
              [
                {
                  hot_industrial_name: '已购买',
                  chains: purchasedData
                }
              ],
              'chainMap'
            ),
            ...this.formatApiDataToComponentFormat(originalData, 'chainMap')
          ]
        };

        // 更新组件数据
        this.setData({
          dataSourceMap,
          currentDataSource: dataSourceMap.hot // 默认使用热点数据
        });
        return dataSourceMap;
      } catch (error) {
        console.error('初始化数据源失败:', error);
        // 如果 API 失败，使用空数据
        const emptyDataSourceMap = {
          hot: [],
          classic: [],
          emerging: []
        };

        this.setData({
          dataSourceMap: emptyDataSourceMap,
          currentDataSource: []
        });

        return emptyDataSourceMap;
      }
    },

    /**
     * 格式化 API 数据为组件需要的格式
     * @param {Array} apiData - API 返回的数据
     * @returns {Array} 格式化后的数据
     */
    formatApiDataToComponentFormat(apiData, type) {
      if (!Array.isArray(apiData)) {
        return [];
      }
      return apiData.map(item => {
        const mainName =
          item.hot_industrial_name || item.classic_type_name || item.chain_name;
        const children = item?.chains || item?.child || item?.list;
        return {
          code: item?.code || item?.chain_code || String(item.classic_type_id),
          name: mainName,
          type,
          children: Array.isArray(children)
            ? children.map(chain => {
                const tempObj = {
                  code: `${chain?.code || chain?.chain_code || chain.id}`,
                  name:
                    chain?.name ||
                    chain?.chain_name ||
                    chain.classic_industrial_name,
                  type
                };
                if (Object.keys(chain).includes('purchased')) {
                  tempObj.purchased = chain.purchased;
                }
                return tempObj;
              })
            : []
        };
      });
    },

    // 从不同格式的defaultCode中提取code值（支持字符串、对象、数组格式）
    extractCodeFromDefaultCode(defaultCode) {
      if (!defaultCode) return '';
      if (typeof defaultCode === 'string') {
        return defaultCode;
      }
      if (Array.isArray(defaultCode)) {
        if (defaultCode.length === 0) return '';
        const firstItem = defaultCode[0];
        if (typeof firstItem === 'string') {
          return firstItem;
        }
        if (typeof firstItem === 'object' && firstItem.code) {
          return firstItem.code;
        }
        return '';
      }
      if (typeof defaultCode === 'object' && defaultCode.code) {
        return defaultCode.code;
      }
      return '';
    },

    // 初始化数据源：设置父级列表，默认激活第一个父级并显示其子项（避免右侧空白）
    initializeData(allData) {
      // 1. 为每个父级项添加选择状态标记，第一个设为激活状态
      const parentList = allData.map((item, index) => ({
        ...item,
        selected: false, // 是否被最终选中（红色高亮+勾选图标）
        active: index === 0 // 第一个父级默认激活（蓝色高亮，展开子列表）
      }));

      // 2. 获取第一个父级的子列表（如果存在），确保右侧有内容显示
      const firstParent = parentList[0];
      const activeChildList =
        firstParent && firstParent.children
          ? firstParent.children.map(child => ({
              ...child,
              selected: false, // 子项选择状态
              active: false // 子项激活状态
            }))
          : [];

      // 3. 重置组件的所有状态数据
      this.setData({
        parentList, // 左侧父级列表
        activeChildList, // 右侧子级列表（显示第一个父级的子项）
        selectedPath: {
          // 选择路径：记录用户的选择层级
          parent: null, // 选中的父级
          child: null // 选中的子级
        },
        finalSelection: null, // 最终选择结果（用于提交）
        readyToShow: true // 数据准备完毕，可以显示界面
      });
    },

    // Tab 切换处理：切换数据源并重新初始化，根据逻辑决定是否恢复选择
    switchTab(e) {
      const {index} = e.currentTarget.dataset;
      const tabCode = TAB_OPTIONS[index].code; // 获取Tab对应的数据源key
      const {dataSourceMap} = this.data;
      const newDataSource = dataSourceMap[tabCode]; // 获取新的数据源
      const {tempSelection} = this.data;

      // 更新Tab状态和数据源
      this.setData({
        activeTabIndex: index, // 更新当前激活的Tab索引
        currentDataSource: newDataSource, // 更新当前数据源
        readyToShow: false // 暂时隐藏内容，等待数据初始化完成
      });

      // 重新初始化数据（显示第一个父级的子项）
      this.initializeData(newDataSource);

      // Tab切换后的显示逻辑：
      // 1. 如果临时选择就在当前Tab，显示临时选择状态
      // 2. 否则，显示默认状态（第一个父级激活，无选中）
      // 注意：不在Tab切换时恢复已确认选择，只在弹窗打开时回填
      if (
        tempSelection?.hasSelection &&
        tempSelection.tabIndex === index &&
        tempSelection.selectedCode
      ) {
        // 临时选择就在当前Tab，恢复临时选择状态
        setTimeout(() => {
          this.setDefaultSelect(tempSelection.selectedCode);
        }, 50);
      }
      // 如果临时选择不在当前Tab，就保持默认状态（第一个父级激活，无选中）
    },

    // 智能默认选择：根据defaultCode自动查找所属Tab并切换，然后设置选中状态
    setDefaultSelectWithTab(targetCode) {
      if (!targetCode) return;

      // 在所有数据源中查找匹配的code（支持父级和子级）
      let foundTab = null; // 找到的Tab索引
      let foundData = null; // 找到的数据（包含parent和child信息）

      // 遍历所有Tab的数据源
      const {dataSourceMap} = this.data;
      for (let tabIndex = 0; tabIndex < TAB_OPTIONS.length; tabIndex++) {
        const tabCode = TAB_OPTIONS[tabIndex].code;
        const dataSource = dataSourceMap[tabCode];

        // 在当前数据源中查找匹配的code
        for (let parent of dataSource) {
          // 检查是否匹配父级code
          if (parent.code === targetCode) {
            foundTab = tabIndex;
            foundData = {parent, child: null}; // 匹配父级
            break;
          }
          // 检查是否匹配子级code
          if (parent.children) {
            for (let child of parent.children) {
              if (child.code === targetCode) {
                foundTab = tabIndex;
                foundData = {parent, child}; // 匹配子级
                break;
              }
            }
            if (foundData) break;
          }
        }
        if (foundData) break; // 找到匹配项，跳出外层循环
      }

      if (foundTab !== null && foundData) {
        // 切换到对应的Tab
        const {dataSourceMap} = this.data;
        this.setData({
          activeTabIndex: foundTab,
          currentDataSource: dataSourceMap[TAB_OPTIONS[foundTab].code]
        });

        // 先初始化数据
        this.initializeData(this.data.currentDataSource);

        // 延迟设置默认选择（确保数据初始化完成）
        setTimeout(() => {
          this.setDefaultSelect(targetCode);
        }, 50);
      } else {
        // 没找到匹配项，直接显示
        this.setData({readyToShow: true});
      }
    },

    // 设置默认选中项：在当前数据源中查找并选中指定code的项目（仅用于恢复状态，不更新临时选择）
    setDefaultSelect(targetCode) {
      if (!targetCode) return;
      const {parentList} = this.data;
      let selectedPath = {
        parent: null,
        child: null
      };

      // 在当前父级列表中查找匹配的code
      for (let parent of parentList) {
        // 检查父级是否匹配
        if (parent.code === targetCode) {
          selectedPath.parent = parent;
          // 对于一级code（父级code），需要特殊处理
          this._selectParentItemForDefaultCode(parent);
          break;
        }
        // 检查子级是否匹配
        if (parent.children) {
          for (let child of parent.children) {
            if (child.code === targetCode) {
              selectedPath.parent = parent;
              selectedPath.child = child;
              this._selectParentItemForRestore(parent); // 先恢复父级UI
              this._selectChildItemForRestore(child, parent); // 再恢复子级UI
              break;
            }
          }
          if (selectedPath.child) break; // 找到子级匹配，跳出外层循环
        }
      }

      // 更新选择状态和最终结果
      this.setData({
        selectedPath, // 更新选择路径
        finalSelection: this.getFinalSelection(selectedPath), // 更新最终选择结果
        readyToShow: true // 显示界面
      });
    },

    // 专门处理defaultCode为一级code（父级code）的情况
    _selectParentItemForDefaultCode(selectedParent) {
      let {parentList} = this.data;

      // 重置所有父级的状态
      parentList = parentList.map(item => ({
        ...item,
        selected: false,
        active: false
      }));

      // 设置当前父级为选中和激活状态
      const parentIndex = parentList.findIndex(
        item => item.code === selectedParent.code
      );
      if (parentIndex !== -1) {
        parentList[parentIndex].selected = true;
        parentList[parentIndex].active = true;
      }

      // 获取选中父级的子列表
      const activeChildList = selectedParent.children
        ? selectedParent.children.map(child => ({
            ...child,
            selected: false
          }))
        : [];

      // 更新UI状态，并更新临时选择（因为这是defaultCode传入的选择）
      this.setData({
        parentList,
        activeChildList,
        // 更新临时选择状态，记录当前的一级选择
        'tempSelection.tabIndex': this.data.activeTabIndex,
        'tempSelection.selectedCode': selectedParent.code,
        'tempSelection.hasSelection': true
      });
    },

    // 仅用于恢复状态的父级选择方法（不更新临时选择）
    _selectParentItemForRestore(selectedParent) {
      let {parentList} = this.data;

      // 重置所有父级的状态
      parentList = parentList.map(item => ({
        ...item,
        selected: false,
        active: false
      }));

      // 设置当前父级为选中和激活状态
      const parentIndex = parentList.findIndex(
        item => item.code === selectedParent.code
      );
      if (parentIndex !== -1) {
        parentList[parentIndex].selected = true;
        parentList[parentIndex].active = true;
      }

      // 获取选中父级的子列表
      const activeChildList = selectedParent.children
        ? selectedParent.children.map(child => ({
            ...child,
            selected: false
          }))
        : [];

      // 只更新UI，不更新临时选择
      this.setData({
        parentList,
        activeChildList
      });
    },

    // 仅用于恢复状态的子级选择方法（不更新临时选择）
    _selectChildItemForRestore(selectedChild, parentOfChild) {
      let {activeChildList, parentList} = this.data;

      // 更新子级状态
      activeChildList = activeChildList.map(item => ({
        ...item,
        selected: item.code === selectedChild.code
      }));

      // 更新父级状态
      parentList = parentList.map(item => ({
        ...item,
        selected: item.code === parentOfChild.code,
        active: item.code === parentOfChild.code
      }));

      // 只更新UI，不更新临时选择
      this.setData({
        parentList,
        activeChildList
      });
    },

    // 用户点击父级项的事件处理
    selectParent(e) {
      const dataset = e.currentTarget.dataset;
      const {index} = dataset; // 获取点击的父级索引
      const {parentList} = this.data;
      const selectedParent = parentList[index]; // 获取选中的父级对象
      this.selectParentItem(selectedParent); // 调用选中父级的核心逻辑
    },

    // 选中父级项的核心逻辑：展开子列表，设置红色字体但不显示勾勾
    selectParentItem(selectedParent) {
      let {parentList} = this.data;

      // 重置所有父级的状态
      parentList = parentList.map(item => ({
        ...item,
        selected: false, // 清除选中状态
        active: false // 清除激活状态
      }));

      // 设置当前父级为选中和激活状态（红色字体 + 展开子列表）
      const parentIndex = parentList.findIndex(
        item => item.code === selectedParent.code
      );
      if (parentIndex !== -1) {
        parentList[parentIndex].selected = true; // 设为选中（红色字体）
        parentList[parentIndex].active = true; // 设为激活（展开子列表）
      }

      // 获取选中父级的子列表（右侧显示）
      const activeChildList = selectedParent.children
        ? selectedParent.children.map(child => ({
            ...child,
            selected: false // 重置所有子项的选择状态
          }))
        : [];

      // 更新组件状态和临时选择
      this.setData({
        parentList, // 更新父级列表状态
        activeChildList, // 更新右侧子级列表
        // 记录临时选择（点击第一级也算临时选择）
        'tempSelection.tabIndex': this.data.activeTabIndex,
        'tempSelection.selectedCode': selectedParent.code,
        'tempSelection.hasSelection': true
      });
    },

    // 用户点击子级项的事件处理
    selectChild(e) {
      const dataset = e.currentTarget.dataset;
      const {code, purchased} = dataset; // 获取点击的子级code
      if (!purchased && this.data.activeTabIndex === 2) {
        // 说明当前没有购买 不可以查看
        app.showToast('请解锁该产业链图谱后使用', 'none');
        return;
      }

      const {activeChildList} = this.data;
      const selectedChild = activeChildList.find(item => item.code === code); // 查找对应的子级对象
      if (selectedChild) {
        this.selectChildItem(selectedChild); // 调用选中子级的核心逻辑
      }
    },

    // 选中子级项的核心逻辑：显示选中状态，然后关闭弹窗并通知外部
    selectChildItem(selectedChild) {
      let {parentList, activeChildList, activeTabIndex, tabs} = this.data;

      // 找到子级对应的父级
      let parentOfChild = null;
      for (let parent of parentList) {
        if (
          parent.children &&
          parent.children.some(child => child.code === selectedChild.code)
        ) {
          parentOfChild = parent;
          break;
        }
      }

      if (parentOfChild) {
        // 先更新UI显示选中状态
        activeChildList = activeChildList.map(item => ({
          ...item,
          selected: item.code === selectedChild.code
        }));

        // 更新父级状态
        parentList = parentList.map(item => ({
          ...item,
          selected: item.code === parentOfChild.code,
          active: item.code === parentOfChild.code
        }));

        // 立即更新UI显示选中效果
        this.setData({
          parentList,
          activeChildList
        });

        // 构造选择路径
        const selectedPath = {
          parent: parentOfChild,
          child: selectedChild
        };

        // 构造返回的选择结果
        const selectionWithParent = {...selectedChild};
        selectionWithParent.parent = parentOfChild.code;

        // 添加当前Tab信息到路径中
        const currentTab = tabs[activeTabIndex];
        const pathWithTab = {
          ...selectedPath,
          tabCode: currentTab.code,
          tabName: currentTab.name
        };

        // 更新临时选择和已确认选择（点击第二级相当于确定按钮）
        this.setData({
          // 更新临时选择
          'tempSelection.tabIndex': activeTabIndex,
          'tempSelection.selectedCode': selectedChild.code,
          'tempSelection.hasSelection': true,
          // 同时更新已确认选择（点击第二级等于确认）
          'confirmedSelection.tabIndex': activeTabIndex,
          'confirmedSelection.selectedCode': selectedChild.code,
          'confirmedSelection.hasConfirmed': true
        });

        // 短暂延迟后关闭弹窗，让用户看到选中效果
        setTimeout(() => {
          // 触发选择事件，通知外部
          this.triggerEvent('submit', {
            selection: selectionWithParent,
            path: pathWithTab
          });

          // 关闭弹窗
          this.triggerEvent('close');
        }, 150); // 150ms延迟，让用户看到选中效果
      }
    },

    // 获取最终选择结果：优先返回子级，如果没有子级则返回父级
    getFinalSelection(path = null) {
      const selectedPath = path || this.data.selectedPath;
      return selectedPath.child || selectedPath.parent; // 子级优先级高于父级
    },

    // 重置组件状态：清除所有选择，恢复到初始状态（第一个父级激活）
    reset() {
      // 清除选择路径和最终结果
      this.setData({
        selectedPath: {
          parent: null, // 清除父级选择
          child: null // 清除子级选择
        },
        finalSelection: null // 清除最终选择结果
      });

      const {parentList} = this.data;
      // 重置父级列表状态，第一个父级保持激活（确保右侧有内容显示）
      const resetParentList = parentList.map((item, index) => ({
        ...item,
        selected: false, // 清除选中状态
        active: index === 0 // 第一个父级保持激活状态（蓝色高亮）
      }));

      // 重置时也显示第一个父级的子列表（避免右侧空白）
      const firstParent = resetParentList[0];
      const activeChildList =
        firstParent && firstParent.children
          ? firstParent.children.map(child => ({
              ...child,
              selected: false, // 清除子级选中状态
              active: false // 清除子级激活状态
            }))
          : [];

      // 更新组件状态
      this.setData({
        parentList: resetParentList, // 更新父级列表
        activeChildList // 更新子级列表（显示第一个父级的子项）
      });
    },

    // 关闭弹窗：重置临时选择，保持已确认选择不变（相当于取消操作）
    close() {
      // 清除临时选择（相当于取消当前操作）
      this.setData({
        'tempSelection.tabIndex': 0,
        'tempSelection.selectedCode': null,
        'tempSelection.hasSelection': false
      });

      this.reset(); // 重置组件UI状态
      this.triggerEvent('close'); // 触发关闭事件，通知父组件
    },

    // 清除所有选择记录（可供外部调用，用于重置组件状态）
    clearAllSelections() {
      this.setData({
        'confirmedSelection.tabIndex': 0,
        'confirmedSelection.selectedCode': null,
        'confirmedSelection.hasConfirmed': false,
        'tempSelection.tabIndex': 0,
        'tempSelection.selectedCode': null,
        'tempSelection.hasSelection': false
      });
    }
  }
});
