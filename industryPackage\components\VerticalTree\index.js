// industryPackage/components/VerticalTree/index.js - 核心逻辑

// 导入依赖
const {canvasMixin} = require('./mixins/canvasMixin.js');
const {drawMixin} = require('./mixins/drawMixin.js');
const {utilsMixin} = require('./mixins/utilsMixin.js');
const {exportMixin} = require('./mixins/exportMixin.js');

// 导入所有常量配置
const {
  LAYOUT_CONSTANTS,
  NODE_STYLE_CONSTANTS,
  EVENT_CONSTANTS,
  DEFAULT_DATA
} = require('./config/constants.js');

Component({
  // 组件的属性列表
  properties: {
    treeData: {
      type: Array,
      value: DEFAULT_DATA.TREE_DATA,
      observer: '_onTreeDataChange' // 监听数据变化
    },
    canvasHeight: {
      type: Number,
      value: DEFAULT_DATA.CANVAS_HEIGHT
    },
    // 新增配置属性
    enableDrag: {
      type: Boolean,
      value: true
    },
    enableCache: {
      type: Boolean,
      value: true
    },
    debugMode: {
      type: Boolean,
      value: false
    },
    // 新增：是否启用节点点击功能
    enableNodeClick: {
      type: Boolean,
      value: true
    },
    purchased: {
      //是否购买-定制需求
      type: Boolean,
      value: true
    }
  },

  // 组件的初始数据
  data: {
    ...canvasMixin.data,
    ...utilsMixin.data,
    ...exportMixin.data,
    // 拖拽相关数据
    translateX: 0,
    translateY: 0,
    lastTouchX: 0,
    lastTouchY: 0,
    isDragging: false,
    // 性能监控数据
    _renderCount: 0,
    _lastRenderTime: 0,
    // 组件状态管理
    _isDestroyed: false,
    _isVisible: true
  },

  // 组件的方法列表
  methods: {
    // 合并所有mixin的方法
    ...canvasMixin.methods,
    ...drawMixin.methods,
    ...utilsMixin.methods,
    ...exportMixin.methods,

    // 获取节点样式配置（统一优化版本）
    getNodeStyle(level) {
      // 初始化样式缓存（在组件实例上，不在data中）
      if (!this._styleCache) {
        this._styleCache = new Map();
      }

      const cacheKey = `nodeStyle_${level}`;

      if (this._styleCache.has(cacheKey)) {
        return this._styleCache.get(cacheKey);
      }

      let style;
      switch (level) {
        case LAYOUT_CONSTANTS.TREE_LEVELS.ROOT:
          style = this._createRootNodeStyle();
          break;
        case LAYOUT_CONSTANTS.TREE_LEVELS.FIRST:
          style = this._createFirstLevelNodeStyle();
          break;
        case LAYOUT_CONSTANTS.TREE_LEVELS.SECOND:
          style = this._createSecondLevelNodeStyle();
          break;
        case LAYOUT_CONSTANTS.TREE_LEVELS.THIRD:
          style = this._createThirdLevelNodeStyle();
          break;
        default:
          // 第4层及以后的所有层级都使用第4层样式（支持无限层级）
          if (level >= LAYOUT_CONSTANTS.TREE_LEVELS.FOURTH) {
            style = this._createFourthLevelNodeStyle();
          } else {
            style = this._createSecondLevelNodeStyle(); // 兜底默认样式
          }
      }

      this._styleCache.set(cacheKey, style);
      return style;
    },

    // 创建根节点样式
    _createRootNodeStyle() {
      const config = NODE_STYLE_CONSTANTS.ROOT;
      return {
        backgroundColor: config.BACKGROUND_COLOR,
        borderRadius: this.rpxToPx(config.BORDER_RADIUS),
        fontSize: this.rpxToPx(config.FONT_SIZE),
        fontWeight: config.FONT_WEIGHT,
        color: config.COLOR,
        width: this.rpxToPx(config.WIDTH),
        paddingX: this.rpxToPx(config.PADDING_X),
        paddingY: this.rpxToPx(config.PADDING_Y),
        minHeight: this.rpxToPx(config.MIN_HEIGHT)
      };
    },

    // 创建第一级节点样式
    _createFirstLevelNodeStyle() {
      const config = NODE_STYLE_CONSTANTS.FIRST_LEVEL;
      return {
        backgroundColor: config.BACKGROUND_COLOR,
        borderRadius: this.rpxToPx(config.BORDER_RADIUS),
        fontSize: this.rpxToPx(config.FONT_SIZE),
        countFontSize: this.rpxToPx(config.COUNT_FONT_SIZE),
        fontWeight: config.FONT_WEIGHT,
        color: config.COLOR,
        countColor: config.COUNT_COLOR,
        padding: this.rpxToPx(config.PADDING),
        textGap: this.rpxToPx(config.TEXT_GAP),
        minWidth: this.rpxToPx(config.MIN_WIDTH),
        minHeight: this.rpxToPx(config.MIN_HEIGHT),
        iconPath: config.ICON_PATH,
        iconSize: this.rpxToPx(config.ICON_SIZE),
        expandIconPath: config.EXPAND_ICON_PATH,
        collapseIconPath: config.COLLAPSE_ICON_PATH,
        borderWidth: 0,
        borderColor: '#E5E5E5'
      };
    },

    // 创建第二级节点样式
    _createSecondLevelNodeStyle() {
      const config = NODE_STYLE_CONSTANTS.SECOND_LEVEL;
      return {
        backgroundColor: config.BACKGROUND_COLOR,
        borderRadius: this.rpxToPx(config.BORDER_RADIUS),
        fontSize: this.rpxToPx(config.FONT_SIZE),
        countFontSize: this.rpxToPx(config.COUNT_FONT_SIZE),
        fontWeight: config.FONT_WEIGHT,
        color: config.COLOR,
        countColor: config.COUNT_COLOR,
        paddingX: this.rpxToPx(config.PADDING_X),
        paddingY: this.rpxToPx(config.PADDING_Y),
        textGap: this.rpxToPx(config.TEXT_GAP),
        minWidth: this.rpxToPx(config.MIN_WIDTH),
        minHeight: this.rpxToPx(config.MIN_HEIGHT),
        iconSize: this.rpxToPx(config.ICON_SIZE),
        expandIconPath: config.EXPAND_ICON_PATH,
        collapseIconPath: config.COLLAPSE_ICON_PATH
      };
    },

    // 创建第三级节点样式
    _createThirdLevelNodeStyle() {
      const config = NODE_STYLE_CONSTANTS.THIRD_LEVEL;
      return {
        backgroundColor: config.BACKGROUND_COLOR,
        borderRadius: this.rpxToPx(config.BORDER_RADIUS),
        fontSize: this.rpxToPx(config.FONT_SIZE),
        countFontSize: this.rpxToPx(config.COUNT_FONT_SIZE),
        fontWeight: config.FONT_WEIGHT,
        color: config.COLOR,
        countColor: config.COUNT_COLOR,
        paddingX: this.rpxToPx(config.PADDING_X),
        paddingY: this.rpxToPx(config.PADDING_Y),
        textGap: this.rpxToPx(config.TEXT_GAP),
        minWidth: this.rpxToPx(config.MIN_WIDTH),
        minHeight: this.rpxToPx(config.MIN_HEIGHT),
        iconSize: this.rpxToPx(config.ICON_SIZE),
        expandIconPath: config.EXPAND_ICON_PATH,
        collapseIconPath: config.COLLAPSE_ICON_PATH
      };
    },

    // 创建第四级节点样式
    _createFourthLevelNodeStyle() {
      const config = NODE_STYLE_CONSTANTS.FOURTH_LEVEL;
      return {
        backgroundColor: config.BACKGROUND_COLOR,
        borderRadius: this.rpxToPx(config.BORDER_RADIUS),
        fontSize: this.rpxToPx(config.FONT_SIZE),
        countFontSize: this.rpxToPx(config.COUNT_FONT_SIZE),
        fontWeight: config.FONT_WEIGHT,
        color: config.COLOR,
        countColor: config.COUNT_COLOR,
        paddingX: this.rpxToPx(config.PADDING_X),
        paddingY: this.rpxToPx(config.PADDING_Y),
        textGap: this.rpxToPx(config.TEXT_GAP),
        minWidth: this.rpxToPx(config.MIN_WIDTH),
        minHeight: this.rpxToPx(config.MIN_HEIGHT),
        iconSize: this.rpxToPx(config.ICON_SIZE),
        expandIconPath: config.EXPAND_ICON_PATH,
        collapseIconPath: config.COLLAPSE_ICON_PATH
      };
    },

    // 兼容性方法（保持向后兼容）
    getRootNodeStyle() {
      return this.getNodeStyle(LAYOUT_CONSTANTS.TREE_LEVELS.ROOT);
    },

    getFirstLevelNodeStyle() {
      return this.getNodeStyle(LAYOUT_CONSTANTS.TREE_LEVELS.FIRST);
    },

    getSecondLevelNodeStyle() {
      return this.getNodeStyle(LAYOUT_CONSTANTS.TREE_LEVELS.SECOND);
    },

    getThirdLevelNodeStyle() {
      return this.getNodeStyle(LAYOUT_CONSTANTS.TREE_LEVELS.THIRD);
    },

    getFourthLevelNodeStyle() {
      return this.getNodeStyle(LAYOUT_CONSTANTS.TREE_LEVELS.FOURTH);
    },

    // 获取实际Canvas宽度（px单位）- 优化版本
    getActualCanvasWidth() {
      // 优先使用缓存的实际宽度
      if (this.data.actualCanvasWidth) {
        return this.data.actualCanvasWidth;
      }

      // 尝试从Canvas对象获取
      if (this.canvas && this.canvas.width) {
        try {
          const dpr = this._getDevicePixelRatio();
          return this.canvas.width / dpr;
        } catch (error) {
          console.warn('获取Canvas宽度失败:', error);
        }
      }

      // 降级到属性值或默认值
      return this.rpxToPx(this.properties.canvasWidth || 750);
    },

    // 获取实际Canvas高度（px单位）- 优化版本
    getActualCanvasHeight() {
      // 优先使用缓存的实际高度
      if (this.data.actualCanvasHeight) {
        return this.data.actualCanvasHeight;
      }

      // 尝试从Canvas对象获取
      if (this.canvas && this.canvas.height) {
        try {
          const dpr = this._getDevicePixelRatio();
          return this.canvas.height / dpr;
        } catch (error) {
          console.warn('获取Canvas高度失败:', error);
        }
      }

      // 降级到属性值或默认值
      return this.rpxToPx(
        this.properties.canvasHeight || DEFAULT_DATA.CANVAS_HEIGHT
      );
    },

    // 获取设备像素比（优化版本）
    _getDevicePixelRatio() {
      // 使用缓存避免重复调用
      if (this._cachedPixelRatio) {
        return this._cachedPixelRatio;
      }

      try {
        // 优先使用新的API
        if (wx.getDeviceInfo) {
          const deviceInfo = wx.getDeviceInfo();
          this._cachedPixelRatio = deviceInfo.pixelRatio || 2;
        } else if (wx.getWindowInfo) {
          const windowInfo = wx.getWindowInfo();
          this._cachedPixelRatio = windowInfo.pixelRatio || 2;
        } else if (wx.getSystemInfoSync) {
          // 降级到旧API
          const systemInfo = wx.getSystemInfoSync();
          this._cachedPixelRatio = systemInfo.pixelRatio || 2;
        } else {
          this._cachedPixelRatio = 2;
        }
      } catch (error) {
        console.warn('获取设备像素比失败:', error);
        this._cachedPixelRatio = 2; // 默认值
      }

      return this._cachedPixelRatio;
    },

    // 计算整个树的总宽度（动态支持多层级版本）
    calculateTotalTreeWidth() {
      // 使用缓存避免重复计算
      if (this._totalTreeWidthCache) {
        return this._totalTreeWidthCache;
      }

      const layoutConfig = this._getLayoutConfig();

      // 计算当前数据的最大深度
      const maxDepth = this._calculateMaxTreeDepth();

      // 动态计算各级节点宽度
      let totalWidth = 0;

      for (let level = 0; level <= maxDepth; level++) {
        let levelWidth;

        // 根据层级获取对应的最小宽度
        if (level === 0) {
          levelWidth = this.rpxToPx(NODE_STYLE_CONSTANTS.ROOT.WIDTH);
        } else if (level === 1) {
          levelWidth = this.rpxToPx(NODE_STYLE_CONSTANTS.FIRST_LEVEL.MIN_WIDTH);
        } else if (level === 2) {
          levelWidth = this.rpxToPx(
            NODE_STYLE_CONSTANTS.SECOND_LEVEL.MIN_WIDTH
          );
        } else if (level === 3) {
          levelWidth = this.rpxToPx(NODE_STYLE_CONSTANTS.THIRD_LEVEL.MIN_WIDTH);
        } else {
          // 第4层及以后都使用第4层的宽度
          levelWidth = this.rpxToPx(
            NODE_STYLE_CONSTANTS.FOURTH_LEVEL.MIN_WIDTH
          );
        }

        totalWidth += levelWidth;

        // 添加层级间距（最后一层不需要间距）
        if (level < maxDepth) {
          totalWidth +=
            layoutConfig.horizontalGap + layoutConfig.horizontalSegment;
        }
      }

      this._totalTreeWidthCache = totalWidth;
      return totalWidth;
    },

    // 计算树的最大深度
    _calculateMaxTreeDepth() {
      const treeData = this.properties.treeData;
      if (!treeData || !Array.isArray(treeData)) {
        return 0;
      }

      const calculateDepth = (nodes, currentDepth = 0) => {
        let maxDepth = currentDepth;

        if (nodes && Array.isArray(nodes)) {
          nodes.forEach(node => {
            if (
              node.children &&
              Array.isArray(node.children) &&
              node.children.length > 0
            ) {
              const childDepth = calculateDepth(
                node.children,
                currentDepth + 1
              );
              maxDepth = Math.max(maxDepth, childDepth);
            }
          });
        }

        return maxDepth;
      };

      // 限制最大深度，防止无限递归
      const maxDepth = calculateDepth(treeData);
      return Math.min(maxDepth, LAYOUT_CONSTANTS.TREE_LEVELS.MAX_LEVEL || 7);
    },

    // 清理宽度缓存
    _clearWidthCache() {
      this._totalTreeWidthCache = null;
    },

    // 绘制树形图 - 核心方法（优化版本）
    drawTree() {
      // 检查组件是否已销毁
      if (this.data._isDestroyed) {
        console.warn('组件已销毁，跳过绘制');
        return;
      }

      if (!this._validateDrawConditions()) {
        return;
      }

      // 防止重复绘制
      if (this._isDrawing) {
        return;
      }

      this._isDrawing = true;
      const startTime = Date.now();
      this._incrementRenderCount();

      try {
        // 数据预处理
        const treeData = this._prepareTreeData();
        if (!treeData || treeData.length === 0) {
          console.warn('树形数据为空，跳过绘制');
          this._isDrawing = false;
          return;
        }

        // 清空画布
        this._clearCanvas();

        // 保存Canvas状态
        this._saveCanvasState();

        // 计算布局（带缓存）
        const layout = this.calculateTreeLayout(treeData);
        this.currentLayout = layout;

        // 绘制内容
        this._renderTreeContent(layout);

        // 在绘制完成后进行居中（确保Canvas已经完全准备好）
        // 只在首次绘制或明确需要居中时才执行
        if (!this._hasBeenCentered || this._needsRecentering) {
          this._centerTreeAfterRender(layout);
          this._needsRecentering = false;
        }

        // 恢复Canvas状态
        this._restoreCanvasState();

        // 性能统计
        this._recordRenderTime(startTime);
      } catch (error) {
        console.error('绘制树形图失败:', error);
        this._handleDrawError(error);
      } finally {
        this._isDrawing = false;
      }
    },

    // 节流版本的绘制方法（增强防抖）
    _throttledDrawTree() {
      // 检查组件状态
      if (this.data._isDestroyed) {
        return;
      }

      // 如果已经有待执行的绘制任务，取消它
      if (this._drawTreeTimer) {
        clearTimeout(this._drawTreeTimer);
      }

      // 防抖延迟，避免频繁调用
      const debounceDelay = this.properties.debugMode ? 0 : 16; // 约60fps

      this._drawTreeTimer = setTimeout(() => {
        // 再次检查组件状态
        if (this.data._isDestroyed) {
          return;
        }

        // 使用wx.nextTick + requestAnimationFrame确保在合适的时机绘制
        wx.nextTick(() => {
          this._requestAnimationFrame(() => {
            if (!this.data._isDestroyed) {
              this.drawTree();
            }
            this._drawTreeTimer = null;
          });
        });
      }, debounceDelay);
    },

    // 节流版本的setData方法（增强版）
    _throttledSetData(data, immediate = false) {
      // 检查组件状态
      if (this.data._isDestroyed) {
        return;
      }

      // 如果是立即更新，直接执行
      if (immediate) {
        this.setData(data);
        return;
      }

      // 合并待更新的数据
      if (!this._pendingSetData) {
        this._pendingSetData = {};
      }
      Object.assign(this._pendingSetData, data);

      // 如果已经有待执行的setData任务，不重复创建
      if (this._setDataTimer) {
        return;
      }

      // 使用较短的延迟进行批量更新
      this._setDataTimer = setTimeout(() => {
        if (this._pendingSetData && !this.data._isDestroyed) {
          try {
            this.setData(this._pendingSetData);
          } catch (error) {
            console.error('setData执行失败:', error);
          }
          this._pendingSetData = null;
        }
        this._setDataTimer = null;
      }, 8); // 8ms延迟，约120fps的更新频率
    },

    // 清理所有定时器（增强版）
    _clearTimers() {
      // 清理绘制相关定时器
      if (this._drawTreeTimer) {
        clearTimeout(this._drawTreeTimer);
        this._drawTreeTimer = null;
      }

      // 清理数据更新定时器
      if (this._setDataTimer) {
        clearTimeout(this._setDataTimer);
        this._setDataTimer = null;
      }

      // 清理移动相关定时器
      if (this._moveTimeout) {
        clearTimeout(this._moveTimeout);
        this._moveTimeout = null;
      }

      // 清理居中相关定时器
      if (this._centeringTimer) {
        clearTimeout(this._centeringTimer);
        this._centeringTimer = null;
      }

      // 清理可见性检查定时器
      if (this._visibilityTimer) {
        clearTimeout(this._visibilityTimer);
        this._visibilityTimer = null;
      }

      // 清理性能监控定时器
      if (this._performanceTimer) {
        clearTimeout(this._performanceTimer);
        this._performanceTimer = null;
      }

      // 清理图标加载相关状态
      this._iconPreloadingInProgress = false;
      this._iconDrawPending = false;
    },

    // 验证绘制条件
    _validateDrawConditions() {
      if (!this.ctx) {
        console.warn('Canvas上下文不存在');
        return false;
      }

      if (!this.isCanvasReady || !this.isCanvasReady()) {
        console.warn('Canvas未准备就绪');
        return false;
      }

      return true;
    },

    // 增加渲染计数（优化版本）
    _incrementRenderCount() {
      const newCount = this.data._renderCount + 1;

      // 使用节流更新，避免频繁setData
      this._throttledSetData({
        _renderCount: newCount
      });

      // 性能监控：如果渲染次数过多，发出警告
      if (this.properties.debugMode && newCount % 10 === 0) {
        console.warn(
          `VerticalTree渲染次数已达到 ${newCount} 次，请检查是否存在性能问题`
        );
        this._logPerformanceStats();
      }
    },

    // 记录性能统计信息
    _logPerformanceStats() {
      if (!this.properties.debugMode) return;

      const stats = {
        renderCount: this.data._renderCount,
        lastRenderTime: this.data._lastRenderTime,
        isDestroyed: this.data._isDestroyed,
        isVisible: this.data._isVisible,
        hasCache: !!this._layoutCache,
        cacheSize: this._styleCache ? this._styleCache.size : 0,
        iconCacheSize: this._preloadedIcons ? this._preloadedIcons.size : 0
      };

      // console.log('VerticalTree性能统计:', stats);
    },

    // 准备树形数据
    _prepareTreeData() {
      const rawData = this.properties.treeData;
      if (!rawData || !Array.isArray(rawData)) {
        console.warn('无效的树形数据:', rawData);
        return null;
      }
      return this.processTreeData(rawData);
    },

    // 清空画布
    _clearCanvas() {
      const width = this.getActualCanvasWidth();
      const height = this.getActualCanvasHeight();

      try {
        this.ctx.clearRect(0, 0, width, height);
      } catch (error) {
        console.error('清空画布失败:', error);
      }
    },

    // 保存Canvas状态
    _saveCanvasState() {
      if (!this.isLegacyCanvas) {
        try {
          this.ctx.save();
          // 应用拖拽偏移量
          if (this.properties.enableDrag) {
            this.ctx.translate(this.data.translateX, this.data.translateY);
          }
        } catch (error) {
          console.error('保存Canvas状态失败:', error);
        }
      }
    },

    // 渲染树形内容
    _renderTreeContent(layout) {
      // 绘制连接线
      this.drawConnections(layout);
      // 绘制节点
      this.drawNodes(layout);
    },

    // 恢复Canvas状态
    _restoreCanvasState() {
      try {
        if (!this.isLegacyCanvas) {
          this.ctx.restore();
        } else {
          this.ctx.draw();
        }
      } catch (error) {
        console.error('恢复Canvas状态失败:', error);
      }
    },

    // 记录渲染时间
    _recordRenderTime(startTime) {
      const renderTime = Date.now() - startTime;
      this.setData({
        _lastRenderTime: renderTime
      });
    },

    // 处理绘制错误
    _handleDrawError(error) {
      console.error('绘制错误详情:', {
        message: error.message,
        stack: error.stack,
        canvasReady: this.isCanvasReady ? this.isCanvasReady() : false,
        dataValid: !!this.properties.treeData
      });

      // 可以在这里添加错误恢复逻辑
    },

    // 计算树形布局 - 核心算法（优化版本，带缓存）
    calculateTreeLayout(data) {
      if (!data || !Array.isArray(data) || data.length === 0) {
        console.warn('布局计算：数据无效');
        return {nodes: [], connections: []};
      }

      // 检查组件状态
      if (this.data._isDestroyed) {
        return {nodes: [], connections: []};
      }

      // 生成缓存键
      const cacheKey = this._generateLayoutCacheKey(data);

      // 检查缓存
      if (this._layoutCache && this._layoutCache.key === cacheKey) {
        return this._layoutCache.layout;
      }

      const nodes = [];
      const connections = [];

      // 获取布局配置
      const layoutConfig = this._getLayoutConfig();
      const canvasHeight = this.getActualCanvasHeight();

      // 计算子树高度的辅助函数
      const calculateSubtreeHeight = (node, depth) => {
        const style = this.getNodeStyle(depth);
        const nodeHeight =
          style.height || this.calculateNodeHeight(node.name, style);

        // 如果节点没有子节点，或者节点是收起状态，只返回节点本身的高度
        if (
          !node.children ||
          node.children.length === 0 ||
          node.isExpanded === false
        ) {
          return nodeHeight;
        } else {
          const childrenHeights = node.children.map(child =>
            calculateSubtreeHeight(child, depth + 1)
          );
          const totalChildrenHeight =
            childrenHeights.reduce((sum, h) => sum + h, 0) +
            (childrenHeights.length - 1) * layoutConfig.verticalGap;

          return Math.max(nodeHeight, totalChildrenHeight);
        }
      };

      // 处理节点的递归函数
      const processNode = (
        node,
        depth,
        parentX,
        parentY,
        parentWidth,
        index,
        siblings
      ) => {
        const isRoot = depth === 0;
        const style = this.getNodeStyle(depth);

        // 计算节点尺寸
        const {width, height} = this._calculateNodeDimensions(
          node,
          style,
          depth
        );

        // 计算节点位置
        const {x, y} = this._calculateNodePosition(
          node,
          depth,
          parentX,
          parentY,
          parentWidth,
          index,
          siblings,
          width,
          height,
          canvasHeight,
          calculateSubtreeHeight,
          layoutConfig
        );

        // 创建节点信息
        const nodeInfo = {
          ...node,
          x,
          y,
          width,
          height,
          style,
          depth,
          isRoot,
          // 添加节点路径信息，用于精确匹配同名节点
          _nodePath: this._buildNodePath(node, depth, index, siblings)
        };
        nodes.push(nodeInfo);

        // 添加连接线
        if (!isRoot) {
          this._addConnection(
            connections,
            nodes,
            parentX,
            parentY,
            x,
            y,
            depth,
            layoutConfig
          );
        }

        // 处理子节点 - 只有在节点明确展开时才处理子节点
        // 严格判断：只有明确设置为true时才展开
        const shouldExpand = node.isExpanded === true;
        if (node.children && node.children.length > 0 && shouldExpand) {
          node.children.forEach((child, childIndex) => {
            processNode(
              child,
              depth + 1,
              x,
              y,
              width,
              childIndex,
              node.children
            );
          });
        }
      };

      // 处理所有根节点
      data.forEach((rootNode, index) => {
        processNode(rootNode, 0, 0, 0, 0, index, data);
      });

      const layout = {nodes, connections};

      // 缓存布局结果
      this._layoutCache = {
        key: cacheKey,
        layout: layout
      };

      return layout;
    },

    // 获取布局配置
    _getLayoutConfig() {
      if (!this._layoutConfigCache) {
        this._layoutConfigCache = {
          horizontalGap: this.rpxToPx(LAYOUT_CONSTANTS.HORIZONTAL_GAP),
          verticalGap: this.rpxToPx(LAYOUT_CONSTANTS.VERTICAL_GAP),
          connectionExtension: this.rpxToPx(
            LAYOUT_CONSTANTS.CONNECTION.EXTENSION_LENGTH
          ),
          horizontalSegment: this.rpxToPx(
            LAYOUT_CONSTANTS.CONNECTION.HORIZONTAL_SEGMENT
          )
        };
      }
      return this._layoutConfigCache;
    },

    // 生成布局缓存键
    _generateLayoutCacheKey(data) {
      // 基于数据结构和展开状态生成缓存键
      const generateNodeKey = (node, depth = 0) => {
        let key = `${node.name}_${node.isExpanded}_${depth}`;
        if (node.children && node.children.length > 0) {
          const childKeys = node.children.map(child =>
            generateNodeKey(child, depth + 1)
          );
          key += `_[${childKeys.join(',')}]`;
        }
        return key;
      };

      const dataKey = data.map(node => generateNodeKey(node)).join('|');
      const canvasKey = `${this.getActualCanvasWidth()}_${this.getActualCanvasHeight()}`;

      return `${dataKey}_${canvasKey}`;
    },

    // 计算节点尺寸
    _calculateNodeDimensions(node, style, depth) {
      const isHorizontalLayout = depth >= 2;

      const width =
        style.width ||
        this.calculateNodeWidth(
          node.name,
          style,
          depth >= 1 ? node.count : null,
          isHorizontalLayout
        );

      const height =
        style.height ||
        this.calculateNodeHeight(
          node.name,
          style,
          depth === 0, // isVerticalText
          depth === 1, // isFirstLevel
          isHorizontalLayout
        );

      return {width, height};
    },

    // 计算节点位置
    _calculateNodePosition(
      node,
      depth,
      parentX,
      parentY,
      parentWidth,
      index,
      siblings,
      width,
      height,
      canvasHeight,
      calculateSubtreeHeight,
      layoutConfig
    ) {
      let x, y;

      if (depth === 0) {
        // 根节点居中
        const totalTreeWidth = this.calculateTotalTreeWidth();
        const canvasWidth = this.getActualCanvasWidth();
        x = (canvasWidth - totalTreeWidth) / 2;
        y = canvasHeight / 2;
      } else {
        // 子节点位置计算
        const subtreeHeights = siblings.map(sibling =>
          calculateSubtreeHeight(sibling, depth)
        );
        const totalSubtreeHeight =
          subtreeHeights.reduce((sum, h) => sum + h, 0) +
          (subtreeHeights.length - 1) * layoutConfig.verticalGap;

        let currentY = parentY - totalSubtreeHeight / 2;
        for (let i = 0; i < index; i++) {
          currentY += subtreeHeights[i] + layoutConfig.verticalGap;
        }
        currentY += subtreeHeights[index] / 2;

        y = currentY;

        // 使用传入的父节点宽度
        x =
          parentX +
          parentWidth +
          layoutConfig.horizontalGap +
          layoutConfig.horizontalSegment;
      }

      return {x, y};
    },

    // 添加连接线
    _addConnection(
      connections,
      nodes,
      parentX,
      parentY,
      x,
      y,
      depth,
      layoutConfig
    ) {
      const parentNode = nodes.find(n => n.x === parentX && n.y === parentY);
      const parentWidth = parentNode ? parentNode.width : 0;

      let connectionFromX = parentX + parentWidth;
      if (parentNode && parentNode.depth === 1) {
        connectionFromX += layoutConfig.connectionExtension;
      }

      connections.push({
        fromX: connectionFromX,
        fromY: parentY,
        toX: x,
        toY: y,
        toDepth: depth
      });
    },

    // 绘制连接线
    drawConnections(layout) {
      const ctx = this.ctx;

      // 设置线条样式
      if (this.isLegacyCanvas) {
        ctx.setStrokeStyle(LAYOUT_CONSTANTS.CONNECTION.COLOR);
        ctx.setLineWidth(this.rpxToPx(LAYOUT_CONSTANTS.CONNECTION.LINE_WIDTH));
      } else {
        ctx.strokeStyle = LAYOUT_CONSTANTS.CONNECTION.COLOR;
        ctx.lineWidth = this.rpxToPx(LAYOUT_CONSTANTS.CONNECTION.LINE_WIDTH);
      }

      // 按父节点分组连接线
      const connectionsByParent = new Map();
      layout.connections.forEach(conn => {
        const parentKey = `${conn.fromX},${conn.fromY}`;
        if (!connectionsByParent.has(parentKey)) {
          connectionsByParent.set(parentKey, []);
        }
        connectionsByParent.get(parentKey).push(conn);
      });

      // 为每个父节点绘制连接线 - 纵向布局：水平线+垂直线
      connectionsByParent.forEach(connections => {
        if (connections.length === 0) return;

        const firstConn = connections[0];
        const layoutConfig = this._getLayoutConfig();
        const midX = firstConn.fromX + layoutConfig.horizontalSegment;

        // 1. 绘制父节点的水平线
        ctx.beginPath();
        ctx.moveTo(firstConn.fromX, firstConn.fromY);
        ctx.lineTo(midX, firstConn.fromY);
        ctx.stroke();

        // 2. 绘制垂直线（从父节点到所有子节点）
        const topmostY = Math.min(...connections.map(c => c.toY));
        const bottommostY = Math.max(...connections.map(c => c.toY));

        ctx.beginPath();
        ctx.moveTo(midX, firstConn.fromY);
        ctx.lineTo(midX, topmostY);
        if (topmostY !== bottommostY) {
          // 如果有多个子节点，绘制完整的垂直线
          ctx.lineTo(midX, bottommostY);
        }
        ctx.stroke();

        // 3. 绘制每个子节点的水平连接线（连接到三角箭头）
        connections.forEach(conn => {
          ctx.beginPath();
          ctx.moveTo(midX, conn.toY);
          ctx.lineTo(conn.toX, conn.toY); // 直接连接到节点左边缘
          ctx.stroke();

          // 绘制向右的三角箭头（紧挨着节点左边）
          this.drawArrow(conn.toX, conn.toY, 'right');
        });
      });
    },

    // 绘制节点
    drawNodes(layout) {
      layout.nodes.forEach(node => {
        this.drawNode(node);
      });
    },

    // 绘制单个节点
    drawNode(node) {
      const ctx = this.ctx;
      const {x, y, width, height, style, name} = node;

      // 计算节点矩形位置 - 纵向布局调整
      const rectX = x; // 左对齐，不再居中
      const rectY = y - height / 2; // 垂直居中

      // 绘制圆角矩形背景
      this.drawRoundedRect(
        rectX,
        rectY,
        width,
        height,
        style.borderRadius,
        style.backgroundColor
      );

      // 绘制文字 - 根据节点类型选择绘制方式
      if (node.isRoot) {
        // 根节点文字垂直排布
        this.drawVerticalText(name, x + width / 2, y, style);
      } else if (node.depth === 1) {
        // 第一级节点：上下排布（名字 + count）
        this.drawFirstLevelText(node, x, y, width, height, style);
      } else if (node.depth >= 2) {
        // 第二、三、四级节点：水平排布（文字 + count）
        this.drawHorizontalText(node, x, y, width, height, style);
        // 为非叶子节点绘制展开/收起图标
        if (node.hasChildren) {
          this.drawNodeIcon(node, x, y, width, height, style);
        }
      } else {
        // 其他节点（备用）
        if (this.isLegacyCanvas) {
          ctx.setFillStyle(style.color);
          ctx.setFontSize(style.fontSize);
          ctx.setTextAlign('center');
          ctx.fillText(name, x + width / 2, y);
        } else {
          ctx.fillStyle = style.color;
          ctx.font = `${style.fontWeight} ${style.fontSize}px sans-serif`;
          ctx.textAlign = 'center';
          ctx.textBaseline = 'middle';
          ctx.fillText(name, x + width / 2, y);
        }
      }

      // 存储节点信息用于点击检测
      node.bounds = {x: rectX, y: rectY, width, height};
    },

    // 绘制垂直文字
    drawVerticalText(text, x, y, style) {
      const ctx = this.ctx;
      const fontSize = style.fontSize;
      const lineHeight = fontSize * 1.2; // 行高

      if (this.isLegacyCanvas) {
        ctx.setFillStyle(style.color);
        ctx.setFontSize(fontSize);
        ctx.setTextAlign('center');

        // 逐字绘制，垂直排列
        for (let i = 0; i < text.length; i++) {
          const char = text.charAt(i);
          const charY =
            y - ((text.length - 1) * lineHeight) / 2 + i * lineHeight;
          ctx.fillText(char, x, charY);
        }
      } else {
        ctx.fillStyle = style.color;
        ctx.font = `${style.fontWeight} ${fontSize}px sans-serif`;
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';

        // 逐字绘制，垂直排列
        for (let i = 0; i < text.length; i++) {
          const char = text.charAt(i);
          const charY =
            y - ((text.length - 1) * lineHeight) / 2 + i * lineHeight;
          ctx.fillText(char, x, charY);
        }
      }
    },

    // 绘制第一级节点文字（名字 + count 上下排布）
    drawFirstLevelText(node, x, y, width, _height, style) {
      const ctx = this.ctx;
      const centerX = x + width / 2;
      // 修正：y是节点中心点，所以centerY就是y本身
      const centerY = y;

      // 计算文字位置 - 确保整体垂直居中且不重叠
      const nameHeight = style.fontSize;
      const countHeight = style.countFontSize || style.fontSize;
      const textGap = style.textGap || 0;

      // 计算整个文字块的总高度（包括字体高度和间距）
      const totalContentHeight = nameHeight + textGap + countHeight;

      // 名字位置：从整体内容的顶部开始，向下偏移名字高度的一半
      const nameY = centerY - totalContentHeight / 2 + nameHeight / 2;
      // count位置：从整体内容的底部开始，向上偏移count高度的一半
      const countY = centerY + totalContentHeight / 2 - countHeight / 2;

      if (this.isLegacyCanvas) {
        // 绘制名字
        ctx.setFillStyle(style.color);
        ctx.setFontSize(style.fontSize);
        ctx.setTextAlign('center');
        ctx.setTextBaseline('middle'); // 添加垂直居中对齐
        ctx.fillText(node.name, centerX, nameY);

        // 绘制count
        ctx.setFillStyle(style.countColor || style.color);
        ctx.setFontSize(style.countFontSize || style.fontSize);
        ctx.setTextAlign('center');
        ctx.setTextBaseline('middle'); // 添加垂直居中对齐
        ctx.fillText(node.count.toString(), centerX, countY);
      } else {
        // 绘制名字
        ctx.fillStyle = style.color;
        ctx.font = `${style.fontWeight} ${style.fontSize}px sans-serif`;
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        ctx.fillText(node.name, centerX, nameY);

        // 绘制count
        ctx.fillStyle = style.countColor || style.color;
        ctx.font = `${style.fontWeight} ${
          style.countFontSize || style.fontSize
        }px sans-serif`;
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        ctx.fillText(node.count.toString(), centerX, countY);
      }

      // 绘制图标（展开/折叠图标）- 使用通用方法
      if (node.hasChildren) {
        this.drawNodeIcon(node, x, y, width, _height, style);
      }
    },

    // 绘制第一级节点图标
    drawFirstLevelIcon(_node, x, y, width, _height, style) {
      if (!style.iconPath) return;

      // 计算图标位置：容器最右侧 + 偏移图片一半的位置，上下居中
      const iconSize = style.iconSize || this.rpxToPx(32);

      // 图标X位置：节点右边缘 + 偏移量
      const iconX = x + width - iconSize / 2;
      // 图标Y位置：节点垂直中心
      const iconY = y - iconSize / 2;

      // 优先使用新的缓存机制
      const preloadedIcon = this._getPreloadedIcon(style.iconPath);

      if (preloadedIcon && this.ctx) {
        // 使用预加载的图标进行同步绘制
        this.ctx.drawImage(preloadedIcon, iconX, iconY, iconSize, iconSize);
      } else if (this._preloadedIcon && this.ctx) {
        // 向后兼容：使用旧的缓存机制
        this.ctx.drawImage(
          this._preloadedIcon,
          iconX,
          iconY,
          iconSize,
          iconSize
        );
      } else if (this.ctx) {
        // 使用统一的异步加载方法
        this._loadAndDrawIcon(style.iconPath, iconX, iconY, iconSize);
      }
    },

    // 绘制节点图标（通用方法，适用于所有有子节点的节点）
    drawNodeIcon(node, x, y, width, height, style) {
      // 只为有子节点的节点绘制图标
      if (!node.hasChildren || !style.iconSize) return;

      const iconSize = style.iconSize;

      // 计算图标位置：节点右边缘外侧，垂直居中
      // 根据用户偏好，图标位置在容器右边缘，半个图标偏移
      const iconX = x + width - iconSize / 2;
      const iconY = y - iconSize / 2;

      // 根据展开状态选择图标路径
      // 展开状态显示收起图标，收起状态显示展开图标
      const iconPath = node.isExpanded
        ? style.collapseIconPath || style.iconPath // 展开时显示收起图标
        : style.expandIconPath || style.iconPath; // 收起时显示展开图标

      if (!iconPath) {
        console.warn('图标路径未配置:', {
          depth: node.depth,
          hasChildren: node.hasChildren
        });
        return;
      }

      // 获取对应的预加载图标
      const preloadedIcon = this._getPreloadedIcon(iconPath);

      if (preloadedIcon && this.ctx) {
        // 使用预加载的图标进行同步绘制
        this.ctx.drawImage(preloadedIcon, iconX, iconY, iconSize, iconSize);
      } else if (this.ctx) {
        // 只有在Canvas存在且图标未缓存时才异步加载
        this._loadAndDrawIcon(iconPath, iconX, iconY, iconSize);
      }

      // 存储图标区域信息，用于点击检测（每次都更新，因为位置可能变化）
      // 扩大点击区域，让用户更容易点击
      const clickPadding = iconSize * 0.5; // 增加50%的点击区域
      node.iconBounds = {
        x: iconX - clickPadding,
        y: iconY - clickPadding,
        width: iconSize + clickPadding * 2,
        height: iconSize + clickPadding * 2
      };
    },

    // 获取预加载的图标
    _getPreloadedIcon(iconPath) {
      if (!this._preloadedIcons) {
        this._preloadedIcons = new Map();
      }
      return this._preloadedIcons.get(iconPath);
    },

    // 异步加载并绘制图标（优化版本，避免重复加载）
    _loadAndDrawIcon(iconPath, x, y, size) {
      // 初始化加载状态管理
      if (!this._loadingIcons) {
        this._loadingIcons = new Set();
      }

      // 如果正在加载中，避免重复加载
      if (this._loadingIcons.has(iconPath)) {
        return;
      }

      // 标记为加载中
      this._loadingIcons.add(iconPath);

      this.loadImage(iconPath)
        .then(img => {
          // 缓存图标
          if (!this._preloadedIcons) {
            this._preloadedIcons = new Map();
          }
          this._preloadedIcons.set(iconPath, img);

          // 移除加载状态
          this._loadingIcons.delete(iconPath);

          // 图标加载完成后，触发重新绘制整个树形图，确保所有图标都能正确显示
          if (this.ctx && img && !this.data._isDestroyed) {
            // 使用节流的重绘方法，避免频繁重绘
            this._throttledDrawTree();
          }
        })
        .catch(err => {
          // 移除加载状态，允许重试
          this._loadingIcons.delete(iconPath);
          console.warn('加载节点图标失败:', {iconPath, error: err});
        });
    },

    // 绘制水平排布文字（文字 + count 左右排布）
    drawHorizontalText(node, x, y, width, _height, style) {
      const ctx = this.ctx;
      const centerY = y; // y是节点中心点

      // 计算文字位置
      const nameWidth = this.calculateTextWidth(node.name, style.fontSize);
      const countWidth = this.calculateTextWidth(
        node.count.toString(),
        style.countFontSize || style.fontSize
      );
      const textGap = style.textGap || 0;
      const totalTextWidth = nameWidth + textGap + countWidth;

      // 计算起始位置（居中）
      const startX = x + (width - totalTextWidth) / 2;

      // 名字位置（左侧）
      const nameX = startX + nameWidth / 2;
      // count位置（右侧）
      const countX = startX + nameWidth + textGap + countWidth / 2;

      if (this.isLegacyCanvas) {
        // 绘制名字
        ctx.setFillStyle(style.color);
        ctx.setFontSize(style.fontSize);
        ctx.setTextAlign('center');
        ctx.setTextBaseline('middle');
        ctx.fillText(node.name, nameX, centerY);

        // 绘制count
        ctx.setFillStyle(style.countColor || style.color);
        ctx.setFontSize(style.countFontSize || style.fontSize);
        ctx.setTextAlign('center');
        ctx.setTextBaseline('middle');
        ctx.fillText(node.count.toString(), countX, centerY);
      } else {
        // 绘制名字
        ctx.fillStyle = style.color;
        ctx.font = `${style.fontWeight} ${style.fontSize}px sans-serif`;
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        ctx.fillText(node.name, nameX, centerY);

        // 绘制count
        ctx.fillStyle = style.countColor || style.color;
        ctx.font = `${style.fontWeight} ${
          style.countFontSize || style.fontSize
        }px sans-serif`;
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        ctx.fillText(node.count.toString(), countX, centerY);
      }
    },

    // 触摸开始事件（优化版本）
    onTouchStart(e) {
      if (!e.touches || e.touches.length === 0) {
        console.warn('触摸事件无效');
        return;
      }

      const touch = e.touches[0];
      this.setData({
        lastTouchX: touch.clientX,
        lastTouchY: touch.clientY,
        isDragging: false // 初始时不是拖拽状态
      });

      // 记录触摸开始时间，用于区分点击和拖拽
      this._touchStartTime = Date.now();
    },

    // 触摸移动事件 - 内容拖拽（优化版本）
    onTouchMove(e) {
      // 检查组件状态
      if (this.data._isDestroyed || !this.data._isVisible) return;

      if (!this.properties.enableDrag) return;

      if (!e.touches || e.touches.length === 0) return;

      const touch = e.touches[0];
      const deltaX = touch.clientX - this.data.lastTouchX;
      const deltaY = touch.clientY - this.data.lastTouchY;

      // 检查是否超过拖拽阈值
      const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);
      if (distance < EVENT_CONSTANTS.TOUCH.DRAG_THRESHOLD) return;

      // 标记为正在拖拽
      if (!this.data.isDragging) {
        this._throttledSetData(
          {
            isDragging: true
          },
          true
        ); // 立即更新拖拽状态
      }

      // 计算新的偏移量
      const newTranslateX = this.data.translateX + deltaX;
      const newTranslateY = this.data.translateY + deltaY;

      // 使用节流的setData，避免频繁更新
      this._throttledSetData({
        translateX: newTranslateX,
        translateY: newTranslateY,
        lastTouchX: touch.clientX,
        lastTouchY: touch.clientY
      });

      // 使用requestAnimationFrame进行重绘，但增加节流
      if (!this._dragDrawPending) {
        this._dragDrawPending = true;
        this._requestAnimationFrame(() => {
          if (!this.data._isDestroyed) {
            this.drawTree();
          }
          this._dragDrawPending = false;
        });
      }
    },

    // 触摸结束事件（优化版本）
    onTouchEnd(e) {
      const touchDuration = Date.now() - (this._touchStartTime || 0);
      const wasDragging = this.data.isDragging;

      // 清理定时器
      this._clearTimers();

      // 重置拖拽状态
      this.setData({
        isDragging: false
      });

      // 如果是短时间触摸且没有拖拽，认为是点击事件
      if (touchDuration < EVENT_CONSTANTS.TOUCH.TAP_TIMEOUT && !wasDragging) {
        this._handleTap(e);
      }

      this._touchStartTime = null;
    },

    // 处理点击事件
    _handleTap(e) {
      // 获取点击坐标 - 使用微信小程序的坐标系统
      const touch = e.changedTouches ? e.changedTouches[0] : e.touches[0];
      if (!touch) {
        console.warn('无法获取触摸坐标');
        return;
      }

      // 获取Canvas的边界信息
      this._getCanvasBounds().then(bounds => {
        if (!bounds) {
          console.warn('无法获取Canvas边界信息');
          return;
        }

        // 计算相对于Canvas的坐标
        const canvasX = touch.clientX - bounds.left - this.data.translateX;
        const canvasY = touch.clientY - bounds.top - this.data.translateY;

        // 优先检查是否点击了图标（展开/收起功能）
        const clickedIcon = this._findClickedIcon(canvasX, canvasY);
        if (clickedIcon) {
          this._toggleNodeExpansion(clickedIcon);
          return;
        }

        // 如果没有点击图标，检查是否点击了节点本身
        if (this.properties.enableNodeClick) {
          const clickedNode = this._findClickedNode(canvasX, canvasY);
          if (clickedNode) {
            this._handleNodeClick(clickedNode);
          }
        }
      });
    },

    // 获取Canvas的边界信息
    _getCanvasBounds() {
      return new Promise(resolve => {
        const query = wx.createSelectorQuery().in(this);
        query
          .select('#treeCanvas')
          .boundingClientRect(res => {
            resolve(res);
          })
          .exec();
      });
    },

    // 查找被点击的图标对应的节点
    _findClickedIcon(x, y) {
      if (!this.currentLayout || !this.currentLayout.nodes) {
        return null;
      }

      for (const node of this.currentLayout.nodes) {
        if (node.hasChildren && node.iconBounds) {
          const bounds = node.iconBounds;
          const isHit =
            x >= bounds.x &&
            x <= bounds.x + bounds.width &&
            y >= bounds.y &&
            y <= bounds.y + bounds.height;

          if (isHit) {
            return node;
          }
        }
      }

      return null;
    },

    // 查找被点击的节点（节点本身，不包括图标）
    _findClickedNode(x, y) {
      if (!this.currentLayout || !this.currentLayout.nodes) {
        return null;
      }

      for (const node of this.currentLayout.nodes) {
        if (node.bounds) {
          const bounds = node.bounds;
          const isHit =
            x >= bounds.x &&
            x <= bounds.x + bounds.width &&
            y >= bounds.y &&
            y <= bounds.y + bounds.height;

          if (isHit) {
            // 如果节点有图标，需要排除图标区域
            if (node.hasChildren && node.iconBounds) {
              const iconBounds = node.iconBounds;
              const isIconHit =
                x >= iconBounds.x &&
                x <= iconBounds.x + iconBounds.width &&
                y >= iconBounds.y &&
                y <= iconBounds.y + iconBounds.height;

              // 如果点击的是图标区域，不算点击节点
              if (isIconHit) {
                continue;
              }
            }

            return node;
          }
        }
      }

      return null;
    },

    // 处理节点点击事件
    _handleNodeClick(node) {
      if (!node) return;
      // 触发自定义事件，通知父组件节点被点击
      this.triggerEvent('nodeClick', {
        node: {
          name: node.name,
          depth: node.depth,
          isExpanded: node.isExpanded,
          hasChildren: node.hasChildren,
          count: node.count,
          expertchain_code: node.expertchain_code,
          // 传递完整的节点数据，但排除一些内部属性
          data: {
            name: node.name,
            count: node.count,
            children: node.children,
            isExpanded: node.isExpanded,
            hasChildren: node.hasChildren
          }
        },
        // 提供点击位置信息
        position: {
          x: node.x,
          y: node.y,
          depth: node.depth
        }
      });
    },

    // 切换节点展开/收起状态（精确控制单个节点）
    _toggleNodeExpansion(node) {
      if (!node.hasChildren) return;
      if (!this.properties.purchased && node.depth == 2) {
        this.triggerEvent('openVip');
        return;
      }

      // 切换展开状态
      const newExpandedState = !node.isExpanded;
      node.isExpanded = newExpandedState;

      // 如果是折叠操作，需要递归折叠所有子节点
      if (!newExpandedState && node.children && node.children.length > 0) {
        this._collapseAllChildrenRecursively(node.children);
      }

      // 生成节点路径用于精确匹配
      const nodePath = this._generateNodePath(node);

      // 同步状态到原始数据，使用路径匹配确保精确性
      this._syncNodeStateByPath(nodePath, newExpandedState);

      // 如果是折叠操作，同步子节点状态
      if (!newExpandedState && node.children && node.children.length > 0) {
        this._syncChildrenStateByPath(node.children, nodePath);
      }

      // 清理缓存，确保重新计算布局
      this._clearLayoutCache();

      // 重新绘制树形图
      this.drawTree();

      // 触发自定义事件，通知父组件状态变化
      this.triggerEvent('nodeToggle', {
        node: {
          name: node.name,
          depth: node.depth,
          isExpanded: node.isExpanded,
          hasChildren: node.hasChildren,
          expertchain_code: node.expertchain_code
        }
      });
    },

    // 同步单个节点状态到原始数据（精确控制，不影响其他节点）
    _syncSingleNodeStateToOriginalData(targetNode) {
      const syncSingleNode = (
        nodes,
        targetName,
        targetDepth,
        currentDepth = 0
      ) => {
        if (!nodes || !Array.isArray(nodes)) return false;

        for (const node of nodes) {
          if (node.name === targetName && currentDepth === targetDepth) {
            node.isExpanded = targetNode.isExpanded;

            return true;
          }

          if (
            node.children &&
            syncSingleNode(
              node.children,
              targetName,
              targetDepth,
              currentDepth + 1
            )
          ) {
            return true;
          }
        }
        return false;
      };

      // 获取当前的树形数据
      const currentTreeData = this.properties.treeData;
      if (currentTreeData && Array.isArray(currentTreeData)) {
        const success = syncSingleNode(
          currentTreeData,
          targetNode.name,
          targetNode.depth
        );
        if (!success) {
          console.warn(
            `未找到要同步的节点: ${targetNode.name} (depth: ${targetNode.depth})`
          );
        }
      }
    },

    // 同步节点状态到原始数据
    _syncNodeStateToOriginalData(targetNode) {
      const syncNodeInTree = (
        nodes,
        targetName,
        targetDepth,
        currentDepth = 0
      ) => {
        if (!nodes || !Array.isArray(nodes)) return false;

        for (const node of nodes) {
          if (node.name === targetName && currentDepth === targetDepth) {
            node.isExpanded = targetNode.isExpanded;

            return true;
          }

          if (
            node.children &&
            syncNodeInTree(
              node.children,
              targetName,
              targetDepth,
              currentDepth + 1
            )
          ) {
            return true;
          }
        }
        return false;
      };

      // 获取当前的树形数据
      const currentTreeData = this.properties.treeData;
      if (currentTreeData && Array.isArray(currentTreeData)) {
        syncNodeInTree(currentTreeData, targetNode.name, targetNode.depth);
      }
    },

    // 清理布局相关缓存
    _clearLayoutCache() {
      this._clearWidthCache();
      this._totalTreeWidthCache = null;
      this._layoutConfigCache = null;
      this.currentLayout = null;
      // 清理布局计算缓存
      this._layoutCache = null;
    },

    // 重置拖拽状态（优化版本）
    resetDragState() {
      // 检查组件状态
      if (this.data._isDestroyed) {
        return;
      }

      // 清理所有定时器
      this._clearTimers();

      // 重置拖拽相关状态
      this._throttledSetData(
        {
          translateX: 0,
          translateY: 0,
          isDragging: false,
          lastTouchX: 0,
          lastTouchY: 0
        },
        true
      ); // 立即更新

      // 清理拖拽相关的临时状态
      this._dragDrawPending = false;
      this._touchStartTime = null;

      // 重新绘制
      if (this.data._isVisible) {
        this._throttledDrawTree();
      }
    },

    // 全部展开
    expandAll() {
      this._setAllNodesExpansion(true);
    },

    // 全部收起
    collapseAll() {
      this._setAllNodesExpansion(false);
    },

    // 设置所有节点的展开状态
    _setAllNodesExpansion(isExpanded) {
      if (isExpanded) {
        // 全部展开：展开所有层级
        this._expandAllLevels();
      } else {
        // 全部收起：只显示根节点和第一级节点，隐藏第二、三、四级节点
        this._collapseToFirstLevel();
      }
    },

    // 展开所有层级
    _expandAllLevels() {
      const setAllExpanded = nodes => {
        if (!nodes || !Array.isArray(nodes)) return;

        nodes.forEach(node => {
          if (node.children && node.children.length > 0) {
            node.isExpanded = true;
            node.hasChildren = true;

            setAllExpanded(node.children);
          }
        });
      };

      const currentTreeData = this.properties.treeData;
      if (currentTreeData && Array.isArray(currentTreeData)) {
        setAllExpanded(currentTreeData);
      }

      this._redrawTree();
      this.triggerEvent('allNodesToggle', {isExpanded: true});
    },

    // 收起到第一级（只显示根节点和第一级节点）
    _collapseToFirstLevel() {
      const setFirstLevelOnly = nodes => {
        if (!nodes || !Array.isArray(nodes)) return;

        nodes.forEach(rootNode => {
          // 根节点保持展开，显示第一级节点
          if (rootNode.children && rootNode.children.length > 0) {
            rootNode.isExpanded = true;
            rootNode.hasChildren = true;

            // 第一级节点全部收起，隐藏第二、三、四级节点
            rootNode.children.forEach(firstLevelNode => {
              if (
                firstLevelNode.children &&
                firstLevelNode.children.length > 0
              ) {
                // 彻底清除第一级节点的展开状态，避免数据预处理时保留原有状态
                delete firstLevelNode.isExpanded;
                firstLevelNode.hasChildren = true;

                // 递归收起所有子节点，确保深层级节点也被正确收起
                this._collapseAllChildren(firstLevelNode.children);
              }
            });
          }
        });
      };

      const currentTreeData = this.properties.treeData;
      if (currentTreeData && Array.isArray(currentTreeData)) {
        setFirstLevelOnly(currentTreeData);
      }

      this._redrawTree();
      this.triggerEvent('allNodesToggle', {isExpanded: false});
    },

    // 递归收起所有子节点（用于全部折叠）
    _collapseAllChildren(nodes) {
      if (!nodes || !Array.isArray(nodes)) return;

      nodes.forEach(node => {
        if (node.children && node.children.length > 0) {
          // 彻底清除展开状态，避免数据预处理时保留原有状态
          delete node.isExpanded;
          node.hasChildren = true;

          // 递归处理更深层级的子节点
          this._collapseAllChildren(node.children);
        }
      });
    },

    // 递归收起所有子节点（用于单个节点折叠）
    _collapseAllChildrenRecursively(nodes) {
      if (!nodes || !Array.isArray(nodes)) return;

      nodes.forEach(node => {
        if (node.children && node.children.length > 0) {
          // 设置为收起状态，但不删除属性（保持状态同步）
          node.isExpanded = false;
          node.hasChildren = true;

          // 递归处理更深层级的子节点
          this._collapseAllChildrenRecursively(node.children);
        }
      });
    },

    // 同步节点及其所有子节点状态到原始数据
    _syncNodeAndChildrenStateToOriginalData(targetNode) {
      // 先同步当前节点
      this._syncSingleNodeStateToOriginalData(targetNode);

      // 如果当前节点被折叠，需要同步所有子节点的状态
      if (
        !targetNode.isExpanded &&
        targetNode.children &&
        targetNode.children.length > 0
      ) {
        this._syncChildrenStateToOriginalData(
          targetNode.children,
          targetNode.depth + 1
        );
      }
    },

    // 递归同步子节点状态到原始数据
    _syncChildrenStateToOriginalData(children, currentDepth) {
      if (!children || !Array.isArray(children)) return;

      children.forEach(child => {
        // 同步子节点状态
        this._syncSingleNodeStateByDepthToOriginalData(child, currentDepth);

        // 递归处理孙子节点
        if (child.children && child.children.length > 0) {
          this._syncChildrenStateToOriginalData(
            child.children,
            currentDepth + 1
          );
        }
      });
    },

    // 根据深度同步单个节点状态到原始数据
    _syncSingleNodeStateByDepthToOriginalData(targetNode, targetDepth) {
      const syncSingleNode = (
        nodes,
        targetName,
        targetDepth,
        currentDepth = 0
      ) => {
        if (!nodes || !Array.isArray(nodes)) return false;

        for (const node of nodes) {
          if (node.name === targetName && currentDepth === targetDepth) {
            node.isExpanded = targetNode.isExpanded;
            return true;
          }

          if (
            node.children &&
            syncSingleNode(
              node.children,
              targetName,
              targetDepth,
              currentDepth + 1
            )
          ) {
            return true;
          }
        }
        return false;
      };

      // 获取当前的树形数据
      const currentTreeData = this.properties.treeData;
      if (currentTreeData && Array.isArray(currentTreeData)) {
        const success = syncSingleNode(
          currentTreeData,
          targetNode.name,
          targetDepth
        );
        if (!success) {
          console.warn(
            `未找到要同步的节点: ${targetNode.name} (depth: ${targetDepth})`
          );
        }
      }
    },

    // 构建节点路径（用于精确匹配同名节点）
    _buildNodePath(node, depth, index, siblings) {
      // 使用深度、索引和节点名称构建唯一路径
      // 格式: "depth:index:name"
      return `${depth}:${index}:${node.name}`;
    },

    // 生成节点路径（从布局节点信息中提取）
    _generateNodePath(node) {
      // 如果节点已经有路径信息，直接返回
      if (node._nodePath) {
        return node._nodePath;
      }

      // 兜底方案：使用深度和名称
      return `${node.depth}:${node.name}`;
    },

    // 根据路径同步节点状态到原始数据
    _syncNodeStateByPath(nodePath, isExpanded) {
      const pathParts = nodePath.split(':');
      if (pathParts.length < 3) {
        console.warn('无效的节点路径:', nodePath);
        return;
      }

      const targetDepth = parseInt(pathParts[0]);
      const targetIndex = parseInt(pathParts[1]);
      const targetName = pathParts.slice(2).join(':'); // 处理名称中可能包含冒号的情况

      const syncByPath = (nodes, currentDepth = 0) => {
        if (!nodes || !Array.isArray(nodes)) return false;

        if (currentDepth === targetDepth) {
          // 到达目标深度，根据索引和名称精确匹配
          if (nodes[targetIndex] && nodes[targetIndex].name === targetName) {
            nodes[targetIndex].isExpanded = isExpanded;
            return true;
          }
        } else if (currentDepth < targetDepth) {
          // 还没到达目标深度，继续递归
          for (const node of nodes) {
            if (node.children && syncByPath(node.children, currentDepth + 1)) {
              return true;
            }
          }
        }
        return false;
      };

      const currentTreeData = this.properties.treeData;
      if (currentTreeData && Array.isArray(currentTreeData)) {
        const success = syncByPath(currentTreeData);
        if (!success) {
          console.warn(`未找到要同步的节点路径: ${nodePath}`);
        }
      }
    },

    // 根据路径同步子节点状态
    _syncChildrenStateByPath(children, parentPath) {
      if (!children || !Array.isArray(children)) return;

      children.forEach((child, index) => {
        // 构建子节点路径
        const childPath = this._buildNodePath(
          child,
          child.depth || 0,
          index,
          children
        );

        // 同步子节点状态
        this._syncNodeStateByPath(childPath, child.isExpanded || false);

        // 递归处理孙子节点
        if (child.children && child.children.length > 0) {
          this._syncChildrenStateByPath(child.children, childPath);
        }
      });
    },

    // 重新绘制树形图
    _redrawTree() {
      this._clearLayoutCache();

      this.drawTree();
    },

    // 展开到第二级（显示第二级节点，但收起第三级及以下）
    expandToSecondLevel() {
      this._setLevelExpansion(2);
    },

    // 展开到第三级（显示第三级节点，但收起第四级及以下）
    expandToThirdLevel() {
      this._setLevelExpansion(3);
    },

    // 展开到第四级（显示第四级节点，但收起第五级及以下）
    expandToFourthLevel() {
      this._setLevelExpansion(4);
    },

    // 展开到第五级（显示第五级节点，但收起第六级及以下）
    expandToFifthLevel() {
      this._setLevelExpansion(5);
    },

    // 展开到第六级（显示第六级节点，但收起第七级及以下）
    expandToSixthLevel() {
      this._setLevelExpansion(6);
    },

    // 展开到第七级（显示所有层级）
    expandToSeventhLevel() {
      this._setLevelExpansion(7);
    },

    // 设置展开到指定层级
    _setLevelExpansion(maxLevel) {
      const setNodeExpansionByLevel = (nodes, currentLevel = 0) => {
        if (!nodes || !Array.isArray(nodes)) return;

        nodes.forEach(node => {
          if (node.children && node.children.length > 0) {
            // 如果当前层级小于最大层级，则展开；否则收起
            const shouldExpand = currentLevel < maxLevel;
            node.isExpanded = shouldExpand;
            node.hasChildren = true;

            // 递归处理子节点
            setNodeExpansionByLevel(node.children, currentLevel + 1);
          }
        });
      };

      // 更新原始数据
      const currentTreeData = this.properties.treeData;
      if (currentTreeData && Array.isArray(currentTreeData)) {
        setNodeExpansionByLevel(currentTreeData);
      }

      // 清理缓存并重新绘制
      this._clearLayoutCache();
      this.drawTree();

      // 触发事件通知
      this.triggerEvent('levelExpansion', {
        maxLevel: maxLevel
      });
    },

    // 数据变化监听器 - 当treeData属性发生变化时触发
    _onTreeDataChange(newData, oldData) {
      // 如果组件已销毁，不处理数据变化
      if (this.data._isDestroyed) {
        return;
      }

      // 如果是初始化时的数据设置，不需要特殊处理
      if (!oldData && newData) {
        return;
      }
      // 检查数据是否真的发生了变化
      if (this._isDataEqual(newData, oldData)) {
        return;
      }
      // 清除所有缓存
      this._clearAllCaches();

      // 重置拖拽状态（可选，根据需求决定）
      this.resetDragState();

      // 标记需要重新居中
      this._needsRecentering = true;
      this._hasBeenCentered = false;

      // 触发数据变化事件，通知父组件
      this.triggerEvent('dataChange', {
        newData: newData,
        oldData: oldData,
        timestamp: Date.now()
      });

      // 延迟重新绘制，确保数据已经完全更新
      wx.nextTick(() => {
        if (!this.data._isDestroyed) {
          this._throttledDrawTree();
        }
      });
    },

    // 检查两个数据是否相等（深度比较）
    _isDataEqual(data1, data2) {
      // 简单的引用比较
      if (data1 === data2) return true;

      // 类型检查
      if (!data1 || !data2) return false;
      if (!Array.isArray(data1) || !Array.isArray(data2)) return false;
      if (data1.length !== data2.length) return false;

      // 简化的深度比较 - 只比较关键属性
      try {
        return JSON.stringify(data1) === JSON.stringify(data2);
      } catch (error) {
        // 如果JSON序列化失败，认为数据不同
        console.warn('数据比较失败，认为数据已变化:', error);
        return false;
      }
    },

    // 清除所有缓存
    _clearAllCaches() {
      // 清除布局相关缓存
      this._clearLayoutCache();

      // 清除样式缓存
      if (this._styleCache) {
        this._styleCache.clear();
      }

      // 清除宽度缓存
      this._clearWidthCache();

      // 清除其他缓存
      this._totalTreeWidthCache = null;
      this._layoutConfigCache = null;
      this._cachedPixelRatio = null;
      this._cachedWindowInfo = null;

      // 清除当前布局
      this.currentLayout = null;

      // 如果启用了缓存清理功能
      if (this.properties.enableCache && this.clearCache) {
        this.clearCache();
      }
    },

    // 清理资源（增强版）
    _cleanup() {
      // 标记组件为已销毁状态
      this.setData({
        _isDestroyed: true,
        _isVisible: false
      });

      // 清理所有定时器
      this._clearTimers();

      // 清理缓存
      if (this.properties.enableCache) {
        this.clearCache();
        if (this._styleCache) {
          this._styleCache.clear();
          this._styleCache = null;
        }
      }

      // 重置Canvas状态
      if (this.resetCanvas) {
        this.resetCanvas();
      }

      // 清理Canvas上下文
      if (this.ctx) {
        try {
          // 清空画布
          const width = this.getActualCanvasWidth();
          const height = this.getActualCanvasHeight();
          this.ctx.clearRect(0, 0, width, height);
        } catch (error) {
          console.warn('清理Canvas失败:', error);
        }
        this.ctx = null;
      }

      // 清理Canvas对象
      if (this.canvas) {
        this.canvas = null;
      }

      // 清理其他缓存
      this._clearWidthCache();
      this._clearLayoutCache();
      this._totalTreeWidthCache = null;
      this._layoutConfigCache = null;

      // 清理图标缓存
      if (this._preloadedIcons) {
        this._preloadedIcons.clear();
        this._preloadedIcons = null;
      }
      this._preloadedIcon = null;

      // 清理加载状态
      if (this._loadingIcons) {
        this._loadingIcons.clear();
        this._loadingIcons = null;
      }

      // 重置绘制状态
      this._isDrawing = false;
      this._needsRecentering = false;
      this._hasBeenCentered = false;
      this._centeringInProgress = false;

      // 清理布局数据
      this.currentLayout = null;

      // 清理待处理的数据
      this._pendingSetData = null;

      // 清理缓存的窗口信息
      this._cachedWindowInfo = null;

      // 清理缓存的像素比
      this._cachedPixelRatio = null;
    },

    // 获取窗口信息（缓存版本，避免重复调用）
    _getWindowInfo() {
      if (!this._cachedWindowInfo) {
        try {
          // 使用新的API替代废弃的wx.getSystemInfoSync
          if (wx.getWindowInfo) {
            this._cachedWindowInfo = wx.getWindowInfo();
          } else {
            // 降级到旧API
            const systemInfo = wx.getSystemInfoSync();
            this._cachedWindowInfo = {
              windowHeight: systemInfo.windowHeight,
              windowWidth: systemInfo.windowWidth
            };
          }
        } catch (error) {
          console.warn('获取窗口信息失败，使用默认值:', error);
          this._cachedWindowInfo = {
            windowHeight: 667, // iPhone 6/7/8 默认高度
            windowWidth: 375 // iPhone 6/7/8 默认宽度
          };
        }
      }
      return this._cachedWindowInfo;
    },

    // 检查组件可见性（优化版本）
    _checkVisibility() {
      if (this.data._isDestroyed) {
        return;
      }

      const query = wx.createSelectorQuery().in(this);
      query
        .select('#treeCanvas')
        .boundingClientRect(rect => {
          if (!rect) {
            this._throttledSetData({_isVisible: false});
            return;
          }

          // 使用缓存的窗口信息
          const windowInfo = this._getWindowInfo();
          const windowHeight = windowInfo.windowHeight;

          // 判断组件是否在可视区域内
          const isVisible = rect.bottom > 0 && rect.top < windowHeight;

          if (isVisible !== this.data._isVisible) {
            this._throttledSetData({_isVisible: isVisible});

            // 如果组件变为可见，触发重绘
            if (isVisible && !this.data._isDestroyed) {
              this._throttledDrawTree();
            }
          }
        })
        .exec();
    },

    // 启动可见性监控
    _startVisibilityMonitoring() {
      if (this._visibilityTimer) {
        return;
      }

      // 每秒检查一次可见性
      this._visibilityTimer = setInterval(() => {
        if (!this.data._isDestroyed) {
          this._checkVisibility();
        } else {
          this._stopVisibilityMonitoring();
        }
      }, 1000);
    },

    // 停止可见性监控
    _stopVisibilityMonitoring() {
      if (this._visibilityTimer) {
        clearInterval(this._visibilityTimer);
        this._visibilityTimer = null;
      }
    },

    // 预加载图标（优化版本）- 预加载所有层级的展开/收起图标
    preloadIcons() {
      if (!this.canvas) {
        console.warn('Canvas未初始化，跳过图标预加载');
        return;
      }

      // 收集所有需要预加载的图标路径
      const iconPaths = this._collectIconPaths();

      if (iconPaths.length === 0) {
        // console.log('没有找到需要预加载的图标，直接绘制树形图');
        // 延迟一下绘制，确保Canvas完全准备好
        setTimeout(() => {
          if (!this.data._isDestroyed) {
            this.drawTree();
          }
        }, 50);
        return;
      }

      // 初始化图标缓存
      if (!this._preloadedIcons) {
        this._preloadedIcons = new Map();
      }

      // 记录加载状态
      let loadedCount = 0;
      let totalCount = iconPaths.length;

      // 标记图标预加载开始
      this._iconPreloadingInProgress = true;

      // 并行加载所有图标
      const loadPromises = iconPaths.map((iconPath, index) =>
        this.loadImage(iconPath)
          .then(img => {
            this._preloadedIcons.set(iconPath, img);
            // 保持向后兼容
            if (iconPath === NODE_STYLE_CONSTANTS.FIRST_LEVEL.ICON_PATH) {
              this._preloadedIcon = img;
            }

            loadedCount++;
            // console.log(
            //   `图标加载进度: ${loadedCount}/${totalCount} - ${iconPath}`
            // );

            // 每加载完一个图标就触发一次重绘，确保图标能及时显示
            // 但要避免过于频繁的重绘，使用节流
            if (this.ctx && !this.data._isDestroyed && !this._iconDrawPending) {
              this._iconDrawPending = true;
              setTimeout(() => {
                if (!this.data._isDestroyed) {
                  this._throttledDrawTree();
                }
                this._iconDrawPending = false;
              }, 16); // 约60fps的频率
            }

            return {path: iconPath, success: true, index};
          })
          .catch(err => {
            console.warn(`图标预加载失败: ${iconPath}`, err);
            loadedCount++;
            return {path: iconPath, success: false, error: err, index};
          })
      );

      // 等待所有图标加载完成后进行最终绘制
      Promise.allSettled(loadPromises).then(results => {
        const successCount = results.filter(
          r => r.value && r.value.success
        ).length;
        // console.log(`图标预加载完成: ${successCount}/${totalCount} 成功`);

        // 标记图标预加载完成
        this._iconPreloadingInProgress = false;
        this._iconDrawPending = false;

        // 最终绘制，确保所有图标都能正确显示
        if (!this.data._isDestroyed) {
          // 使用setTimeout确保在下一个事件循环中执行，避免与其他绘制冲突
          setTimeout(() => {
            if (!this.data._isDestroyed) {
              this.drawTree();
            }
          }, 10);
        }
      });
    },

    // 收集所有需要预加载的图标路径
    _collectIconPaths() {
      const iconPaths = new Set();

      // 第一级节点图标
      const firstLevel = NODE_STYLE_CONSTANTS.FIRST_LEVEL;
      if (firstLevel.ICON_PATH) iconPaths.add(firstLevel.ICON_PATH);
      if (firstLevel.EXPAND_ICON_PATH)
        iconPaths.add(firstLevel.EXPAND_ICON_PATH);
      if (firstLevel.COLLAPSE_ICON_PATH)
        iconPaths.add(firstLevel.COLLAPSE_ICON_PATH);

      // 第二级节点图标
      const secondLevel = NODE_STYLE_CONSTANTS.SECOND_LEVEL;
      if (secondLevel.EXPAND_ICON_PATH)
        iconPaths.add(secondLevel.EXPAND_ICON_PATH);
      if (secondLevel.COLLAPSE_ICON_PATH)
        iconPaths.add(secondLevel.COLLAPSE_ICON_PATH);

      // 第三级节点图标
      const thirdLevel = NODE_STYLE_CONSTANTS.THIRD_LEVEL;
      if (thirdLevel.EXPAND_ICON_PATH)
        iconPaths.add(thirdLevel.EXPAND_ICON_PATH);
      if (thirdLevel.COLLAPSE_ICON_PATH)
        iconPaths.add(thirdLevel.COLLAPSE_ICON_PATH);

      return Array.from(iconPaths);
    },

    // 首次绘制时居中显示树形图
    _centerTreeIfNeeded(layout) {
      // 只在首次绘制或重置时居中
      if (this._hasBeenCentered) {
        return;
      }

      if (!layout || !layout.nodes || layout.nodes.length === 0) {
        return;
      }

      // 计算树形图的边界
      const bounds = this._calculateTreeBounds(layout);
      if (!bounds) {
        return;
      }

      // 获取Canvas尺寸
      const canvasWidth = this.getActualCanvasWidth();
      const canvasHeight = this.getActualCanvasHeight();

      // 计算居中偏移量
      const centerX = (canvasWidth - bounds.width) / 2 - bounds.left;
      const centerY = (canvasHeight - bounds.height) / 2 - bounds.top;

      // 设置初始偏移量
      this.setData({
        translateX: centerX,
        translateY: centerY
      });

      // 标记已经居中过
      this._hasBeenCentered = true;
    },

    // 计算树形图的边界
    _calculateTreeBounds(layout) {
      if (!layout.nodes || layout.nodes.length === 0) {
        return null;
      }

      let minX = Infinity,
        minY = Infinity;
      let maxX = -Infinity,
        maxY = -Infinity;

      layout.nodes.forEach(node => {
        const left = node.x;
        const right = node.x + node.width;
        const top = node.y - node.height / 2;
        const bottom = node.y + node.height / 2;

        minX = Math.min(minX, left);
        maxX = Math.max(maxX, right);
        minY = Math.min(minY, top);
        maxY = Math.max(maxY, bottom);
      });

      return {
        left: minX,
        top: minY,
        right: maxX,
        bottom: maxY,
        width: maxX - minX,
        height: maxY - minY
      };
    },

    // 重置居中状态（用于重新居中）
    resetCenterState() {
      this._hasBeenCentered = false;
      this.setData({
        translateX: 0,
        translateY: 0
      });
    },

    // 在渲染完成后进行居中（解决初始化时机问题）
    _centerTreeAfterRender(layout) {
      // 防止重复调用
      if (this._centeringInProgress) {
        return;
      }

      this._centeringInProgress = true;

      // 使用wx.nextTick确保DOM更新完成，然后用requestAnimationFrame确保渲染完成
      wx.nextTick(() => {
        this._requestAnimationFrame(() => {
          this._centerTreeIfNeeded(layout);

          // 如果成功居中，需要重新绘制以应用偏移
          if (
            this._hasBeenCentered &&
            (this.data.translateX !== 0 || this.data.translateY !== 0)
          ) {
            // 重新绘制但不再居中
            this._centeringInProgress = false;
            this._throttledDrawTree();
          } else {
            this._centeringInProgress = false;
          }
        });
      });
    },

    // 确保初始居中（备用方案）
    _ensureInitialCentering() {
      // 如果还没有居中过，并且有布局数据，则进行居中
      if (
        !this._hasBeenCentered &&
        this.currentLayout &&
        !this._centeringInProgress
      ) {
        this._centerTreeIfNeeded(this.currentLayout);

        // 如果备用方案成功居中，重新绘制
        if (
          this._hasBeenCentered &&
          (this.data.translateX !== 0 || this.data.translateY !== 0)
        ) {
          this.drawTree();
        }
      }
    },

    // 重新居中树形图
    recenterTree() {
      if (this.data._isDestroyed) {
        return;
      }

      this._hasBeenCentered = false;
      this._needsRecentering = true;
      if (this.currentLayout) {
        this._centerTreeIfNeeded(this.currentLayout);
        this._throttledDrawTree();
      }
    },

    // 手动销毁组件（供外部调用）
    destroy() {
      this._cleanup();
    },

    // 暂停组件（节省资源）
    pause() {
      this.setData({_isVisible: false});
      this._stopVisibilityMonitoring();
      this._clearTimers();
    },

    // 恢复组件
    resume() {
      if (!this.data._isDestroyed) {
        this.setData({_isVisible: true});
        this._startVisibilityMonitoring();
        this._throttledDrawTree();
      }
    },

    // requestAnimationFrame兼容性封装
    _requestAnimationFrame(callback) {
      if (typeof requestAnimationFrame !== 'undefined') {
        requestAnimationFrame(callback);
      } else if (wx.createRequestAnimationFrame) {
        wx.createRequestAnimationFrame(callback);
      } else {
        // 降级到wx.nextTick
        wx.nextTick(callback);
      }
    },

    // 强制检查居中状态
    _forceCheckCentering() {
      // 使用wx.nextTick替代setTimeout，更适合小程序环境
      wx.nextTick(() => {
        this._requestAnimationFrame(() => {
          // 如果还没有居中，强制执行居中
          if (
            !this._hasBeenCentered ||
            (this.data.translateX === 0 && this.data.translateY === 0)
          ) {
            this.recenterTree();
          }
        });
      });
    }
  },

  // 组件生命周期（优化版本）
  ready() {
    try {
      // 初始化组件状态
      this.setData({
        _isDestroyed: false,
        _isVisible: true
      });

      // 初始化系统信息
      this.initSystemInfo();

      // 启动可见性监控
      this._startVisibilityMonitoring();

      // 使用wx.nextTick确保DOM完全渲染后初始化Canvas
      wx.nextTick(() => {
        this._requestAnimationFrame(() => {
          if (!this.data._isDestroyed) {
            this.initCanvas();

            // 在Canvas初始化完成后，再次尝试居中（备用方案）
            wx.nextTick(() => {
              this._requestAnimationFrame(() => {
                if (!this.data._isDestroyed) {
                  this._ensureInitialCentering();
                  // 强制检查居中状态
                  this._forceCheckCentering();
                }
              });
            });
          }
        });
      });
    } catch (error) {
      console.error('组件初始化失败:', error);
    }
  },

  // 组件挂载
  attached() {
    // 确保组件状态正确
    if (this.data._isDestroyed) {
      this.setData({
        _isDestroyed: false,
        _isVisible: true
      });
    }
  },

  // 组件卸载
  detached() {
    try {
      // 停止可见性监控
      this._stopVisibilityMonitoring();

      // 清理资源
      this._cleanup();
    } catch (error) {
      console.error('组件卸载时出错:', error);
    }
  },

  // 页面显示时的处理
  pageLifetimes: {
    show() {
      if (!this.data._isDestroyed) {
        this.setData({_isVisible: true});
        this._startVisibilityMonitoring();
        // 延迟重绘，确保页面完全显示
        setTimeout(() => {
          if (!this.data._isDestroyed) {
            this._throttledDrawTree();
          }
        }, 100);
      }
    },

    hide() {
      this.setData({_isVisible: false});
      this._stopVisibilityMonitoring();
      // 清理定时器以节省资源
      this._clearTimers();
    }
  }
});
