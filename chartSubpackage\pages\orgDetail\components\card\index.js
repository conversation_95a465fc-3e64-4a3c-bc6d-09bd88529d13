const app = getApp()

Component({
  options: {
    addGlobalClass: true
  },
  properties: {
    obj: {
      type: Object,
      observer(val) {
        val.logo = /(http|https):\/\/([\w.]+\/?)\S*/.test(val.logo) ? val.logo : 'https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/quesen.png';
        const invest_tag = `${ val.invest_round_name ? val.invest_round_name :  '---'} | ${val.moneyStr ? val.moneyStr : '--'}`;
        val.invest_tag = invest_tag.length > 10 ? invest_tag.slice(0, 10) + '...' : invest_tag;
        // val.invest_tag  = invest_tag;
        const catTag = `${val.cat_name ? val.cat_name : '-'}-${ val.sub_cat_name ? val.sub_cat_name : '-'}`;
        val.catTag = catTag.length > 8 ? catTag.slice(0, 8) + '...' : catTag;
        this.setData({
          objData: val
        })
        this.setData({
          objData: val
        });
      }
    },
  },
  data: {
    objData: {}, // 源数据
  },
  methods: {
    clickOrg({
      currentTarget: {
        dataset
      }
    }) {
      let {
        item
      } = dataset;
      if (item.org_id) {
        let url = `/chartSubpackage/pages/orgDetail/index?org_id=${item.org_id}`;
        app.route(this, url)
      }
    },
    errorFunction() {
      const {
        objData
      } = this.data;
      this.setData({
        objData: {
          ...objData,
          picture_url: "https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/default.png"
        }
      });
    },
  }
})