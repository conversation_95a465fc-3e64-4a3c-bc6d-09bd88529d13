<!--components/BtmListMultGroupPop/index.wxml-->
<HalfScreenPop
  showFooter="{{true}}"
  changePadding
  disableAnimation="{{true}}"
  visible="{{visible}}"
  position="{{position}}"
  bindclose="close"
  zIndex="{{zIndex}}"
  bindsubmit="submit"
  title="{{title}}"
>
  <view slot="customContent">
    <view class="btm-list-wrap">
      <!-- 列表内容 -->
      <scroll-view scroll-y class="list-container">
        <view
          wx:for="{{sourceData}}"
          wx:key="code"
          class="list-item {{item.active ? 'active' : ''}}"
        >
          <view class="item-content">
            <view class="item-name">{{item.name}}</view>
            <view
              class="item-checkbox {{item.active ? 'checked' : ''}}"
              bindtap="handleItemClick"
              data-item="{{item}}"
              wx:if="{{item.purchased}}"
            >
            </view>
            <image
              src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/cyt_v2/image/industry/i_lock.png"
              wx:else
              style="width: 32rpx;height: 32rpx; flex-shrink: 0;"
              bindtap="handleItemClick"
              data-item="{{item}}"
            />
          </view>
        </view>

        <!-- 空状态 -->
        <view class="empty-state" wx:if="{{sourceData.length === 0}}">
          <view class="empty-text">--暂无数据--</view>
        </view>
      </scroll-view>
    </view>
  </view>
</HalfScreenPop>

<!-- 申请开通产业弹窗 -->
<IndustryApplyPop
  visible="{{vipVisible}}"
  bindclose="onVipClose"
  bindsend="onApplySend"
  zIndex="{{zIndex + 1}}"
  chain_code="{{currentChainCode}}"
  chain_name="{{currentChainName}}"
/>
