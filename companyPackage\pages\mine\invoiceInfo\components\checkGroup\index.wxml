<view class="checked-group-wrapper">
  <view
    wx:for="{{checkList}}"
    wx:key="value"
    class="checked-item"
    data-value="{{item.value}}"
    bindtap="onHandlerTap"
  >
    <view class="checked-box {{!item.checked && 'show'}}">
      <image
        hidden="{{!item.checked}}"
        src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/cyt_v2/image/mine/invoice_select_active.png"
      />
    </view>
    <text>{{item.label}}</text>
  </view>
</view>
