<import src="/template/null/null.wxml"></import>
<!-- style="padding-bottom:  {{isIphoneX ? '168rpx':'100rpx'}};" 按钮这个不用加 ，加上反而很丑 -->
<view class="suggest">
  <view class="suggest-title"> 跟进内容 </view>
  <view class="area">
    <!--  @input="textareaBInput" -->
    <textarea
      maxlength="{{500}}"
      :disabled="modalName!=null"
      placeholder-class="area-pla"
      class="textarea"
      placeholder="请输入跟进内容（必填）"
      bindinput="tareaChange"
      value="{{form.content}}"
    >
    </textarea>
    <view class="word-number">{{wordNum|0}}/500</view>
  </view>
  <!-- 联系方式 -->
  <view class="sug-phone">
    <view class="sug-phone-l">
      <text class="sug-text">联系人</text>
    </view>
    <input
      type="number"
      class="sug-input"
      placeholder="{{form.contact_person_id?'已选择':'请选择联系人'}}"
      bindtap="inputChange"
      value="{{form.name}}"
      placeholder-class="input-pla"
      disabled
    />
    <view class="arrow-r flex_all_center">
      <image
        src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/cyt_v2/image/business/arrow-r.png"
      ></image>
    </view>
  </view>
  <!-- 上传图片---ps:这里应该要在开发工具里面配置一个图片上传的地址 -->
  <view class="sug-imgbox-t">图片</view>
  <view class="sug-imgbox">
    <UploadFile bindchange="uploadfile" />
  </view>
  <!-- 按钮 style="height: {{isIphoneX?'168rpx':'100rpx'}};"-->
  <view class="sug-btm">
    <view class="sug-btn" ontap="goResult">保存</view>
  </view>
  <!-- 选择联系人-底部弹窗 -->
  <half-screen-pop
    title="选择联系人"
    position="bottom"
    visible="{{visible}}"
    zIndex="{{10}}"
  >
    <view slot="icon">
      <view class="addperson" bindtap="goEdit">
        <view class="img flex_all_center">
          <image
            src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/cyt_v2/image/business/add.png"
            mode="aspectFit"
          ></image>
        </view>
        <view>联系人</view>
      </view>
    </view>
    <view slot="customContent">
      <scroll-view scroll-with-animation scroll-y class="person-box">
        <block wx:if="{{personList.length>0}}">
          <view
            wx:for="{{personList}}"
            wx:key="index"
            style="margin-top: {{index==0?'32rpx':'0'}};"
          >
            <PersonCard
              selectContact="{{selectContact}}"
              isShow="{{false}}"
              obj="{{item}}"
              bindtap="getCard"
              data-item="{{item}}"
            >
            </PersonCard>
          </view>
        </block>
        <block wx:else>
          <template is="null"></template>
        </block>
      </scroll-view>
    </view>
  </half-screen-pop>
</view>
