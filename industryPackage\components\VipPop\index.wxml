<!-- 从下往上的弹窗 -->
<view
  wx:if="{{visible}}"
  class="vip-popup-mask {{visible ? 'show' : 'hide'}}"
  catchtap="onMaskTap"
  catchtouchmove="preventTouchMove"
>
  <!-- 弹窗内容容器 -->
  <view
    class="vip-popup-container {{visible ? 'slide-up' : 'slide-down'}}"
    catchtap="preventBubble"
  >
    <image
      src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/cyt_v2/image/industry/i_vip1.png"
      class="vip-image1"
    ></image>
    <view catchtap="onMaskTap" class="zhanwei"></view>
    <view class="images-container">
      <scroll-view scroll-y class="scroll_view">
        <image
          src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/cyt_v2/image/industry/i_vip2.png"
          class="vip_image2"
          mode="widthFix"
        ></image>
      </scroll-view>

      <image
        src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/cyt_v2/image/industry/i_vip3.png"
        class="vip_mage3"
        mode="widthFix"
      ></image>

      <image
        src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/cyt_v2/image/industry/i_vip4.png"
        class="vip_button"
        mode="widthFix"
        catchtap="onButtonTap"
      ></image>
    </view>
  </view>
</view>
