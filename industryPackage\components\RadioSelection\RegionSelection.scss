.region-container {
  height: 712rpx;
  width: 100%;
  position: relative;
}

.region-wrap {
  height: 100%;
  width: 100%;
  display: flex;
  font-weight: 400;
  font-size: 26rpx;
  color: #525665;
}

.region-wrap .list {
  flex: 1; /* 使用flex布局，每列平均分配宽度 */
  height: 100%;
  overflow-y: auto;
  box-sizing: border-box; /* 确保padding不会导致溢出 */
}

.region-wrap .list .item {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx 4.13% 30rpx 4.67%; /* 31rpx/750rpx=4.13%, 35rpx/750rpx=4.67% */
  box-sizing: border-box;
}

.province-list {
  background-color: rgba(247, 247, 247, 0.6);
}

.province-list .item.active {
  background-color: #fff;
}

.province-list .item.selected {
  background-color: #fff;
  font-weight: 600;
  font-size: 26rpx;
  color: #e72410;
}

.city-list {
  background-color: #fff;
  position: relative;

  /* 左边框分隔线 */
  &::before {
    content: " ";
    position: absolute;
    left: 0;
    top: 0;
    width: 1px;
    height: 100%;
    background: #eeeeee;
    transform: scaleX(0.5);
  }
}

.city-list .item.selected {
  font-weight: 600;
  font-size: 26rpx;
  color: #e72410;
}

.area-list {
  background-color: #fff;
  position: relative;

  /* 左边框分隔线 */
  &::before {
    content: " ";
    position: absolute;
    left: 0;
    top: 0;
    width: 1px;
    height: 100%;
    background: #eeeeee;
    transform: scaleX(0.5);
  }
}

.area-list .item.selected {
  font-weight: 600;
  font-size: 26rpx;
  color: #e72410;
}

.selected-text {
  color: #e72410 !important;
  font-size: 26rpx;
  font-weight: 600 !important;
}
.checkmark {
  width: 32rpx; /* 28rpx / 750rpx = 3.73% */
  height: 32rpx; /* 高度保持固定，避免变形 */
  flex-shrink: 0; /* 防止图标被压缩 */
}

.checkmark.show {
  opacity: 1;
}

.placeholder-content {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.placeholder-text {
  font-size: 28rpx;
  color: #999;
}

/* width90 class removed - components now auto-adapt to container width */
