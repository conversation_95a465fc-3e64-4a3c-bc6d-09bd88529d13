/* childSubpackage/pages/projectManagement/components/MemberList/memberList.scss */
@import '../../../../../template/null/null.scss';
.member-wrap {
  padding: 20rpx 24rpx 0;
  border-top: 1rpx solid #EEEEEE;
  height: 542rpx;
}
.member {
  padding-bottom: 1rpx;
}
.member .member-item {
  width: 100%;
  height: 100rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 58rpx 0 28rpx;
  color: #20263A;
  border-radius: 16rpx;
  border: 2rpx solid #EEEEEE;
  margin-bottom: 20rpx;
}
.member .member-item .name {
  font-size: 32rpx;
}
.member .member-item .number {
  font-size: 28rpx;
}
.member-item .number .label {
  color: #9B9EAC;
}
.member .member-item.active {
  border: 2rpx solid #E72410;
}
.footer {
  height: 220rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 24rpx;
}
.footer .btn {
  width: 340rpx;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  color: #20263A;
  font-size: 32rpx;
  border-radius: 8rpx;
  background: linear-gradient(315deg, #EEEEEE 0%, #F5F5F5 100%);
}
.footer .btn.submit {
  background: linear-gradient(90deg, #F56E60 0%, #E72410 100%);
  color: #FFFFFF;
}