<import src="/template/more/more"></import>
<import src="/template/loading/index"></import>
<view class="search-card" wx:for="{{souceData}}" wx:key="index">
  <view class="card-boxs">
    <view class="card-logo" bindtap="goDetail" data-item="{{item}}">
      <image
        src="{{item.logo=='' ?'https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/default.png':item.logo }}"
        binderror="errorFunction"
        data-index="{{index}}"
      ></image>
    </view>
    <view class="card-head">
      <view class="card_h">
        <view
          class="card_h_l text-ellipsis "
          bindtap="goDetail"
          data-item="{{item}}"
        >
          <rich-text nodes="{{item.ent_name_show}}" />
        </view>
        <view
          class="card_h_r"
          bindtap="delet"
          data-item="{{item}}"
          data-index="{{index}}"
        >
          <!-- 后面根据具体传进来的字段判断 -->
          <view class="card_h_r_img">
            <image
              src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/cyt_v2/image/delet.png"
            ></image>
          </view>
        </view>
      </view>
      <view class="card_tag">
        <view class="card_tag_box">
          <text
            wx:for="{{item.tagsShow}}"
            style="{{tagItem.type==1?'background: rgba(160,165,186,0.35);color: #A0A5BA ;':''}}"
            wx:for-item="tagItem"
            wx:key="tagName"
            >{{tagItem.name}}</text
          >
          <text
            class="{{tagItem.tagColor}}"
            wx:for="{{item.tags}}"
            wx:for-item="tagItem"
            wx:key="tagName"
            >{{tagItem.tagName}}</text
          >
        </view>
      </view>
      <view class="card_c"> 收藏时间 {{item.collect_date}} </view>
    </view>
  </view>

  <view class="card_ico">
    <view class="card_ico_i" bindtap="relation" data-item="{{item}}">
      <view class="card_ico_i_img">
        <image
          src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/home/<USER>"
        ></image>
      </view>
      <view> 联系方式 </view>
    </view>
    <view class="card_ico_i" bindtap="official" data-item="{{item}}">
      <view class="card_ico_i_img">
        <image
          src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/home/<USER>"
        ></image>
      </view>
      <view> 官网 </view>
    </view>
    <view class="card_ico_i" bindtap="site" data-item="{{item}}">
      <view class="card_ico_i_img">
        <image
          src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/home/<USER>"
        ></image>
      </view>
      <view> 地址 </view>
    </view>
  </view>
</view>
<!-- 未登录只能查看5条数据 -->
<view class="vip" wx:if="{{!isLogin}}">
  <vloginOverlay bindsubmit="login"></vloginOverlay>
</view>
<!-- 开通vip -->
<view class="vip" wx:if="{{permission=='普通VIP'&&souceList.length>=10}}">
  <vipOccupancy bindsubmit="vipPop"></vipOccupancy>
</view>
