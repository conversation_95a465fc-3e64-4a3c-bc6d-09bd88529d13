var echarts = require('../ec-canvas/echarts');
import {getPx} from '../../../utils/formate';

const CHINA_CODE = '1000000';
let myChart = null;

// 删除内部mock数据，改为从外部传入

Component({
  properties: {
    // 地图区域配置
    region: {
      type: Object,
      value: {},
      observer(val) {
        this.getMapData(val);
      }
    },
    // 热力图数据
    heatMapData: {
      type: Array,
      value: [],
      observer(val) {
        this.setData(
          {
            hotMapData: val ? val : []
          },
          () => {
            this.initMap();
          }
        );
      }
    },
    // 地图类型
    mapType: {
      type: String,
      value: 'province' // province | city
    },
    // 自定义样式
    customStyle: {
      type: String,
      value: ''
    },
    // 标记点数据 - Level 3时使用
    markPoints: {
      type: Array,
      value: [],
      observer() {
        // 当标记点数据变化时，如果是Level 3则重新初始化地图
        const region = this.properties.region;
        const isLevel3 = region && (region.level === '3' || region.level === 3);
        if (isLevel3 && this.data.mapJson && this.data.mapJson.type) {
          this.initMap();
        }
      }
    }
  },

  data: {
    ec: {
      lazyLoad: true
    },
    forceUseOldCanvas: false,
    map: 'china',
    currentCode: CHINA_CODE,
    mapJson: {},
    chinaMap: {},
    hotMapData: []
  },

  lifetimes: {
    attached() {
      // 检测开发者工具
      wx.getSystemInfo({
        success: res =>
          res.platform == 'devtools' &&
          this.setData({
            forceUseOldCanvas: true
          })
      });

      // 不再主动调用getMapData，完全依赖外部传入的region和heatMapData
    }
  },

  methods: {
    // 获取地图数据
    getMapData(region) {
      const that = this;
      let {currentCode, mapJson, chinaMap, map} = that.data;

      map = region?.code === CHINA_CODE ? 'china' : region.name;
      const isLevel3 = region.level === '3';
      const isLevel2 = region.level === '2';
      currentCode = region?.code == 'ALL' ? CHINA_CODE : region.code;

      if (isLevel3) {
        // 区县编码拿父亲的
        currentCode = region.parent;
      }

      wx.request({
        url: `https://feifeife.oss-cn-beijing.aliyuncs.com/opensource/map_json_1.0/json/${currentCode}.json`,
        method: 'GET',
        success: res => {
          mapJson = res.data;

          // console.log('currentCode', currentCode);
          // console.log('mapJson', mapJson);

          // 验证地图数据格式
          if (
            !mapJson ||
            !mapJson.features ||
            !Array.isArray(mapJson.features) ||
            mapJson?.features?.[0].geometry === null
          ) {
            console.log('地图数据格式不正确:', mapJson);

            // 直接调用错误处理逻辑，而不是抛出异常
            if (isLevel2) {
              console.log('准备去拿父级的:', mapJson);
              this._fallbackToParent(region);
            }
            return;
          }

          // 处理Level3数据过滤
          if (isLevel3) {
            mapJson = {
              type: res.data.type,
              features: res.data.features.filter(
                item =>
                  String(item.properties.adcode) === String(region.code) ||
                  item.properties.name === region.name
              )
            };
          }

          if (currentCode === CHINA_CODE) {
            chinaMap = res.data;
          }

          echarts.registerMap(
            currentCode === CHINA_CODE ? 'china' : 'custom',
            mapJson
          );

          this.setData(
            {
              currentCode,
              mapJson,
              chinaMap,
              map
            },
            () => {
              this.initMap({
                geo: {
                  map: currentCode === CHINA_CODE ? 'china' : 'custom'
                }
              });
            }
          );
        },
        fail: err => {
          console.error('地图数据加载失败:', err);

          // 只对Level2进行回退处理
          if (isLevel2) {
            console.warn(`Level2 数据获取失败，回退到父级: ${region.code}`);
            this._fallbackToParent(region);
          } else {
            console.error('地图加载失败，无回退策略');
          }
        }
      });
    },

    // Level2回退到父级地图
    _fallbackToParent(region) {
      const that = this;
      let {mapJson, chinaMap} = that.data;

      // 直接使用传入的父级编码
      const parentCode = region.parent;

      if (!parentCode) {
        console.log('编码和地图编码对不上');
        return;
      }
      wx.request({
        url: `https://feifeife.oss-cn-beijing.aliyuncs.com/opensource/map_json_1.0/json/${parentCode}.json`,
        method: 'GET',
        success: res => {
          // 验证返回的数据格式
          if (
            !res.data ||
            !res.data.features ||
            !Array.isArray(res.data.features)
          ) {
            return;
          }

          mapJson = res.data;
          // 从父级地图中过滤出目标区域
          const targetFeatures = mapJson.features.filter(item => {
            const match =
              String(item.properties.adcode) === String(region.code) ||
              item.properties.name === region.name ||
              item.properties.name.includes(region.name) ||
              region.name.includes(item.properties.name);
            return match;
          });
          if (targetFeatures.length > 0) {
            mapJson = {
              type: mapJson.type,
              features: targetFeatures
            };
          } else {
            console.warn('未找到匹配的区域，使用完整的父级地图');
            // 如果找不到精确匹配，使用完整的父级地图
            mapJson = res.data;
          }

          // 如果父级是全国，更新chinaMap
          if (parentCode === CHINA_CODE) {
            chinaMap = res.data;
          }

          echarts.registerMap(
            parentCode === CHINA_CODE ? 'china' : 'custom',
            mapJson
          );

          this.setData(
            {
              currentCode: parentCode,
              mapJson,
              chinaMap,
              map: parentCode === CHINA_CODE ? 'china' : region.name
            },
            () => {
              this.initMap({
                geo: {
                  map: parentCode === CHINA_CODE ? 'china' : 'custom'
                }
              });
            }
          );
        },
        fail: err => {
          console.log('编码和地图编码对不上', err);
        }
      });
    },

    // 初始化地图
    initMap(options = {}) {
      let {mapJson, hotMapData} = this.data;

      if (!mapJson.type) return;

      this.chartComponent = this.selectComponent('#mychart-map');
      this.chartComponent.init((canvas, width, height, dpr) => {
        myChart = echarts.init(canvas, null, {
          width: width,
          height: height,
          devicePixelRatio: dpr
        });

        const option = {
          tooltip: {
            trigger: 'item',
            formatter: params => {
              return `${params.name} ${
                isNaN(params.value) ? 0 : params.value
              }家企业`;
            }
          },
          geo: [this.getGeo(options?.geo)],
          visualMap: [],
          series: []
        };

        // 判断是否为层级3
        const region = this.properties.region;
        const isLevel3 = region && (region.level === '3' || region.level === 3);

        if (isLevel3) {
          // 使用外部传入的markPoints数据，并进行边界验证
          const markPointsData = this.properties.markPoints || [];
          const validMarkPoints = this._filterMarkPointsByBounds(
            markPointsData,
            mapJson,
            region
          );
          const markPointData = validMarkPoints.map(point => ({
            name: point.name,
            value: point.coord // scatter系列的value是坐标
          }));

          // 添加散点图系列来显示标记点
          option.series.push({
            name: '标记点',
            type: 'scatter',
            coordinateSystem: 'geo',
            geoIndex: 0,
            silent: false, // 启用点击事件
            symbol: 'pin', // 使用内置的pin图标
            symbolSize: 18, // 调整大小
            itemStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: 'rgba(255,255,255,0.5)' // 顶部颜色
                  },
                  {
                    offset: 1,
                    color: '#fff' // 底部颜色
                  }
                ],
                global: false // 相对图形渐变
              },
              borderColor: '#2F7CFF',
              borderWidth: 4
            },
            label: {
              show: false, // 默认不显示
              position: 'top',
              color: '#FFFFFF', // 白色文字
              fontSize: 12, // 24rpx转换为px约12px
              fontFamily: 'PingFang SC',
              fontWeight: 400,
              backgroundColor: '#2F7CFF', // 蓝色背景
              borderColor: '#1E75DB',
              borderWidth: 1,
              borderRadius: 4, // 8rpx转换为px约4px
              padding: [6, 12], // 上下0，左右12rpx转换为px约6px
              formatter: '{b}' // 显示name
            },
            tooltip: {
              show: false // 禁用tooltip弹窗
            },
            data: markPointData.map(point => ({
              ...point,
              label: {
                show: false // 每个点默认不显示label
              }
            }))
          });
        } else {
          // 层级1和2：显示热力图
          const json = mapJson.features.map(item => {
            return {
              ...item,
              ...item.properties
            };
          });

          const data = this.mergeArray(hotMapData, json).sort(
            (a, b) => a.value - b.value
          );
          const seriesIndex = option.series.length;

          option.visualMap.push(
            this.getVisualMap(data, {
              seriesIndex
            })
          );

          option.series[seriesIndex] = {
            name: '',
            type: 'map',
            data: data,
            geoIndex: 0,
            selectedMode: false
          };
        }

        myChart.setOption(option);

        // 添加点击事件
        if (isLevel3) {
          // 层级3：处理标记点点击事件 后面再看放开不
          // myChart.on('click', params => {
          //   if (params.seriesName === '标记点') {
          //     this._toggleMarkPointLabel(myChart, params, option);
          //   }
          // });
        } else {
          // 其他层级：处理地图区域点击事件
          myChart.on('click', params => {
            this.triggerEvent('clickMap', {
              region: this.properties.region,
              data: params,
              mapType: this.properties.mapType
            });
          });
        }

        return myChart;
      });
    },

    // 获取地理配置
    getGeo(options = {}) {
      let {currentCode} = this.data;

      // 检查地图层级
      const region = this.properties.region;
      const isLevel1 =
        !region || region.level === '1' || region.code === '1000000';
      const isLevel2 = region && (region.level === '2' || region.level === 2);
      const isLevel3 = region && (region.level === '3' || region.level === 3);

      // 根据层级动态设置布局参数
      let layoutConfig = {};

      if (isLevel1 || isLevel2) {
        // 全国地图 - 使用原有配置
        layoutConfig = {
          layoutCenter: ['50%', '48%'],
          layoutSize: currentCode === '1000000' ? 322 : '90%',
          aspectScale: 0.8
        };
      } else if (isLevel3) {
        // 市级/区县地图 - 需要最大化显示
        layoutConfig = {
          layoutCenter: ['50%', '50%'],
          layoutSize: '90%', // 更大的显示区域
          aspectScale: 1.0
        };
      } else {
        // 默认配置
        layoutConfig = {
          layoutCenter: ['50%', '50%'],
          layoutSize: '85%',
          aspectScale: 1.0
        };
      }

      const baseConfig = {
        type: 'map',
        map: currentCode === CHINA_CODE ? 'china' : 'custom',
        roam: false,
        ...layoutConfig, // 应用动态布局配置
        label: {
          show: false,
          emphasis: {
            show: false,
            textStyle: {
              color: '#fff'
            }
          }
        }
      };

      if (isLevel3) {
        // 层级3的特殊配置
        baseConfig.silent = true; // 禁用所有交互
        baseConfig.itemStyle = {
          normal: {
            areaColor: '#95C6FF',
            borderColor: '#389dff',
            borderWidth: 1
          },
          emphasis: {
            areaColor: '#95C6FF', // 悬停也保持同样颜色
            borderColor: '#389dff',
            borderWidth: 1
          }
        };
      } else {
        // Level 1 和其他层级的默认配置
        baseConfig.itemStyle = {
          normal: {
            areaColor: '#fff',
            borderColor: '#389dff',
            borderWidth: 0.5
          },
          emphasis: {
            areaColor: '#2e317c',
            shadowOffsetX: 0,
            shadowOffsetY: 0,
            borderWidth: 0
          }
        };
      }

      return {
        ...baseConfig,
        ...options
      };
    },

    // 合并数组 - 使用includes进行模糊匹配
    mergeArray(ary1, ary2) {
      const result = [];

      for (let i = 0; i < ary2.length; i++) {
        const item1 = ary2[i]; // 地图数据项

        // 使用includes进行双向匹配，提高匹配成功率
        const find = ary1.find(item => {
          // 去除常见的行政区划后缀进行匹配
          const mapName = item1.name.replace(
            /[市|省|区|县|自治区|特别行政区|维吾尔自治区|回族自治区|壮族自治区]$/g,
            ''
          );
          const dataName = item.name.replace(
            /[市|省|区|县|自治区|特别行政区|维吾尔自治区|回族自治区|壮族自治区]$/g,
            ''
          );

          // 双向包含匹配
          return (
            item1.name.includes(item.name) ||
            item.name.includes(item1.name) ||
            mapName.includes(dataName) ||
            dataName.includes(mapName)
          );
        });

        const res = find
          ? {
              ...item1,
              name: item1.name, // 保持地图原始名称
              value: find.value // 使用匹配到的数值
            }
          : {
              ...item1,
              name: item1.name,
              value: 0
            };
        result.push(res);
      }
      return result;
    },

    // 获取视觉映射配置
    getVisualMap(data, options) {
      const min = data[0]?.value || 0;
      const max = data[data.length - 1]?.value || 0;

      return {
        show: true,
        left: getPx(0),
        bottom: '0',
        max,
        min,
        calculable: true,
        realtime: false,
        realtime: false,
        itemWidth: getPx(40),
        itemHeight: getPx(140),
        align: 'left',
        textGap: getPx(8),
        text: [], // 显示格式化数值
        // 如果不想显示任何文字，可以使用: text: [],
        inRange: {
          color: ['rgba(242, 248, 255, 1)', 'rgba(63, 152, 255, 1)'],
          symbolSize: [0, 80]
        },
        textStyle: {
          color: '#282D30',
          backgroundColor: 'transparent',
          shadowColor: 'transparent',
          shadowBlur: 0,
          shadowOffsetX: 0,
          shadowOffsetY: 0
        },
        seriesIndex: 0,
        ...options
      };
    },

    /**
     * 根据地图边界过滤标记点
     * @param {Array} markPoints - 原始标记点数据
     * @param {Object} mapJson - 地图JSON数据
     * @param {Object} region - 区域信息
     * @returns {Array} 过滤后的有效标记点
     */
    _filterMarkPointsByBounds(markPoints, mapJson, region) {
      if (!markPoints || !markPoints.length || !mapJson || !region) {
        return markPoints || [];
      }

      // 查找目标区域的几何边界
      const targetFeature = mapJson.features.find(
        feature => String(feature.properties.adcode) === String(region.code)
      );

      if (!targetFeature || !targetFeature.geometry) {
        // console.warn(`未找到区域 ${region.code} 的边界数据，不进行过滤`);
        return markPoints;
      }

      // 过滤标记点
      const validMarkPoints = markPoints.filter(point => {
        if (!this._isValidCoordinate(point.coord)) {
          return false;
        }
        return this._isPointInGeometry(point.coord, targetFeature.geometry);
      });

      // console.log(
      //   `区域 ${region.name || region.code} 标记点过滤: ${
      //     markPoints.length
      //   } -> ${validMarkPoints.length}`
      // );

      return validMarkPoints;
    },

    /**
     * 验证坐标是否有效
     * @param {Array} coord - 坐标 [lon, lat]
     * @returns {boolean} 是否有效
     */
    _isValidCoordinate(coord) {
      if (!Array.isArray(coord) || coord.length !== 2) {
        return false;
      }

      const [lon, lat] = coord;
      const longitude = parseFloat(lon);
      const latitude = parseFloat(lat);

      // 检查是否为有效数字
      if (isNaN(longitude) || isNaN(latitude)) {
        return false;
      }

      // 检查坐标是否在中国境内的合理范围
      // 中国经度范围：73°33′E 至 135°05′E
      // 中国纬度范围：3°51′N 至 53°33′N
      return (
        longitude >= 73.33 &&
        longitude <= 135.05 &&
        latitude >= 3.85 &&
        latitude <= 53.55
      );
    },

    /**
     * 判断点是否在几何图形内
     * @param {Array} point - 点坐标 [lon, lat]
     * @param {Object} geometry - GeoJSON几何对象
     * @returns {boolean} 是否在几何图形内
     */
    _isPointInGeometry(point, geometry) {
      const [x, y] = point.map(coord => parseFloat(coord));

      if (geometry.type === 'Polygon') {
        return this._pointInPolygonRing(x, y, geometry.coordinates[0]);
      } else if (geometry.type === 'MultiPolygon') {
        // 对于MultiPolygon，检查是否在任一多边形内
        return geometry.coordinates.some(polygon =>
          this._pointInPolygonRing(x, y, polygon[0])
        );
      }

      return false;
    },

    /**
     * 射线法判断点是否在多边形环内
     * @param {number} x - 点的x坐标（经度）
     * @param {number} y - 点的y坐标（纬度）
     * @param {Array} ring - 多边形环的坐标数组
     * @returns {boolean} 是否在多边形内
     */
    _pointInPolygonRing(x, y, ring) {
      let inside = false;

      for (let i = 0, j = ring.length - 1; i < ring.length; j = i++) {
        const xi = ring[i][0];
        const yi = ring[i][1];
        const xj = ring[j][0];
        const yj = ring[j][1];

        if (yi > y !== yj > y && x < ((xj - xi) * (y - yi)) / (yj - yi) + xi) {
          inside = !inside;
        }
      }

      return inside;
    },

    /**
     * 切换标记点label的显示状态
     * @param {Object} chart - ECharts实例
     * @param {Object} params - 点击事件参数
     * @param {Object} option - 图表配置选项
     */
    _toggleMarkPointLabel(chart, params, option) {
      if (!chart || !params || !option) return;

      const seriesIndex = params.seriesIndex;
      const dataIndex = params.dataIndex;

      if (seriesIndex === undefined || dataIndex === undefined) return;

      // 获取当前系列数据
      const currentSeries = option.series[seriesIndex];
      if (!currentSeries || !currentSeries.data) return;

      // 切换当前点的label显示状态
      const currentPoint = currentSeries.data[dataIndex];
      if (!currentPoint) return;

      // 如果当前点没有label配置，初始化
      if (!currentPoint.label) {
        currentPoint.label = {show: false};
      }

      // 切换显示状态
      const newShowState = !currentPoint.label.show;

      // 更新当前点的label配置
      currentPoint.label = {
        show: newShowState,
        position: 'top',
        color: '#FFFFFF',
        fontSize: 12,
        fontFamily: 'PingFang SC',
        fontWeight: 400,
        backgroundColor: '#2F7CFF',
        borderColor: '#1E75DB',
        borderWidth: 1,
        borderRadius: 4,
        padding: [6, 12],
        formatter: '{b}'
      };

      // 如果要显示新的label，隐藏其他所有label（可选：只显示一个）
      if (newShowState) {
        currentSeries.data.forEach((point, index) => {
          if (index !== dataIndex && point.label) {
            point.label.show = false;
          }
        });
      }

      // 重新设置图表选项
      chart.setOption(option, false);

      console.log(
        `标记点 "${params.name}" label ${newShowState ? '显示' : '隐藏'}`
      );
    }
  }
});
