<view class="table-component">
  <!-- 表格内容 -->
  <view wx:if="{{!isEmpty && !loading}}" class="node-list">
    <!-- 固定第一列的表格布局 -->
    <view class="table-container fixed-first-column">
      <view class="table-wrapper">
        <!-- 固定的第一列 -->
        <view class="fixed-column">
          <!-- 第一列表头 -->
          <view class="table-cell table-header-cell fixed-header-cell">
            {{firstColumn.title}}
          </view>
          <!-- 第一列数据 -->
          <view
            wx:for="{{firstColumnData}}"
            wx:for-item="cell"
            wx:key="index"
            class="table-cell table-body-cell fixed-body-cell {{cell.merged ? 'merged-cell' : ''}} {{cell.isMergedMain ? 'merged-main-cell' : ''}}"
            style="{{cell.style}}"
          >
            {{cell.content}}
          </view>
        </view>

        <!-- 可滚动的其他列 -->
        <scroll-view
          class="scrollable-columns"
          scroll-x="{{true}}"
          enhanced="{{true}}"
          show-scrollbar="{{false}}"
        >
          <view class="scrollable-wrapper">
            <!-- 其他列表头 -->
            <view class="table-header">
              <view class="table-row other">
                <view
                  wx:for="{{otherColumns}}"
                  wx:key="index"
                  class="table-cell table-header-cell"
                  style="{{item.style}}"
                >
                  {{item.title}}
                </view>
              </view>
            </view>

            <!-- 其他列数据 -->
            <view class="table-body">
              <view
                wx:for="{{otherColumnsData}}"
                wx:for-item="row"
                wx:for-index="rowIndex"
                wx:key="rowIndex"
                class="table-row"
              >
                <view
                  wx:for="{{row.cells}}"
                  wx:for-item="cell"
                  wx:for-index="colIndex"
                  wx:key="colIndex"
                  class="table-cell table-body-cell"
                  style="{{cell.style}}"
                >
                  {{cell.content || '-'}}
                </view>
              </view>
            </view>
          </view>
        </scroll-view>
      </view>
    </view>
  </view>

  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading-div">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 空状态 -->
  <view wx:if="{{isEmpty}}" class="empty-div">
    <image
      class="empty-icon"
      src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/null.png"
      mode="aspectFit"
    ></image>
    <text class="empty-text">暂无数据</text>
  </view>
</view>
