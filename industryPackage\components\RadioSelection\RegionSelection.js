import {getData} from '../../../components/MultiplecChoice/utils.js';

Component({
  properties: {
    visible: {
      type: Boolean,
      value: false
    },
    position: {
      type: String,
      value: 'top'
    },
    startDistance: {
      type: String,
      value: '0px'
    },
    defaultCode: {
      type: null, // 支持多种类型：String、Object、Array
      value: ''
    },
    top: {
      type: Number,
      value: 0
    },
    dataType: {
      type: String,
      value: 'districtAry'
    },
    zIndex: {
      type: Number,
      value: 10
    }
  },

  data: {
    regionObj: {
      provinceList: [],
      curCityList: [],
      curAreaList: []
    },
    cityObj: {},
    areaObj: {},
    selectedPath: {
      country: null,
      province: null,
      city: null,
      area: null
    },
    finalSelection: null,
    readyToShow: false // 控制是否准备好显示内容
  },

  lifetimes: {
    attached() {
      // 初始化时先不显示内容
      this.setData({readyToShow: false});
      this.getRegion();
    }
  },

  // 将整个 observers 替换为：
  observers: {
    visible: function (bl) {
      if (bl && this.data.defaultCode) {
        // 有回显代码时，先隐藏内容，等数据处理完再显示
        this.setData({readyToShow: false});
        const code = this.extractCodeFromDefaultCode(this.data.defaultCode);
        // 只有当提取出有效的code时才展开
        if (code) {
          this.expandToCode(code);
        } else {
          // 没有有效code时直接显示
          this.setData({readyToShow: true});
        }
      } else if (bl) {
        // 没有回显代码时，直接显示
        this.setData({readyToShow: true});
      }
    },
    defaultCode: function (code) {
      if (code && this.data.visible) {
        this.setData({readyToShow: false});
        const extractedCode = this.extractCodeFromDefaultCode(code);
        // 只有当提取出有效的code时才展开
        if (extractedCode) {
          this.expandToCode(extractedCode);
        } else {
          // 没有有效code时直接显示
          this.setData({readyToShow: true});
        }
      }
    }
  },
  methods: {
    /**
     * 从 defaultCode 中提取 code 值
     * @param {String|Object|Array} defaultCode - 默认代码
     * @returns {String} 提取的 code 值
     */
    extractCodeFromDefaultCode(defaultCode) {
      if (!defaultCode) return '';

      // 如果是字符串，直接返回
      if (typeof defaultCode === 'string') {
        return defaultCode;
      }

      // 如果是数组，取第一个元素的 code
      if (Array.isArray(defaultCode)) {
        if (defaultCode.length === 0) return '';
        const firstItem = defaultCode[0];
        if (typeof firstItem === 'string') {
          return firstItem;
        }
        if (typeof firstItem === 'object' && firstItem.code) {
          return firstItem.code;
        }
        return '';
      }

      // 如果是对象，取 code 属性
      if (typeof defaultCode === 'object' && defaultCode.code) {
        return defaultCode.code;
      }

      return '';
    },

    async expandToCode(targetCode) {
      if (!targetCode) return;
      const {regionObj, cityObj, areaObj} = this.data;
      let selectedPath = {
        province: null,
        city: null,
        area: null,
        country: null
      };

      // 先确保有省级数据
      if (regionObj.provinceList.length === 0) {
        await this.getRegion();
      }

      // 查找目标code属于哪一级
      let targetLevel = '';
      let targetItem = null;
      let parentCode = '';

      // 1. 先在省级中查找
      for (let province of regionObj.provinceList) {
        if (province.code === targetCode) {
          targetLevel = 'province';
          targetItem = province;
          break;
        }
      }

      // 2. 如果不在省级，需要遍历所有省的市级数据查找
      if (!targetLevel) {
        // 先检查已缓存的市级数据
        for (let provinceCode in cityObj) {
          for (let city of cityObj[provinceCode]) {
            if (city.code === targetCode) {
              targetLevel = 'city';
              targetItem = city;
              parentCode = provinceCode;
              break;
            }
          }
          if (targetLevel) break;
        }

        // 如果还没找到，加载所有省的市级数据进行查找
        if (!targetLevel) {
          for (let province of regionObj.provinceList) {
            if (province.code === 'All') continue; // 跳过全国
            if (!cityObj[province.code]) {
              await this.loadCityData(province.code);
            }
            for (let city of cityObj[province.code]) {
              if (city.code === targetCode) {
                targetLevel = 'city';
                targetItem = city;
                parentCode = province.code;
                break;
              }
            }
            if (targetLevel) break;
          }
        }
      }

      // 3. 如果不在市级，查找区级
      if (!targetLevel) {
        // 先检查已缓存的区级数据
        for (let cityCode in areaObj) {
          for (let area of areaObj[cityCode]) {
            if (area.code === targetCode) {
              targetLevel = 'area';
              targetItem = area;
              parentCode = cityCode;
              break;
            }
          }
          if (targetLevel) break;
        }

        // 如果还没找到，需要加载更多区级数据
        if (!targetLevel) {
          for (let provinceCode in cityObj) {
            for (let city of cityObj[provinceCode]) {
              if (city.isarea) continue; // 跳过直辖市的区
              if (!areaObj[city.code]) {
                await this.loadAreaData(city.code);
              }
              for (let area of areaObj[city.code]) {
                if (area.code === targetCode) {
                  targetLevel = 'area';
                  targetItem = area;
                  parentCode = city.code;
                  break;
                }
              }
              if (targetLevel) break;
            }
            if (targetLevel) break;
          }
        }
      }

      // 根据找到的级别进行展开
      if (targetLevel === 'province') {
        selectedPath.province = targetItem;
        await this.loadCityData(targetCode);
      } else if (targetLevel === 'city') {
        // 找到对应的省
        const province = regionObj.provinceList.find(
          p => p.code === parentCode
        );
        selectedPath.province = province;
        selectedPath.city = targetItem;
        await this.loadCityData(parentCode);
        if (!targetItem.isarea) {
          await this.loadAreaData(targetCode);
        }
      } else if (targetLevel === 'area') {
        // 需要找到对应的省和市
        let cityCode = parentCode;
        let provinceCode = '';

        // 找到市对应的省
        for (let pCode in cityObj) {
          if (cityObj[pCode].find(c => c.code === cityCode)) {
            provinceCode = pCode;
            break;
          }
        }

        const province = regionObj.provinceList.find(
          p => p.code === provinceCode
        );
        const city = cityObj[provinceCode]?.find(c => c.code === cityCode);

        selectedPath.province = province;
        selectedPath.city = city;
        selectedPath.area = targetItem;

        await this.loadCityData(provinceCode);
        await this.loadAreaData(cityCode);
      }
      this.setData({
        selectedPath,
        finalSelection: this.getFinalSelection(selectedPath),
        readyToShow: true // 数据处理完成，可以显示了
      });

      this.updateListSelections();
    },

    async loadCityData(provinceCode) {
      const {cityObj, regionObj} = this.data;
      if (!cityObj[provinceCode]) {
        const cityList = await getData({
          code: provinceCode,
          dataType: this.data.dataType
        });
        // 处理直辖市的区级数据，标记为 isarea
        const processedCityList = cityList.map(item => {
          if (item.level === '3') {
            item.isarea = true;
          }
          return item;
        });
        cityObj[provinceCode] = processedCityList;
        this.setData({cityObj});
      }
      regionObj.curCityList = cityObj[provinceCode] || [];
      this.setData({regionObj});
    },

    async loadAreaData(cityCode) {
      const {areaObj, regionObj} = this.data;
      if (!areaObj[cityCode]) {
        const areaList = await getData({
          code: cityCode,
          dataType: this.data.dataType
        });
        areaObj[cityCode] = areaList;
        this.setData({areaObj});
      }
      regionObj.curAreaList = areaObj[cityCode] || [];
      this.setData({regionObj});
    },

    updateListSelections() {
      const {regionObj, selectedPath} = this.data;

      regionObj.provinceList = regionObj.provinceList.map(item => ({
        ...item,
        selected:
          selectedPath.country?.code === item.code ||
          selectedPath.province?.code === item.code,
        active:
          selectedPath.country?.code === item.code ||
          selectedPath.province?.code === item.code
      }));

      regionObj.curCityList = regionObj.curCityList.map(item => ({
        ...item,
        selected: selectedPath.city?.code === item.code,
        active: selectedPath.city?.code === item.code
      }));

      regionObj.curAreaList = regionObj.curAreaList.map(item => ({
        ...item,
        selected: selectedPath.area?.code === item.code
      }));

      this.setData({regionObj});
    },

    getFinalSelection(path = null) {
      const selectedPath = path || this.data.selectedPath;
      return (
        selectedPath.area ||
        selectedPath.city ||
        selectedPath.province ||
        selectedPath.country
      );
    },

    async selectRegion(e) {
      const dataset = e.currentTarget.dataset;
      const {code, level, name} = dataset;
      const isAreaFlag = dataset.isarea;
      let {selectedPath, regionObj} = this.data;
      if (code === 'All') {
        selectedPath.country = {code, level: '0', name};
        selectedPath.province = null;
        selectedPath.city = null;
        selectedPath.area = null;
        regionObj.curCityList = [];
        regionObj.curAreaList = [];
      } else if (level === '1') {
        selectedPath.country = null;
        selectedPath.province = {code, level, name};
        selectedPath.city = null;
        selectedPath.area = null;
        await this.loadCityData(code);
        regionObj.curAreaList = [];
      } else if (level === '2') {
        selectedPath.city = {code, level, name};
        selectedPath.area = null;
        await this.loadAreaData(code);
      } else if (level === '3' && isAreaFlag) {
        // 直辖市的区（特殊处理）
        selectedPath.city = {code, level, name, isarea: true};
        selectedPath.area = null;
        regionObj.curAreaList = [];
      } else if (level === '3') {
        // 普通的区
        selectedPath.area = {code, level, name};
      }

      this.setData({
        selectedPath,
        finalSelection: this.getFinalSelection(selectedPath),
        regionObj
      });
      this.updateListSelections();
    },

    async getRegion(code = '', level) {
      const {regionObj, cityObj, areaObj, dataType} = this.data;
      let temp = !level
        ? 'provinceList'
        : level === '1'
        ? 'curCityList'
        : 'curAreaList';

      const dataList = await getData({code, dataType});
      let processedList = dataList.map(item => {
        // 如果当前获取的是直辖市的下级数据，添加一个区的标记
        if (level === '1' && item.level === '3') {
          item.isarea = true;
        }
        return {
          ...item,
          selected: false,
          active: false
        };
      });

      if (!level) {
        processedList.unshift({
          code: 'All',
          name: '全国',
          level: '0',
          selected: true,
          active: true
        });

        this.setData({
          selectedPath: {
            country: {code: 'All', level: '0', name: '全国'},
            province: null,
            city: null,
            area: null
          },
          finalSelection: {code: 'All', level: '0', name: '全国'},
          readyToShow: true // 初始化完成，可以显示
        });
      }

      regionObj[temp] = processedList;

      if (level === '1') cityObj[code] = dataList;
      if (level === '2') areaObj[code] = dataList;

      this.setData({regionObj, cityObj, areaObj});
    },

    reset() {
      this.setData({
        selectedPath: {
          country: {code: 'All', level: '0', name: '全国'},
          province: null,
          city: null,
          area: null
        },
        finalSelection: {code: 'All', level: '0', name: '全国'},
        regionObj: {
          provinceList: this.data.regionObj.provinceList.map(item => ({
            ...item,
            selected: item.code === 'All',
            active: item.code === 'All'
          })),
          curCityList: [],
          curAreaList: []
        }
      });
    },

    close() {
      this.reset();
      this.triggerEvent('close');
    },

    submit() {
      const {finalSelection, selectedPath} = this.data;
      if (!finalSelection) {
        wx.showToast({title: '请选择地区', icon: 'none'});
        return;
      }

      // 为最终选择添加 parent 信息
      const selectionWithParent = {...finalSelection};

      if (selectedPath.area) {
        // 如果选择的是区，parent 是市
        selectionWithParent.parent = selectedPath.city?.code;
      } else if (selectedPath.city) {
        // 如果选择的是市，parent 是省
        selectionWithParent.parent = selectedPath.province?.code;
      } else if (selectedPath.province) {
        // 如果选择的是省，parent 是全国
        selectionWithParent.parent = 'All';
      } else if (selectedPath.country) {
        // 如果选择的是全国，没有 parent
        selectionWithParent.parent = null;
      }

      this.triggerEvent('submit', {
        selection: selectionWithParent,
        path: selectedPath
      });
    }
  }
});
