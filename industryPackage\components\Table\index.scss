.table-component {
  width: 100%;
}

.node-list {
  width: 100%;

  // 固定第一列的表格容器
  .table-container.fixed-first-column {
    width: 100%;
    overflow: hidden;

    .table-wrapper {
      display: flex;
      width: 100%;
      height: 100%;
    }

    // 固定的第一列
    .fixed-column {
      width: 272rpx;
      min-width: 272rpx;
      z-index: 2;

      // 第一个单元格（表头）添加顶部边框
      .table-cell:first-child {
        border-top: 2rpx solid #eeeeee;
      }

      .table-cell {
        width: 272rpx;
        height: 74rpx;
        background: #f7f7f7;
        border-right: none;
        border-top: none;
        border-left: 2rpx solid #eeeeee;
        border-bottom: 2rpx solid #eeeeee;
        border-radius: 0;
        font-weight: 400;
        font-size: 24rpx;
        color: #525665;
        padding-left: 20rpx;
        padding-right: 20rpx;
        display: flex;
        align-items: center;
        justify-content: flex-start;
        text-align: left;
        box-sizing: border-box;
      }

      .fixed-body-cell {
        &.merged-cell {
          display: none;
        }

        // 合并单元格的垂直居中
        &.merged-main-cell {
          display: flex;
          align-items: center;
          justify-content: flex-start;
          text-align: left;
        }
      }
    }

    // 可滚动的其他列
    .scrollable-columns {
      flex: 1;
      overflow-x: auto;
      background: #ffffff;

      .scrollable-wrapper {
        width: max-content;
        min-width: 100%;
      }

      .table-header {
        width: 100%;
      }

      .table-body {
        width: 100%;
      }

      .table-row {
        display: flex;
        width: max-content;
        min-width: 100%;

        &.other:first-child .table-cell {
          border-top: 2rpx solid #eeeeee;
        }
      }

      .table-cell {
        // flex和width由JS动态设置，支持两种模式：
        // 1. 列数少时：flex: 1 撑满
        // 2. 列数多时：固定宽度
        height: 74rpx;
        background: #ffffff;
        border-left: none;
        border-right: 2rpx solid #eeeeee;
        border-top: none;
        border-bottom: 2rpx solid #eeeeee;
        border-radius: 0;
        padding: 0 20rpx !important; // 强制保持左右间距
        display: flex;
        align-items: center;
        justify-content: center;
        text-align: center;
        box-sizing: border-box;
        white-space: nowrap; // 防止文字换行
      }

      .table-header-cell {
        // 第一行（表头）样式
        font-weight: 600;
        font-size: 26rpx;
        color: #20263a;
      }

      .table-body-cell {
        // 非第一行样式
        font-weight: 400;
        font-size: 24rpx;
        color: #20263a;
      }
    }
  }

  // 通用单元格样式
  .table-cell {
    font-family:
      PingFang SC,
      sans-serif;
    word-break: break-word;
    white-space: normal;
    overflow-wrap: break-word;
    overflow: hidden;
    box-sizing: border-box;
  }
}

// 加载状态
.loading-div {
  height: 500rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  font-size: 24rpx;
  color: #74798c;
  border: 2rpx solid #eee;

  .loading-spinner {
    width: 72rpx;
    height: 72rpx;
    border: 4rpx solid #f3f3f3;
    border-top: 4rpx solid #3e7bfa;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  .loading-text {
    margin-top: 32rpx;
    font-size: 24rpx;
    color: #74798c;
  }
}

// 空状态
.empty-div {
  height: 500rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  font-size: 24rpx;
  color: #74798c;
  border: 2rpx solid #eee;

  .empty-icon {
    width: 160rpx;
    height: 160rpx;
    margin-bottom: 32rpx;
  }

  .empty-text {
    font-size: 24rpx;
    color: #74798c;
  }
}

// 加载动画
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

// 横向滚动优化
.scrollable {
  .table-wrapper {
    width: max-content;
    min-width: 100%;
  }

  .table-row {
    width: max-content;
    min-width: 100%;
  }

  .table-cell {
    flex: none;
    min-width: 200rpx;
  }
}

// 第一列固定滚动样式
.fixed-first-column {
  position: relative;

  .table-wrapper {
    display: flex;
    width: 100%;
  }

  // 固定的第一列
  .fixed-column {
    position: sticky;
    left: 0;
    z-index: 2;
    background: #ffffff;
    box-shadow: 2rpx 0 8rpx rgba(0, 0, 0, 0.1);

    .table-row {
      display: flex;
      flex-direction: column;
    }

    .table-cell {
      border-right: 2rpx solid #eeeeee;
    }

    .table-header-cell {
      background: #f7f7f7;
      z-index: 3;
    }
  }

  // 可滚动的其他列
  .scrollable-columns {
    flex: 1;
    overflow-x: auto;

    .table-wrapper {
      width: max-content;
      min-width: 100%;
    }

    .table-row {
      display: flex;
      width: max-content;
    }

    .table-cell {
      // 样式由JS动态设置，支持flex:1或固定宽度
      white-space: nowrap;
      padding: 0 20rpx !important;
    }
  }
}
