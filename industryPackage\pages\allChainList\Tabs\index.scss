// Tab 组件样式
.tabs-container {
  width: 100%;
  background: #ffffff;
  box-sizing: border-box;
  height: 100rpx;
  border-bottom: 1rpx solid #eee;
  border-top: 20rpx solid #f2f2f2;
  padding: 0 24rpx;

  .tabs-wrapper {
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-radius: 12rpx;
    height: 100%;

    .tab-item {
      position: relative;
      width: 202rpx;
      text-align: center;
      border-radius: 8rpx;

      transition: all 0.3s ease;
      height: 56rpx;
      display: flex;
      align-items: center;
      justify-content: center;

      // 默认样式
      font-weight: 400;
      font-size: 28rpx;
      color: #74798c;
      .vip {
        position: absolute;
        width: 58rpx;
        height: 40rpx;
        right: -12rpx;
        top: -22rpx;
      }

      // 选中样式
      &.active {
        background: linear-gradient(90deg, #e72410 0%, #f17b6f 100%);
        border-radius: 8rpx;
        font-weight: 600;
        font-size: 28rpx;
        color: #ffffff;
      }
    }
  }
}

// 针对产业链图谱样式
.chainMap {
  .tab-item {
    &.active {
      background: linear-gradient(90deg, #c2a5ff 0%, #8441ff 100%) !important;
    }
  }
}
