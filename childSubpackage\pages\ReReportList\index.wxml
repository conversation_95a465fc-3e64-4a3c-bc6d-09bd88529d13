<import src="/template/null/null"></import>
<view class="pages">
	<!-- input -->
	<view class="searchs">
		<view class="s-input" bindtap="navigateToSearch">
			<view class="s-input-img">
				<image src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/search.png" mode="aspectFit"></image>
			</view>
			<view class="s-input-item">
				<input class="s-input-item-i" type="text" placeholder="请输入机构/研报关键字" placeholder-class="placeholder" disabled="{{true}}" />
			</view>
		</view>
	</view>

	<!-- 列表页面 -->
	<view class="list-container">
		<view class="company_num">
		</view>

		<!-- 使用refresh-scroll组件实现下拉刷新上拉加载 -->
		<view class="card-box" style="height: {{listScrollHeight}}px;">
			<refresh-scroll id="listRefreshScroll" container-height="{{listScrollHeight}}" request-url="{{RequestUrlFn}}" requestParams="{{listRequestParams}}" empty-text="暂无研报数据" empty-tip="" bind:datachange="onListDataChange" custom-wrapper-class="custom-wrapper-class" requestType="pageIndex">
				<view slot="content" class="list_wrp">
					<ReportCard report-list="{{reportList}}" bindreportclick="onReportClick" />
				</view>
			</refresh-scroll>
		</view>
	</view>
</view>