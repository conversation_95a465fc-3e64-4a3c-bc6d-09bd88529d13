import {home, chain} from '../../../../service/api';
import {getHeight} from '../../../../utils/height';
import {getShareUrl, handleShareUrl} from '../../../../utils/mixin/pageShare';
const app = getApp();
Page({
  /**
   * 页面的初始数据
   */
  data: {
    allChain: [], // 所有产业链
    filterList: [], // 头部筛选列表
    chainList: [], // 产业链列表
    visible: false,
    startDistance: 0,
    selectChain: {},
    recentlyViewedList: [], // 最近查看列表
    isLogin: app.isLogin() // 是否登录
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: async function (options) {
    handleShareUrl();
    this.scrollH();
    app.showLoading('加载中');
    let allChain = await chain.chainAll();
    allChain = this.filterAllChain(allChain);
    const filterList = [
      {
        chain_name: '全部产业'
      }
    ].concat(allChain);
    // console.log("产业链", allChain);
    this.setData({
      allChain,
      filterList,
      selectChain: filterList[0],
      chainList: JSON.parse(JSON.stringify(allChain))
    });
    wx.hideLoading();
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {},

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    this.setData(
      {
        isLogin: app.isLogin()
      },
      () => {
        const {isLogin} = this.data;
        if (!isLogin) return; // 未登录不调用浏览历史接口
        home.getBevHis('CHAIN').then(res => {
          this.setData({
            recentViewedList: res.map(item => {
              item.chain_code = item.enterprise_id;
              item.chain_name = item.enterprise_name;
              return item;
            })
          });
        });
      }
    );
  },
  scrollH() {
    getHeight(this, ['.navigationBar', '.h_head'], data => {
      const {screeHeight, res} = data;
      let h1 = res[0]?.height || 0;
      let h2 = res[1]?.height || 0;
      let scrollHeight = screeHeight - h1 - h2;
      this.setData({
        scrollHeight,
        startDistance: h1 + h2
      });
    });
  },
  filterAllChain(res = []) {
    let list = [];
    if (res.length <= 0) return [];
    res.forEach(item => {
      let obj = {
        chain_name: '',
        chains: [],
        hidden: false
      };
      let {name, children, chain_code} = item;
      obj['chain_name'] = name;
      obj['chain_code'] = chain_code;
      children.forEach(itm => {
        let {chain_code, name: chain_name} = itm;
        obj['chains'].push({
          chain_code,
          chain_name
        });
      });
      const len = obj['chains'].length;
      obj.height = (parseInt(len / 2) + (len % 2)) * 70 + 22 + 'px';
      list.push(obj);
    });
    return list;
  },
  // 头部筛选箭头切换
  switchFilterArrow() {
    const {visible} = this.data;
    this.setData({
      visible: !visible
    });
  },
  // 数据筛选
  handleFilter({
    currentTarget: {
      dataset: {item: selectChain}
    }
  }) {
    const {allChain} = this.data;
    this.setData({
      selectChain,
      visible: false,
      chainList: allChain.filter(chain => {
        return selectChain.chain_code
          ? chain.chain_code === selectChain.chain_code
          : true;
      })
    });
  },
  close(e) {
    this.setData({
      visible: false
    });
  },
  // 箭头切换显示隐藏
  handleSwitch({
    currentTarget: {
      dataset: {
        item: {hidden},
        index
      }
    }
  }) {
    this.setData({
      [`chainList[${index}].hidden`]: !hidden
    });
  },
  handleChain({
    currentTarget: {
      dataset: {item}
    }
  }) {
    // const url = `/companyPackage/pages/industryChain/newChainMap/newChainMap?chainTypeName=${item.chain_name}&industry_code=${item.chain_code}`;
    const {isLogin} = this.data;
    if (isLogin) {
      home.addBevHis({
        enterprise_name: item.chain_name,
        enterprise_id: item.chain_code,
        behavior_history_mode: 'INDEX_PAGE',
        enterprise_log: '-',
        model_type: 'CHAIN'
      }); //新增浏览历史
    }
    const url = `/industryPackage/pages/IndustryListMasonry/index?chain_name=${item.chain_name}&chain_code=${item.chain_code}`;
    app.route(this, url);
  },
  onShareAppMessage: function () {
    return {
      title: '邀请你查看200+产业链信息', //自定义转发标题
      path: getShareUrl(
        '/companyPackage/pages/industryChain/chainIndex/chainIndex'
      ), //分享页面路径
      imageUrl:
        'https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/aa4.png' //图片后面换
    };
  }
});
