<!-- 下拉tag示的多选 -->
<HalfScreenPop showFooter="{{false}}" showCloseBtn="{{false}}" visible="{{visible}}" position="{{position}}" bindsubmit="submit" bindclose="close" startDistance="{{startDistance}}" disableAnimation="{{true}}" _maskClosable="{{false}}" zIndex="{{10}}" footHeigh="110rpx" cancelBtnText="取消">
  <view slot="customContent" class="area" style="margin-top:{{top}}px;">
    <view class="region-wrap">
      <scroll-view scroll-y style="max-height: 720rpx">
        <view wx:for="{{list}}" wx:key="idx">
          <view class="title" wx:if="{{item.showTitle}}">
            {{ item.title }}
            <view class="tip">
              <slot name="{{'tip' + index}}"></slot>
            </view>
          </view>
          <view class="list" wx:if="{{item.allList.length > 0}}">
            <view class="item {{items.status && 'active' || 'none'}}" wx:for="{{item.allList}}" wx:for-index="index" wx:for-item="items" catchtap="checkItem" data-item="{{items}}" data-needvip="{{item.needVip}}" data-parent='{{item.id}}' data-single='{{item.singleSlect}}' data-slot='{{item.slot}}'>
              <text>{{items.text || item.name}}</text>
            </view>
          </view>
          <!-- 自定义筛选项目 -->
          <view class="customFilterContent">
            <slot name="{{ item.slot}}"></slot>
          </view>
        </view>
      </scroll-view>
      <view class="footer-wrap">
        <view bindtap="close" class="btn">取消</view>
        <view bindtap="submit" class="btn submit">确定</view>
      </view>
    </view>
  </view>
</HalfScreenPop>