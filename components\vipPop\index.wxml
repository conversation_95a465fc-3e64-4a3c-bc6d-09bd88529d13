<view
  class="fadeIn weui-mask"
  catchtap="_disabledPenetrate"
  catchtouchmove="onMaskTouchMove"
  style="top:{{top}}px !important; z-index: {{computedZIndex}} !important;"
  wx:if="{{visible}}"
>
  <!-- 内容部分 -->
  <view class="cont {{isInPopup ? 'popup-mode' : ''}}">
    <image
      src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/cyt_v2/image/vip/vVip.png"
      class="vVip"
    ></image>
    <image
      src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/cyt_v2/image/vip/vclose.png"
      class="vClose"
      catchtap="close"
    ></image>
    <!-- 头部 -->
    <view class="head">
      <image
        src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/cyt_v2/image/vip/vbg.png"
        class="vbg"
      ></image>
      <view class="one"> 成为VIP 开启更多高级功能 </view>
      <view class="two"> 享VIP专属特权 </view>
    </view>
    <!-- 按钮部分 ab-->
    <view class="paybtn" bindtap="order">
      <view> 立即支付 </view>
      <view class="second-view"> 支付后免费邮寄发票 </view>
    </view>
    <!-- 滚动的部分 -->
    <scroll-view
      class="scroll"
      scroll-y="true"
      bindtouchmove="onScrollTouchMove"
    >
      <!-- VIP套餐横向滚动区域 -->
      <view class="vipWrap">
        <scroll-view
          class="henWrap {{isInPopup ? 'popup-mode' : ''}}"
          scroll-x="true"
          show-scrollbar="{{false}}"
          enhanced="{{true}}"
          bounces="{{false}}"
        >
          <view class="horizontal-container">
            <block wx:for="{{person_vip_data}}" wx:for-item="i" wx:key="index">
              <view
                class="item {{i.active && 'lactive'}}"
                bindtap="choose"
                data-item="{{i}}"
                data-type="person_vip_data"
              >
                <image
                  src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/pay/p-aico.png"
                  class="gg"
                />
                <view class="one">
                  {{ i.tit }}
                </view>
                <view class="two">
                  ￥<text>{{ i.price }}</text>
                </view>
                <view class="three">
                  {{ i.comment }}
                </view>
              </view>
            </block>
          </view>
        </scroll-view>
      </view>
      <!-- 支付方式 -->
      <view class="paytype">
        <view class="tit"> 支付方式 </view>
        <view class="paycont">
          <block wx:for="{{payList.list}}" wx:key="index">
            <view
              class="item {{payList.curIdx==index&&'pactive'}}"
              bindtap="payChange"
              data-index="{{index}}"
            >
              <view class="item-l">
                <image src="{{item.icon}}"></image>
                <view>
                  {{item.name}}
                </view>
              </view>
              <view class="item-r">
                <image
                  src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/pay/circle.png"
                  wx:if="{{payList.curIdx!=index}}"
                />
                <image
                  src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/pay/circle-a.png"
                  wx:else
                />
              </view>
            </view>
          </block>
        </view>
      </view>
      <!-- vip专属特权 -->
      <view class="special">
        <view class="tit">
          <image
            src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/pay/p-tl.png"
            class="img1"
          />
          <view> VIP专属特权 </view>
          <image
            src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/pay/p-tr.png"
            class="img2"
          />
        </view>
        <view class="slist">
          <block wx:for="{{staticstateList}}" wx:key="index">
            <view class="itms">
              <image
                src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/pay/{{item.icon}}.png"
              />
              <view>
                {{item.title}}
              </view>
              <view>
                {{item.subTit}}
              </view>
            </view>
          </block>
        </view>
      </view>
    </scroll-view>
  </view>
</view>
