// 产业链图谱组件
Component({
  properties: {
    // 标题文本
    title: {
      type: String,
      value: '产业链图谱'
    },
    type: {
      type: String,
      value: '1'
    },
    // 卡片列表数据 具体字段后续要改
    cardList: {
      type: Array,
      value: [
        {
          id: '1',
          title: '生物医药',
          companyCount: 1453,
          categories: 12,
          fields: 12,
          companies: '20万'
        }
      ]
    }
  },

  data: {},

  methods: {
    // 点击更多按钮
    onMoreClick() {
      this.triggerEvent('moreClick', {
        type: 'industry_map'
      });
    },

    // 点击卡片
    onCardClick(e) {
      const {item, index} = e.currentTarget.dataset;
      this.triggerEvent('callback', {
        item,
        index
      });
    }
  }
});
