/**
 * 简化的 Behavior 基类
 * 创建可配置的基础 Behavior，支持不同变体
 */

const {
  getSearchConfig,
  CONFIG_TYPES
} = require('../config/constants');
const {
  clone
} = require('../../../utils/util');

const app = getApp();

/**
 * 创建可配置的 Hunt Behavior
 * @param {Object} config - 配置选项
 * @returns {Object} Behavior 对象
 */
function createConfigurableBehavior(config) {
  config = config || {};

  const variant = config.variant || CONFIG_TYPES.FULL;
  const enableVipCheck = config.enableVipCheck !== undefined ? config.enableVipCheck : true;
  const customMethods = config.customMethods || {};
  const customData = config.customData || {};

  // 获取对应变体的配置
  const searchConfig = getSearchConfig(variant);

  // 构建数据对象
  const behaviorData = {
    // 基础数据
    isIphoneX: app.globalData.isIphoneX,
    login: app.globalData.login,
    params: clone(searchConfig.defaultParams),

    // 输入框状态
    minCapital: '',
    maxCapital: '',
    dateType: '',
    dateActive: false,
    date: '',
    capitalActive: false,
    minDate: '',
    maxDate: '',
    socialActive: false,
    socialminPeson: '',
    socialmaxPeson: '',

    // 弹窗状态
    saveSearVal: '',
    searPop: false,
    saveSearPop: false,
    regionPop: false,
    datePop: false,
    eleseicPop: false,
    enttypePop: false,
    districtPop: false,
    chainCodePop: false,

    // 模板数据
    idName: '',
    focus: false,
    searchList: [],
    templateAry: [],
    renderAry: [],

    // 配置数据
    itemList: clone(searchConfig.searchTermList),
    leftList: clone(searchConfig.searchLeftLists)
  };

  // 合并自定义数据
  Object.keys(customData).forEach(function (key) {
    behaviorData[key] = customData[key];
  });

  // 构建方法对象
  const behaviorMethods = {
    /**
     * 清空搜索表单
     */
    clearSear: function () {
      const config = getSearchConfig(this.data.variant);
      const self = this;
      const list = config.searchTermList.map(function (item) {
        if (item.isOpenIcon) {
          const matchedItem = self.data.itemList.filter(function (i) {
            return item.type === i.type;
          })[0];
          item.isOpen = matchedItem ? matchedItem.isOpen : false;
        }
        return item;
      });

      this.setData({
        itemList: clone(list),
        params: clone(config.defaultParams),
        minCapital: '',
        maxCapital: '',
        minDate: '',
        maxDate: '',
        date: '',
        capitalActive: false,
        socialActive: false,
        socialminPeson: '',
        socialmaxPeson: '',
        dateActive: false,
        dateType: ''
      });
    },

    /**
     * 设置回填数据
     * @param {Object} tempObj - 回填数据对象
     */
    setBackfillData: function (tempObj) {
      tempObj = tempObj || {};
      this.clearSear();

      const data = this.data;
      let params = data.params;

      // 合并参数
      params = Object.assign(params, tempObj);

      this.setData({
        params: params
      });
    },

    /**
     * 验证搜索参数
     * @returns {boolean} 验证结果
     */
    validateSearchParams: function () {
      // 简化的验证逻辑
      return true;
    },

    /**
     * 处理搜索结果
     * @param {Object} params - 搜索参数
     */
    result: function (params) {
      // 触发搜索结果事件
      this.triggerEvent('search', {
        params: params || this.data.params
      });
    },

    /**
     * 选择标签
     * @param {Object} event - 事件对象
     */
    selectTag: function (event) {
      const dataset = event.currentTarget.dataset;
      const id = dataset.id;
      const type = dataset.type;

      const data = this.data;
      let itemList = data.itemList;
      let params = data.params;

      // 简化的标签选择逻辑
      itemList = itemList.map(function (listItem) {
        if (listItem.type === type && listItem.list) {
          listItem.list = listItem.list.map(function (tag) {
            if (tag.id === id) {
              tag.active = !tag.active;

              // 更新参数
              if (!params[type]) {
                params[type] = [];
              }

              if (tag.active) {
                if (!params[type].includes(id)) {
                  params[type].push(id);
                }
              } else {
                const index = params[type].indexOf(id);
                if (index > -1) {
                  params[type].splice(index, 1);
                }
              }
            }
            return tag;
          });
        }
        return listItem;
      });

      this.setData({
        itemList: itemList,
        params: params
      });
    },

    /**
     * 输入框获取焦点
     */
    inputFocus: function (e) {
      // 简化的焦点处理
    },

    /**
     * 输入框值改变
     */
    inputChange: function (e) {
      const type = e.currentTarget.dataset.type;
      const value = e.detail.value;

      this.setData({
        ['params.' + type]: value
      });

      this.result();
    },

    /**
     * 提交弹窗选择
     */
    submitSub: function (e) {
      const detail = e.detail;
      const obj = detail.checkedList;
      const mark = detail.mark;

      this.setData({
        ['params.' + mark]: obj,
        regionPop: false,
        eleseicPop: false,
        enttypePop: false,
        districtPop: false
      });

      this.result();
    }
  };

  // 合并自定义方法
  Object.keys(customMethods).forEach(function (key) {
    behaviorMethods[key] = customMethods[key];
  });

  return Behavior({
    properties: {
      wrapHeight: {
        type: String,
        value: ''
      },
      isPage: {
        type: Boolean,
        value: false
      },
      variant: {
        type: String,
        value: variant
      },
      filterAry: {
        type: Array,
        value: []
      }
    },

    data: behaviorData,

    observers: {
      params: function (val) {
        // console.log('params changed:', val);
        this.result(val);
      }
    },

    methods: behaviorMethods
  });
}

// 导出模块
module.exports = createConfigurableBehavior;