// 时间戳转日期
function transformDate(num) {
  let number = new Date(num);
  let year = number.getFullYear();
  let month = number.getMonth() + 1;
  let day = number.getDate();
  let date = year + '-' + month + '-' + day;
  return date;
}
// 计算日期
function calcDate(start, end) {
  let curDate = new Date().getTime(); // 当前时间的时间戳
  let oneYear = 365 * 24 * 60 * 60 * 1000; // 一年时间的时间戳
  let startDate = curDate - end * oneYear; // 开始时间
  let endDate = curDate - start * oneYear; // 结束时间
  let dateCombination =
    (end ? transformDate(startDate) : '') + '$' + transformDate(endDate);
  return dateCombination;
}
// 注册时间
export const REG_TIME_ENUM = [{
    code: 1,
    id: calcDate(0, 1),
    text: '1年内'
  },
  {
    code: 2,
    id: calcDate(1, 3),
    text: '1-3年'
  },
  {
    code: 3,
    id: calcDate(3, 5),
    text: '3-5年'
  },
  {
    code: 4,
    id: calcDate(5, 10),
    text: '5-10年'
  },
  {
    code: 5,
    id: calcDate(10, 0),
    text: '10年以上'
  }
];
// 企业规模
export const ent_size_ENUM = [{
    id: '0$500',
    text: '微型'
  },
  {
    id: '500$1000',
    text: '小微型'
  },
  {
    id: '1000$2000',
    text: '中小型'
  },
  {
    id: '2000$10000',
    text: '中型'
  },
  {
    id: '10000$30000',
    text: '大型'
  },
  {
    id: '30000$',
    text: '特大型'
  }
];
// 效益评估
export const benefits_assess_ENUM = [{
    id: '$50',
    text: '小型A'
  },
  {
    id: '50$100',
    text: '小型B'
  },
  {
    id: '100$200',
    text: '中型A'
  },
  {
    id: '200$300',
    text: '中型B'
  },
  {
    id: '300$500',
    text: '中型C'
  },
  {
    id: '500$1000',
    text: '中型D'
  },
  {
    id: '1000$2000',
    text: '大型A'
  },
  {
    id: '2000$3000',
    text: '大型B'
  },
  {
    id: '3000$5000',
    text: '大型C'
  },
  {
    id: '5000$10000',
    text: '大型D'
  },
  {
    id: '10000$',
    text: '特大型'
  }
]