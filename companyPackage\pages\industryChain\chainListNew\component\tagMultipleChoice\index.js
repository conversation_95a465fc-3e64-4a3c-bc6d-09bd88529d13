// var financingBehavior = require('../../behaviorConfig');
import {
  hasPrivile
} from '../../../../../../utils/route';
const app = getApp();
Component({
  // behaviors: [financingBehavior],
  options: {
    multipleSlots: true // 在组件定义时的选项中启用多个 slot 支持
  },
  /**
   * 组件的属性列表
   */
  properties: {
    top: {
      type: Number,
      value: 0
    },
    visible: {
      type: Boolean,
      value: false,
      observer(val) {
        if (val) {
          this.init()
        }
      }
    },
    dataList: {
      type: Array,
      value: [],
      observer(val) {
        // console.log('val', val);
        let list = []
        if (val.length > 0) {
          val.forEach((item) => {
            list.push({
              id: item.id,
              title: item.title,
              showTitle: item.showTitle,
              allList: item.list,
              oldData: item.cur,
              singleSlect: item.singleSlect,
              slot: item.slot,
              newList: [],
              needVip: item.needVip || false
            })
          })
          this.setData({
            list
          });
          return
        }
      }
    },
  },

  /**
   * 组件的初始数据
   */
  data: {
    newList: [],
    list: []
  },
  lifetimes: {
    attached() {}
  },
  methods: {
    init() {
      let {
        list
      } = this.data;
      list.map((data) => {
        if (data.oldData.length) { //説明有回填数据
          data.allList = data.allList.map(item => {
            item.status = ""
            data.oldData.forEach(i => {
              if (item.id == i) item.status = 'checked'
            })
            return item
          })
        } else {
          data.allList.map(item => item.status = item.id === 'all' ? 'checked' : '')
        }
        return data
      });
      this.setData({
        list
      }, () => {
        console.log('list', list);
      });
    },
    // 选择item 
    async checkItem(e) {
      let {
        list
      } = this.data
      let {
        item: {
          id
        },
        parent,
        single,
        slot,
        needvip
      } = e.currentTarget.dataset
      const userType = await hasPrivile({
        packageType: true
      });
      const commonUser = ['游客', '普通VIP'];
      if (needvip && commonUser.includes(userType)) {
        const tempObj = {
          '游客': () => app.route(this, '/pages/login/login'),
          '普通VIP': () => this.triggerEvent('setVipVisible', true)
        }
        tempObj[userType]?.();
        return;
      };
      if (slot) {
        // console.log('slot', slot);
        this.triggerEvent(`handle${slot}`, e)
      }
      list.map((data) => {
        if (data.id === parent) {
          if (id == 'all') { //说明点击到不限了 
            data.allList.map(item => {
              item.status = item.id != 'all' ? '' : 'checked'
              return item
            })
          } else {
            if (single) {
              data.allList.map(item => {
                if (item.id != id) {
                  item.status = ''
                } else {
                  item.status = item.status == 'checked' ? '' : 'checked'
                }
                return item
              })
            } else {
              data.allList.map(item => {
                if (item.id == 'all') item.status = '';
                if (item.id != id) return item;
                item.status = item.status == 'checked' ? '' : 'checked'
                return item
              })
            }
            if (data.allList.every(item => item.status === '') && !data.slot) {
              data.allList.length && (data.allList[0].status = 'checked');
            }
            let newList = data.allList.filter((item) => item.status == 'checked')
            data.newList = newList;
          }
        }
        return data
      });
      this.setData({
        list
      });
    },
    submit() { //点击确定--传一个数组出去 回填的时候也是根据这个数组来 
      let {
        list
      } = this.data;
      list.map((data) => {
        data.codeList = data.allList.filter((item) => item.status).map(item => item.id)
      });
      this.triggerEvent('submit', list)
    },
    close() {
      this.triggerEvent('close')
    }
  }
})