/* VIP弹窗样式 */

/* 遮罩层 */
.vip-popup-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  align-items: flex-end;
  justify-content: center;
  transition: opacity 0.3s ease;
}

.vip-popup-mask.show {
  opacity: 1;
}

.vip-popup-mask.hide {
  opacity: 0;
}

/* 弹窗容器 */
.vip-popup-container {
  width: 100%;
  position: relative;
  transform: translateY(100%);
  transition: transform 0.3s ease;
  overflow: hidden;
  .vip-image1 {
    height: 356rpx;
    margin-bottom: -98rpx;
  }
}

.vip-popup-container.slide-up {
  transform: translateY(0);
}

.vip-popup-container.slide-down {
  transform: translateY(100%);
}

/* 图片容器 */
.images-container {
  width: 100%;
  display: flex;
  flex-direction: column;
  background: #fff;
  align-items: center;
  z-index: 1;
  position: relative;
  border-radius: 24rpx 24rpx 0 0;
  .scroll_view {
    padding: 40rpx 24rpx 0;
    height: 830rpx;
    // 隐藏滚动条
    // ::-webkit-scrollbar {
    //   width: 0;
    //   height: 0;
    //   color: transparent;
    //   display: none;
    // }
    .vip_image2 {
      width: 703rpx;
    }
  }
}
.vip_mage3 {
  width: 452rpx;
  height: 36rpx;
  margin-top: 64rpx;
}
.vip_button {
  padding-bottom: env(safe-area-inset-bottom);
}

/* 防止触摸穿透 */
.vip-popup-container {
  touch-action: manipulation;
}

/* 动画优化 */
.vip-popup-mask,
.vip-popup-container {
  will-change: transform, opacity;
}

/* 图片加载状态 */
.vip-image {
  background: #f5f5f5;
  min-height: 200rpx;
}
.zhanwei {
  position: absolute;
  height: 110rpx;
  width: 100%;
  z-index: 10;
  top: 0;
  opacity: 0;
}
