Page({
  data: {
    purchased: true, //默认购买
    externalFilterState: {}, //企业名单回显
    showMapMode: true
  },

  onLoad: function (options) {
    let {
      chain_name = '',
      chain_code = '',
      isIndustryMap,
      purchased,
      category,
      title,
      showMapMode,
      region_code = '',
      region_name = '',
      filter_code = '',
      filter_name = ''
    } = options;
    region_name = region_name ? decodeURIComponent(region_name) : '';
    filter_name = filter_name ? decodeURIComponent(filter_name) : '';
    chain_name = chain_name ? decodeURIComponent(chain_name) : '产业类型';
    title = title ? decodeURIComponent(title) : '企业名单';
    purchased = purchased !== 'false'; // 外面只有一个地方传进来 false字符串就是没有购买的情况

    showMapMode = showMapMode === 'false' ? false : true; //是否展示地图模式按钮 默认展示
    const tempExternalFilterState = {};
    if (region_code) {
      tempExternalFilterState['regionData'] = {
        code: region_code,
        name: region_name
      };
    }

    // 经典字段不一样
    if (category === 'classic') {
      tempExternalFilterState['classic_industry_code_list'] = {
        code: chain_code,
        name: chain_name
      };
    } else if (category === 'hot') {
      // 热点和产业链图谱 -- 产业链图谱chain_code是必有得
      tempExternalFilterState['industrial_list'] = {
        code: chain_code,
        name: chain_name
      };
    } else {
      // 产业链图谱 category 为chainMap 如果一开始进来没有选择就用filter_code,filter_name
      tempExternalFilterState['industrial_list'] = {
        code: chain_code || filter_code,
        name: chain_code ? chain_name : filter_name
      };
    }
    console.log(4444, filter_code, filter_name);

    if (title === '上市企业') {
      tempExternalFilterState['filterParams'] = {
        listing_status: ['A', 'B', 'NTB', 'HK', 'STAR', 'USA']
      };
    } else if (title === '高新技术企业') {
      tempExternalFilterState['filterParams'] = {
        technology: ['HN']
      };
    } else if (title === '专精特新') {
      // 这个后续数据结构要重构
      tempExternalFilterState['filterParams'] = {
        psn: ['SME', 'CMI', 'SG', 'IME']
      };
    } else if (title === '科技型企业') {
      tempExternalFilterState['filterParams'] = {
        technology: ['HN', 'MST', 'G', 'U', 'GT']
      };
    } else if (title === '小巨人') {
      tempExternalFilterState['filterParams'] = {
        technology: ['GT']
      };
    } else if (title === '单项冠军') {
      tempExternalFilterState['filterParams'] = {
        psn: ['CMI']
      };
    } else if (title === '专精特新中小企业') {
      tempExternalFilterState['filterParams'] = {
        psn: ['SME']
      };
    } else if (title === '创新型中小企业') {
      tempExternalFilterState['filterParams'] = {
        psn: ['IME']
      };
    }
    console.log(tempExternalFilterState);

    this.setData({
      isIndustryMap: isIndustryMap === 'true',
      purchased,
      externalFilterState: tempExternalFilterState,
      category,
      showMapMode,
      filter_code,
      filter_name
    });
    // 处理更多筛选条件 排除不要的
    // --- 已有上市企业

    // 动态设置导航名
    wx.setNavigationBarTitle({
      title
    });
  }
});
