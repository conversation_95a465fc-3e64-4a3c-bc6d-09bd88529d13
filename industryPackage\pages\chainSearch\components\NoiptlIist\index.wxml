<view class="wrap {{type == 'chainMap' ? 'hotChain' : ''}}">
  <!-- 标题区域 -->
  <view class="header">
    <!-- 左侧标题 -->
    <view class="header-left">
      <image
        src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/cyt_v2/image/industry/i_hot.png"
      ></image>
      <text class="title">{{title}}</text>
      <text class="subtitle">{{subtitle}}</text>
    </view>
    <!-- 右侧箭头 -->
    <image
      class="arrow"
      wx:if="{{type !== 'chainMap'}}"
      src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/cyt_v2/image/industry/i_vip.png"
    ></image>
    <image
      class="arrow"
      wx:if="{{type == 'chainMap'}}"
      src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/cyt_v2/image/industry/i_zhuanshi.png"
    ></image>
  </view>

  <!-- 列表区域 -->
  <view class="list" wx:if="{{(industryList && industryList.length)}}">
    <view
      class="item"
      wx:for="{{industryList.length ? industryList : defaultList}}"
      wx:key="{{keyField}}"
      data-item="{{item}}"
      data-index="{{index}}"
      bindtap="onItemClick"
    >
      <view class="left">
        <text class="rank">{{index + 1}}</text>
        <text class="name">{{item.name }}</text>
      </view>
      <view class="right">
        <text class="count" wx:if="{{type !== 'chainMap'}}"
          >{{item.enterprise_count}} 家企业</text
        >
        <text class="count" wx:if="{{type=='chainMap'}}"
          >{{item.node_total_count}}个环节</text
        >
        <text
          class="tag"
          wx:if="{{type !== 'chainMap'}}"
          >{{item.model_type_string}}</text
        >
        <text
          class="count"
          wx:if="{{type=='chainMap'}}"
          style="margin-right: 12rpx;"
          >{{item.enterprise_count}}家企业</text
        >
      </view>
    </view>
  </view>
</view>
