/* companyPackage/pages/bindCardList/bindCardList.scss */
.bind-list {
  height: 100vh;
  background-color: #F7F7F7;
  padding-bottom: 188rpx;
}
.wrap {
  border-top: 20rpx solid #F7F7F7;
}
.bind-btn-wrap {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  padding: 10rpx 24rpx 78rpx;
  background-color: #fff;
}
.bind-btn-wrap .btn {
  width: 100%;
  height: 80rpx;
  background: linear-gradient(90deg, #F56E60 0%, #E72410 100%);
  font-size: 32rpx;
  color: #fff;
  text-align: center;
  line-height: 80rpx;
  border-radius: 8rpx;
}