/* components/uploadFile/uploadFile.scss */

/* <!-- 上传图片 --> */
.sug-imgbox {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  padding: 0 24rpx 32rpx;
  background: #fff;
}
.sug-img {
  position: relative;
  width: 160rpx;
  height: 160rpx;
  margin-right: 24rpx;
  margin-top: 32rpx;
}
.sug-img .delet {
  position: absolute;
  top: 0;
  right: 0;
  width: 36rpx;
  height: 36rpx;
}
.sug-upload {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 160rpx;
  height: 160rpx;
  border-radius: 8rpx;
  background-color: #F4F4F4;
  margin-top: 32rpx;
}
.sug-upload .img {
  width: 60rpx;
  height: 60rpx;
  margin-bottom: 8rpx;
}
.sug-upload .text {
  font-size: 24rpx;
  font-family: PingFang SC, PingFang SC-Regular;
  font-weight: 400;
  color: #74798C;
}
/* 自定义样式 */
.custom-sug-imgbox {
  display: flex;
  height: 200rpx;
}
.custom-sug-upload {
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 200rpx;
  height: 200rpx;
  border-radius: 8rpx;
  font-size: 24rpx;
  color: #74798C;
  background: #F4F4F4;
  /* border: 2rpx dashed #dedede; */
} 
.custom-sug-upload .custom-img {
  width: 60rpx;
  height: 60rpx;
  margin-bottom: 8rpx;
}
.custom-sug-img {
  flex-shrink: 0;
  width: 200rpx;
  height: 200rpx;
  margin-left: 24rpx;
  position: relative;
}
.custom-sug-img .opreate {
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
  height: 50rpx;
  background: rgba(0,0,0,0.5000);
  color: #fff;
  font-size: 24rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-radius: 0 0 8rpx 8rpx;
}
.custom-sug-img .opreate .line {
  height: 24rpx;
  border-left: 1rpx solid rgba(255,255,255,0.3400);
}
.custom-sug-img .opreate .opreate-text {
  flex: 1;
  text-align: center;
}