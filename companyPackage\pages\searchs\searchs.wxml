<import src="/template/null/null"></import>
<import src="/template/more/more"></import>
<import src="/template/loading/index"></import>
<wxs module="tool">
  var isShowCont = function (ent_name) {
    if (ent_name.length == '' &&
      ent_name.length <= 0) { //不是高级搜索的情况-不管登录 
      return true
    } else {
      return false
    }
  }
  var isShowHis = function (ent_name, isLogin) {
    if
      (ent_name.length > 0 || !isLogin) { //没有登录++有内容 也不显示
      return true
    }
    else { return false }
  }
  module.exports = {
    isShowCont: isShowCont, isShowHis:
      isShowHis
  }
</wxs>
<view class="pages">
  <!-- input -->
  <view class="searchs">
    <view class="s-input">
      <view class="s-input-img">
        <image src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/search.png" mode="aspectFit"></image>
      </view>
      <view class="s-input-item">
        <input class="s-input-item-i" type="text" placeholder="请输入企业名称关键词" placeholder-class="placeholder" bindblur="onBlur" value="{{ents_name}}" focus="{{inputShowed}}" bindinput="onInput" bindconfirm="onConfirm" confirm-type="search" />
        <view hidden="{{ent_name.length <= 0}}" catchtap="onClear" class="input-clear">
          <view class="clearIcon"></view>
        </view>
      </view>
    </view>
    <view class="search-cancel" bindtap="goBack" bindtap="goBack">取消</view>
  </view>
  <view class="ceshi"> </view>
  <!-- 历史记录 tool.isShowHis(ent_name, isLogin)-->
  <view class="history_wrap" hidden="{{tool.isShowHis(ent_name, isLogin)}}">
    <!-- 最近搜索 -->
    <block wx:if="{{historyList.length>0}}">
      <view class="page__autofit search_a">
        <view class="his_title">
          <text class="his_title_l">最近搜索</text>
          <view class="his_title_icon" bindtap="handleIcon" data-index="a">
            <image src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/delet.png" mode="aspectFit"></image>
          </view>
        </view>
        <view class="his_content">
          <!-- 内容 -->
          <view class="text-box">
            <block wx:for="{{historyList}}" wx:key="index">
              <view class="his_content_item" bindtap="historyTap" data-item="{{item}}">
                {{item}}
              </view>
            </block>
          </view>
        </view>
      </view>
    </block>
    <!-- 浏览历史 -->
    <view class="page__autofit search_b" hidden="{{!(browsingHistory.length>0)}}">
      <view class="his_titles">
        <text class="his_title_l">浏览历史</text>
        <view class="his_title_icon" bindtap="handleIcon" data-index="b">
          <image src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/delet.png" mode="aspectFit"></image>
        </view>
      </view>
      <scroll-view scroll-y="true" style="height:{{scrollHeight - 200 }}px;">
        <view class="his_content1">
          <!-- 内容 -->
          <view class="his_content1_item" wx:for="{{browsingHistory}}" wx:key="index" bindtap="goDetail" data-item="{{item}}">
            <text class="his_content1_item-l">{{item.enterprise_name}}</text>
            <text class="his_content1_item-r">{{item.create_time}}</text>
          </view>
          <!-- 占位 -->
          <view class="his_content1_item"> </view>
          <view class="his_content1_item"> </view>
        </view>
      </scroll-view>
    </view>
  </view>

  <!-- 搜索内容 hidden="{{ent_name.length =='' }}" 用了组件里面的高度获取不了 为0-->
  <!--  wx:if="{{ ent_name.length !='' && ent_name.length>0  }}" -->
  <!-- hidden="{{ tool.isShowCont(ent_name)}}" -->
  <view class="{{tool.isShowCont(ent_name) && 'hidesss'}}">
    <!-- 筛选条件 -->
    <DropDownMenu height="{{filtrateHeight}}" heightParams="{{heightParams}}" dropDownMenuTitle="{{dropDownMenuTitle}}" excludeFields="{{['area_code_list', 'industry_code_list','ent_name']}}" class="drop-menu" bindsubmit="onFlitter" bindvip="vipPop" />
    <block>
      <!-- 卡片 -->
      <view wx:if="{{!bazaarIsNull}}">
        <scroll-view bindrefresherrefresh="bazaarRefresher" refresher-triggered="{{bazaarIsTriggered}}" refresher-enabled bindscrolltolower="bazaarloadMore" scroll-y style="height: {{cardHeight}}px;background: #f7f7f7; padding-bottom: 20rpx;">
          <view class="bus-card">
            <block>
              <block wx:if="{{bazaarlist.length>0}}">
                <block wx:for="{{bazaarlist}}" wx:key="index">
                  <Card bindcardFun="onCard" bindhandleTit="handleTit" obj="{{item}}" bindtap="goto" data-item="{{item}}" />
                </block>
              </block>
              <!-- <view wx:else style="height: {{cardHeight}}px;">
                <template is="load"></template>
              </view> -->
            </block>
            <!-- 未登录只能查看5条数据 -->
            <view class="vip" wx:if="{{!isLogin && bazaarlist.length >= 5}}">
              <vloginOverlay bindsubmit="login"></vloginOverlay>
            </view>
            <!-- 开通vip -->
            <view class="vip" wx:if="{{permission=='普通VIP'&&bazaarlist.length>=50}}">
              <vipOccupancy bindsubmit="vipPop"></vipOccupancy>
            </view>
            <view wx:elif="{{bazaarlist.length<count}}" style="width: 100%;">
              <template is="more" data="{{hasData:bazaarHasData}}"></template>
            </view>
          </view>
        </scroll-view>
      </view>
      <!--暂无数据 -->
      <view wx:if="{{bazaarIsNull}}" style="width: 100%;height: 600rpx;">
        <template is="null"></template>
      </view>
    </block>
  </view>
</view>

<Contact visible="{{showContact}}" entId="{{activeEntId}}"></Contact>
<!-- 地址弹窗 -->
<dialog visible="{{showAddress}}" title="地址" isShowConfirm="{{false}}" showFooter="{{false}}">
  <view class="dialog-con">
    <view style="padding: 0 50rpx;">
      <map id="map" longitude="{{location.lon}}" latitude="{{location.lat}}" markers="{{addmarkers}}" scale="{{11}}" style="width: 100%; height: 306rpx;">
      </map>
    </view>
    <view style="margin: 32rpx 0;font-size: 28rpx;">{{locationTxt}}</view>
    <view bindtap="goMap" class="map"> 导航 </view>
    <view class="cancel" bindtap="onCloseAddress"> 取消 </view>
  </view>
</dialog>
<!-- vip弹窗 -->
<VipPop visible="{{vipVisible}}"></VipPop>