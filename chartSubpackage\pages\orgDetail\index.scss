@import "../../../template/null/null.scss";

.org_detail_wrapper {
  width: 100%;
  height: 100vh;
  scroll-behavior: auto;
  overflow-y: scroll;
  background-color: #f7f7f7;
  /* position: relative; */
}

::-webkit-scrollbar {
  display: none;
  width: 0;
  height: 0;
  color: transparent;
}

/* 弹窗自定义样式 */
.dialog_content {
  width: 100%;
  margin-bottom: 22rpx;
  /* padding: 0 16rpx; */
}

.dialog_content>text {
  display: inline-block;
  width: auto;
  height: 36rpx;
  background: rgba(253, 147, 49, 0.1);
  border-radius: 4rpx;
  padding: 2rpx 10rpx;
  margin-right: 12rpx;
  margin-bottom: 20rpx;
  font-size: 24rpx;
  font-family:
    PingFang SC-Regular,
    PingFang SC;
  font-weight: 400;
  color: #fd9331;
  text-align: center;
}

#header {
  height: 376rpx;
  background-color: olive;
  position: relative;
}

.head_bg {
  width: 100%;
  height: 200rpx;
  position: absolute;
  top: 0;
  z-index: 1;
}

/* 头部 */
.header-box {
  position: absolute;
  top: 66rpx;
  width: 750rpx;
  height: auto;
  background: #ffffff;
  border-radius: 40rpx 40rpx 0rpx 0rpx;
  z-index: 2;
  margin-bottom: 20rpx;
}

.header-box .top {
  width: 100%;
  height: 184rpx;
  padding: 40rpx 24rpx 24rpx;
  display: flex;
}

.header-box .top .img {
  width: 120rpx;
  height: 120rpx;
  margin-right: 20rpx;
  border-radius: 8rpx 8rpx 8rpx 8rpx;
}

.header-box .top .top_right>view:first-child {
  width: 562rpx;
  height: 44rpx;
  font-size: 32rpx;
  font-family:
    PingFang SC-Semibold,
    PingFang SC;
  font-weight: 600;
  color: #20263a;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  -o-text-overflow: ellipsis;
  margin-bottom: 16rpx;
}

.header-box .top .top_right>view:last-child {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.tag_box {
  flex: 1;
  font-size: 24rpx;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  -o-text-overflow: ellipsis;
}

.tag_box>text {
  display: inline-block;
  width: auto;
  height: 36rpx;
  background: rgba(253, 147, 49, 0.1);
  border-radius: 4rpx;
  padding: 2rpx 10rpx;
  margin-right: 12rpx;
  font-size: 24rpx;
  font-family:
    PingFang SC-Regular,
    PingFang SC;
  font-weight: 400;
  color: #fd9331;
  text-align: center;
}

.red_right {
  font-size: 24rpx;
  min-width: 100rpx;
  color: #e72410;
  text-align: right;
}

.red_right>image {
  width: 20rpx;
  height: 20rpx;
}

.header-box .mind {
  width: 100%;
  min-height: 96rpx;
  border-bottom: 1rpx solid #eeeeee;
}

.header-box .mind>view {
  display: flex;
  margin: 0 24rpx 26rpx 24rpx;
}

.header-box .mind>view>text:first-child {
  width: 140rpx;
  height: 40rpx;
  font-size: 28rpx;
  font-family:
    PingFang SC-Regular,
    PingFang SC;
  font-weight: 400;
  color: #74798c;
}

.header-box .mind>view>text:last-child {
  flex: 1;
  font-size: 28rpx;
  font-family:
    PingFang SC-Regular,
    PingFang SC;
  font-weight: 400;
  color: #20263a;
}

.header-box .mind .com_name {
  width: 562rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 28rpx;
}

.link {
  color: #1e75db !important;
}

.address_icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 8rpx;
}

.address {
  flex: 1;
  display: flex;
  justify-content: flex-start;
  font-size: 28rpx;
  font-family:
    PingFang SC-Regular,
    PingFang SC;
  font-weight: 400;
  color: #20263a;
}

.address .address_icon {
  margin-top: 6rpx;
}

/* 横向滚动 */
.swipe_tab {
  width: 750rpx;
  height: 88rpx;
  display: flex;
  box-sizing: border-box;
  background: #fff;
  flex-wrap: nowrap;
  white-space: nowrap;
  margin-bottom: 20rpx;
}

.swipe_tab .tab_item {
  width: 112rpx;
  height: 88rpx;
  margin-right: 60rpx;
  text-align: center;
  line-height: 88rpx;
  font-size: 28rpx;
  font-weight: 400;
  color: #74798c;
  box-sizing: border-box;
  display: inline-block;
  text-align: center;
}

.swipe_tab .tab_item:first-child {
  margin-left: 24rpx;
}

.swipe_tab .tab_item_active {
  position: relative;
  font-size: 28rpx;
  font-weight: 600;
  color: rgba(231, 36, 16, 1);
}

.swipe_tab .tab_item_active::after {
  position: absolute;
  content: "";
  left: 32%;
  bottom: 10rpx;
  width: 40rpx;
  height: 6rpx;
  background: linear-gradient(to right, rgba(231, 36, 16, 1), rgba(241, 123, 111, 1));
}

/* 相关资讯 */
.information {
  width: 100%;
  min-height: 100rpx;
  margin-bottom: 60rpx;
}

.information .list {
  width: 100%;
  height: 144rpx;
  padding: 32rpx 0;
  display: flex;
  border-bottom: 1rpx solid #eeeeee;
}

.list_box {
  padding: 0 24rpx;
}

.list_box>view:last-child {
  border-bottom: none !important;
}

.information .list .info_img {
  width: 80rpx;
  height: 80rpx;
  margin-right: 20rpx;
}

.information .list .info_title {
  flex: 1;
  height: 80rpx;
  display: flex;
  align-items: center;
  font-size: 28rpx;
  font-family:
    PingFang SC-Regular,
    PingFang SC;
  font-weight: 400;
  color: #20263a;
  text-overflow: -o-ellipsis-lastline;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
}

.information .list .info_time {
  min-width: 140rpx;
  height: 34rpx;
  margin-left: 20rpx;
  font-size: 24rpx;
  font-family:
    PingFang SC-Regular,
    PingFang SC;
  font-weight: 400;
  color: #9b9eac;
}

.event_box {
  width: 100%;
  padding: 0 24rpx;
}

.chart_box {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 32rpx 24rpx;
  /* background-color: olivedrab; */
}

/* 投资偏好 提示 */
.preferenceInfo {
  position: relative;
  display: flex;
  align-items: center;
  flex-flow: row wrap;
  margin-left: 8rpx;
}

.tips {
  position: absolute;
  top: 28rpx;
  left: -140rpx;
  display: flex;
  align-items: center;
  width: 304rpx;
  height: 140rpx;
  padding: 28rpx;
  font-size: 24rpx;
  font-family:
    PingFang SC-Regular,
    PingFang SC;
  font-weight: 400;
  color: #74798c;
  background: url("data:image/png;base64,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") no-repeat;
  background-size: 100% 100%;
  z-index: 999;
}

.tips>text {
  display: inline-block;
  text-align: left;
}

.map_right {
  width: 240rpx;
  height: 56rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.map_right>view {
  width: 120rpx;
  height: 56rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.defulat1 {
  width: 120rpx;
  height: 56rpx;
  background: #f4f4f4;
  border-radius: 8rpx 0rpx 0rpx 8rpx;
  font-size: 24rpx;
  font-family:
    PingFang SC-Regular,
    PingFang SC;
  font-weight: 400;
  color: #20263a;
}

.defulat2 {
  width: 120rpx;
  height: 56rpx;
  background: #f4f4f4;
  border-radius: 0rpx 8rpx 8rpx 0rpx;
  font-size: 24rpx;
  font-family:
    PingFang SC-Regular,
    PingFang SC;
  font-weight: 400;
  color: #20263a;
}

.map_active1 {
  background: linear-gradient(90deg, #e72410 0%, #f17b6f 100%);
  border-radius: 8rpx 0rpx 0rpx 8rpx;
  font-size: 24rpx;
  font-family:
    PingFang SC-Regular,
    PingFang SC;
  font-weight: 400;
  color: #ffffff;
}

.map_active2 {
  background: linear-gradient(90deg, #f17b6f 0%, #e72410 100%);
  border-radius: 0rpx 8rpx 8rpx 0rpx;
  font-size: 24rpx;
  font-family:
    PingFang SC-Regular,
    PingFang SC;
  font-weight: 400;
  color: #ffffff;
}

.map {
  width: 700rpx;
  height: 520rpx;
}