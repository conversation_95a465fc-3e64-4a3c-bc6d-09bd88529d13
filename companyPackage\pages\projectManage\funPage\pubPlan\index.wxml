<!-- 这里面的input框能共用 -->
<view class="addUser">
  <form>
    <view class="from-box">
      <view class="area-title">
        计划内容
      </view>
      <view>
        <!--  @input="textareaBInput" -->
        <textarea maxlength="{{500}}" placeholder-class="inputpla" class="textarea" placeholder="请输入跟进内容（必填）" bindinput="tareaChange" value="{{form.content}}"></textarea>
        <view class="word-number">{{wordNum|0}}/500</view>
      </view>
      <!--代办时间 -->
      <view class="cu-form-groups">
        <view class="title">待办时间</view>
        <input placeholder="请选择待办时间（必选）" class="input" placeholder-class="inputpla" disabled bindtap="handleInput" data-type="commission" value="{{form.toDoName}}"></input>
      </view>
      <view class="cu-form-groups">
        <view class="title">提醒时间</view>
        <input placeholder="请选择提醒时间（必选）" class="input" placeholder-class="inputpla" disabled bindtap="handleInput" data-type="remind" value="{{form.remindName}}"></input>
      </view>
    </view>
    <view class="check-box">
      <view class="check-group">
        <view class="check-group-l">
          <view>系统通知</view>
          <view>可在小程序个人中心-消息通知内查看</view>
        </view>
        <!-- <switch checked="{{form.system_notice}}" bindchange="switch1Change" color="#076ee4" /> -->
        <image src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/cyt_v2/image/business/btn_on.png" class="check-group-r" wx:if="{{form.system_notice}}" bindtap="switch1Change"></image>
        <image src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/cyt_v2/image/business/btn_off.png" class="check-group-r" wx:if="{{!form.system_notice}}" bindtap="switch1Change"></image>
      </view>
      <!-- <view class="check-group">
                <view class="check-group-l">
                    <view>微信通知</view>
                    <view>微信推送 <text class="zw"></text>{{userName}}<text class="zw"></text>
                         <text class="ts">解绑</text> 
                    </view>
                </view>
                <switch checked="{{form.wechat_notice}}" bindchange="switch2Change" color="#F17B6F" />
                <image src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/cyt_v2/image/business/btn_on.png" class="check-group-r" wx:if="{{form.wechat_notice}}" bindtap="switch2Change"></image>
                <image src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/cyt_v2/image/business/btn_off.png" class="check-group-r" wx:if="{{!form.wechat_notice}}" bindtap="switch2Change"></image>
            </view> -->
    </view>
    <!-- 弹窗 -->
    <btm-list-pop title="提醒时间" options="{{commissionList}}" visible="{{iscommission}}" bindget="commissionPop" />
    <datePop visible="{{isremind}}" bindclose="remindClosePop" bindsubmit="remindSubPop"></datePop>
    <!-- 按钮 -->
    <view class="box-btn">
      <button class="btm-btn" bindtap="formSubmit">保存</button>
    </view>
  </form>
</view>