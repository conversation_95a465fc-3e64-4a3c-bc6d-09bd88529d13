import {setTagColor} from '../../../../../utils/util.js';
import {daily} from '../../../../../service/api';
const app = getApp();
Component({
  options: {
    addGlobalClass: true
  },
  /**
   * 组件的属性列表
   */

  properties: {
    searchVal: {
      type: String,
      value: ''
    },
    cardHeight: {
      type: Number
    },
    souceList: {
      //传进来的数组 ，渲染 -目前是写死的 --通过setTagColor处理一下标签
      type: Array,
      observer(newVal) {
        let enumProcessList = [
          '未跟进',
          '有效项目',
          '在谈项目',
          '到访项目',
          '签约项目'
        ];
        let enumTypeList = ['其他项目', '普通项目', '重点项目'];
        let temp = newVal.map(item => {
          let tagList = [];
          // if (item.assigned == false) {
          //   tagList.push({name:'未跟进',type:1})
          // } else {
          if (item.project_process != undefined) {
            tagList.push({
              name: enumProcessList[item.project_process],
              type: 1
            });
          }

          // }
          if (item.project_type != undefined) {
            tagList.push({
              name: enumTypeList[item.project_type],
              type: 2
            });
          }
          if (item.tags) {
            if (item.tags.split(',').length) {
              item.tags = setTagColor(item.tags.split(','));
            }
          }
          item.ent_name_show = `<div style='width: 440rpx; overflow:hidden;white-space:nowrap;text-overflow:ellipsis;'>${
            item.ent_name
              ? item.ent_name.replace(
                  new RegExp(this.data.searchVal),
                  '<span style="color:red">' + this.data.searchVal + '</span>'
                )
              : ''
          }</div>`;
          return {
            ...item,
            tagsShow: tagList
          };
        });
        this.setData({
          souceData: temp
        });
        console.log(this.data.souceData);
      }
    },
    hasData: {
      type: Boolean,
      value: true
    },
    pageSize: {
      type: Number,
      value: 10
    },
    permission: {
      type: String,
      value: ''
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    souceData: [],
    isLogin: app.isLogin()
  },

  /**
   * 组件的方法列表
   */
  methods: {
    login() {
      const url = '/companyPackage/pages/projectManage/index';
      app.route(this, `/pages/login/login?url=${url}`);
    },
    vipPop(val) {
      this.triggerEvent('vipPop', val);
    },
    // 收藏
    collect(e) {
      const item = e.target.dataset['item'] || e.currentTarget.dataset['item'];
      const index =
        e.target.dataset['index'] || e.currentTarget.dataset['index'];
      console.log('点击收藏', index);
      this.triggerEvent('cardFun', {
        type: 'collect',
        index,
        data: item
      });
    },
    // 联系方式
    relation(e) {
      const item = e.target.dataset['item'] || e.currentTarget.dataset['item'];
      this.triggerEvent('cardFun', {
        type: 'relation',
        data: item
      });
    },
    // 官网
    official(e) {
      const item = e.target.dataset['item'] || e.currentTarget.dataset['item'];

      if (item.ent_id) {
        daily.headInfo(item.ent_id).then(res => {
          if (res.official_website) {
            wx.setClipboardData({
              data: res.official_website,
              success(res) {
                wx.showToast({
                  title: '复制成功',
                  icon: 'none'
                });
              }
            });
          } else {
            wx.showToast({
              title: '该企业暂无官网',
              icon: 'none'
            });
          }
        });
      } else {
        wx.showToast({
          title: '该企业暂无官网',
          icon: 'none'
        });
      }
    },
    // 发地址
    site(e) {
      const item = e.target.dataset['item'] || e.currentTarget.dataset['item'];
      console.log(item);
      this.triggerEvent('cardFun', {
        type: 'site',
        data: item
      });
    },
    loadMore() {
      this.triggerEvent('loadMore');
    },
    delet(e) {
      let tempObj = e.currentTarget.dataset.item;
      delete tempObj.tagsShow;
      this.triggerEvent('deletPro', tempObj);
    },
    handleRefresher() {
      console.log(1231231);
      this.triggerEvent('refresh');
    },
    goDetail(e) {
      let {id} = e.currentTarget.dataset.item;
      console.log('跳转id', id);
      if (id) {
        app.route(this, `/companyPackage/pages/projectDetail/index?id=${id}`);
      }
    },
    errorFunction(e) {
      var index = e.currentTarget.dataset.index;
      this.setData({
        [`souceData[${index}].logo`]:
          'https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/default.png'
      });
    }
  }
});
