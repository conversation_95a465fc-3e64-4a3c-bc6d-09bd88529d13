// Tab 组件
import {TAB_OPTIONS} from '../../../utils/constant.js';

Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 当前选中的 tab code
    currentCode: {
      type: String,
      value: 'chainMap'
    },
    // 自定义 Tab 列表
    customTabList: {
      type: Array,
      value: []
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    // Tab 数据
    tabList: TAB_OPTIONS.filter(item => item.chain_code !== 'all')
  },

  /**
   * 组件的方法列表
   */
  methods: {
    // 点击 Tab
    onTabClick(e) {
      const {index} = e.currentTarget.dataset;
      const tabList = this.data.customTabList.length
        ? this.data.customTabList
        : this.data.tabList;
      const selectedTab = tabList[index];

      // 触发父组件事件
      this.triggerEvent('tabChange', {
        chain_code: selectedTab.chain_code,
        chain_name: selectedTab.chain_name
      });
    }
  }
});
