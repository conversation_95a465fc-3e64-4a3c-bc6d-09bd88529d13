@import '../../../template/null/null.scss';
@import '../../../template/tabBar/index.scss';
@import './lFilter/index.scss';

.page {
  width: 100%;
  height: 100vh;
  scroll-behavior: auto;
  overflow-y: scroll;
  background-color: #F7F7F7;
  position: relative;
}

::-webkit-scrollbar {
  display: none;
  width: 0;
  height: 0;
  color: transparent;
}

.head_bg {
  width: 100%;
  height: 200rpx;
  position: absolute;
  top: 0;
  z-index: 1;
}

/* 头部 */
.header-box {
  position: absolute;
  top: 66rpx;
  width: 750rpx;
  min-height: 344rpx;
  padding: 40rpx 24rpx;
  background: #FFFFFF;
  border-radius: 40rpx 40rpx 0rpx 0rpx;
  z-index: 2;
}

.box-up,
.box-down {
  display: flex;
}

.box-down-item {
  font-size: 29rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #20263A;
  line-height: 33rpx;
}

.box-down-item .box-down-label {
  color: #74798C !important;
}

.fuc-box {
  margin-top: 28rpx;
  border-top: 1rpx solid #EEEEEE;
  display: flex;
  justify-content: flex-end;
}

.fuc-item {
  margin-top: 20rpx;
  display: flex;
  height: 52rpx;
  border-radius: 8rpx 8rpx 8rpx 8rpx;
  border: 1rpx solid #DEDEDE;
  margin-right: 16rpx;
  padding: 10rpx 12rpx;
  font-size: 24rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #20263A;
  line-height: 28rpx;
}

.fuc-item-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 4rpx;
  margin-top: -2rpx;
}

.page_content {
  position: absolute;
}

.track-box {
  background: #fff;
  margin-top: 15rpx;
  padding: 24rpx;
}

.contact-box {
  background: #fff;
  margin-top: 15rpx;
  padding: 24rpx;
  border-top: 1rpx solid #f7f7f7;
  padding-bottom: 100rpx;
}

.contact-list {
  padding: 36rpx 24rpx;
  border-bottom: 20rpx solid #F7F7F7;
}

.plan-item {
  border: 1rpx solid #E72410;
  border-radius: 16rpx 16rpx 16rpx 16rpx;
  padding: 32rpx;
  margin-bottom: 30rpx;
}

.item-box-top {
  display: flex;
  justify-content: space-between;
}

.item-box-tag {
  height: 48rpx;
  background: rgba(231, 36, 16, 0.2);
  font-size: 28rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #E72410;
  line-height: 36rpx;
  padding: 6rpx 16rpx;
}

.icon_fnc_box {
  display: flex;
}

.icon_fnc_box_icon {
  width: 36rpx;
  height: 36rpx;
  margin-left: 32rpx;
}

.item-box-center {
  margin-top: 16rpx;
  font-size: 28rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #20263A;
  line-height: 33rpx;
}

.item-box-center .item-box-label {
  color: #74798C !important;
}

.header-box .left {
  /* 定宽 */
  width: 120rpx;
  height: 120rpx;
}

.header-box .left>image {

  margin-top: 0;
}

.header-box .right {
  margin-left: 20rpx;
  /* flex 属性是 flex-grow、flex-shrink 和 flex-basis 属性的简写属性。 */
  flex: 1;
}

.header-box .right .title,
.header-box .right .name_tag {
  margin-bottom: 16rpx;
}

.com_name {
  display: inline-block;
  width: 550rpx;
  overflow: hidden;
  height: 44rpx;
  font-size: 32rpx;
  font-family: PingFang SC-Semibold, PingFang SC;
  font-weight: 600;
  color: #20263A;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}

.invest_round_name {
  margin-left: 20rpx;
  min-width: 236rpx;
  height: 36rpx;
  padding: 2rpx 10rpx;
  font-size: 24rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #26C8A7;
  border-radius: 4rpx 4rpx 4rpx 4rpx;
  border: 1rpx solid #26C8A7;
}

.header-box .right .title>text:first-child {
  width: 140rpx;
  height: 40rpx;
  font-size: 28rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #74798C;
}

.header-box .right .title>text:last-child {
  width: auto;
  height: 40rpx;
  font-size: 28rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #20263A;
}

.tag_box {
  /* display: flex; */
  min-height: 82rpx;
  overflow: hidden;

}

.tag_box_II {
  position: relative;
  text-overflow: clip;
  overflow: hidden;
  -webkit-line-clamp: 2;
  display: -webkit-box;
  -webkit-box-orient: vertical;
}

.tag_item {
  width: 100%;
  min-height: 82rpx;
  display: flex;
  /* background-color: #1E75DB; */
}

.track_status {
  margin-right: 16rpx;
  height: 44rpx;
  display: flex;
  padding: 4rpx 12rpx;
  font-size: 28rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  line-height: 36rpx;
}

.track_status_icon {
  width: 32rpx;
  height: 32rpx;
  margin-left: 4rpx;
}

.tag_box_more {
  position: absolute;
  right: -5rpx;
  bottom: 18rpx;
  background-color: #FFFFFF;
}

.dialog_content {
  margin-bottom: 42rpx;
}

.red_right {
  display: none;
  width: 76rpx;
  color: #E72410;
  font-size: 24rpx;
  font-weight: 400;
  text-align: right;
}

.red_right>image {
  width: 20rpx;
  height: 20rpx;
}

.mine {
  background-color: #fff;
  height: 650px;
}

/*  基本信息  */
.base_info {
  padding: 32rpx 24rpx;
}

.base_info>view {
  display: flex;
  margin-bottom: 16rpx;
}

.base_info>view>text:first-child {
  width: 140rpx;
  height: 40rpx;
  font-size: 28rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #74798C;
}

.base_info>view>text:last-child {
  display: inline-block;
  margin-left: 28rpx;
  /* flex 属性是 flex-grow、flex-shrink 和 flex-basis 属性的简写属性。 */
  flex: 1;
  font-size: 28rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #20263A;
}

.link {
  color: #1E75DB !important;
}

.website {
  /* display: flex; */
  height: 40rpx;
  /* align-items: center; */
  font-size: 28rpx;
  margin-left: 18rpx;
  text-overflow: -o-ellipsis-lastline;
  overflow: hidden;
  text-overflow: ellipsis;
  display: flex;
  justify-content: center;
  align-items: center;
  -webkit-line-clamp: 1;
  line-clamp: 1;
  -webkit-box-orient: vertical;
}

.website>text {
  display: inline-block;
  height: 40rpx;
}

.website_icon {
  width: 32rpx;
  height: 32rpx;
}

.history {
  padding: 40rpx 24rpx 100rpx 40rpx;
  background-color: #fff;
}

.history-tips {
  font-size: 28rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #74798C;
  line-height: 33rpx;
}

.history .list {
  width: 100%;
  min-height: 126rpx;
  padding-left: 34rpx;
  position: relative;
  /* background-color: orange; */
}

.history .list::before {
  position: absolute;
  content: '';
  left: 0rpx;
  top: 10rpx;
  width: 16rpx;
  height: 16rpx;
  background: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAYAAADED76LAAAAxklEQVQoU2NkYGBgeCbPo8nAwtTOwMDkCuIz/mfY+//vn3Kph1+uM0IkmY8yMjIKgiRh4P///+8Z/vy1ZnyuIrCJgYHBl93Vm4GvZQIDw79/DJ/qihh+7t7K8P//v42Mz5QFvjIyMnCJHr/JwCwiBjbg79s3DK8tVBn+/2f4BlcgdvI2A5OQCETBm1cMry3VYQr4NjAyMvmDrWjqY2BgZGL4VFPA8HPPVgaGfwybCTsS5k1GVpbO//8ZnCG++Leb4c+/SpA3Ad5NUyuZnpw8AAAAAElFTkSuQmCC') no-repeat;
  background-position: center;
}

.history .list::after {
  position: absolute;
  content: '';
  left: 7rpx;
  top: 26rpx;
  width: 2px;
  height: 100%;
  border-left: 2rpx dotted #DEDEDE;
}

.history .list:last-child::after {
  display: none;
}

.contact-list-head {
  display: flex;
  justify-content: space-between;
}

.item-l-contact_name {
  font-size: 32rpx;
  font-family: PingFang SC-Semibold, PingFang SC;
  font-weight: 600;
  color: #20263A;
  line-height: 38rpx;
}

.item-l-position {
  margin-left: 16rpx;
  font-size: 28rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #74798C;
  line-height: 33rpx;
}

.contact-list-body {
  margin-top: 24rpx;

}

.contact-list-body text {
  font-size: 28rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #74798C;
  line-height: 33rpx;
}

.socialContact {
  padding: 30rpx 24rpx;
  display: flex;
  justify-content: space-between;
}

.phone-box {
  display: flex;
}

.phone-box view {
  font-size: 30rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #1E75DB;
  line-height: 35rpx;
}

.name-box {
  font-size: 30rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #20263A;
  line-height: 35rpx;
}

.sourse-box {
  font-size: 30rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #74798C;
  line-height: 35rpx;
}

.phone-box-icon {
  height: 36rpx;
  width: 36rpx;
  margin-right: 4prx;
}

.footer-box {
  border-top: 1rpx solid #EEEEEE;
  width: 100%;
  background-color: #fff;
  z-index: 100;
  position: fixed;
  bottom: 0rpx;
  display: flex;
  padding: 24rpx 96rpx;
  justify-content: space-between;
}

.footer-box-item {
  width: 130rpx;
}

.footer-box-item-icon {
  width: 100%;
  display: flex;
  justify-content: center;
  margin-bottom: 4rpx;
}

.footer-box-item-icon image {
  width: 48rpx;
  height: 48rpx;
}

.footer-box-item-lable {
  width: 100%;
  font-size: 20rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #20263A;
  line-height: 23rpx;
  text-align: center;
}

.history .list .date {
  height: 34rpx;
  font-size: 24rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #74798C;
}


/* 相关资讯 */
.information {
  width: 100%;
  padding: 0 24rpx;
}

.information .list {
  width: 100%;
  height: 144rpx;
  padding: 32rpx 0;
  display: flex;
  border-bottom: 1rpx solid #EEEEEE;
}

.list_box>view:last-child {
  border-bottom: none !important;
}

.information .list .info_img {
  width: 80rpx;
  height: 80rpx;
  margin-right: 20rpx;
}

.information .list .info_title {
  flex: 1;
  height: 80rpx;
  font-size: 28rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #20263A;
  text-overflow: -o-ellipsis-lastline;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
}

.information .list .info_time {
  min-width: 130rpx;
  height: 34rpx;
  margin-left: 20rpx;
  font-size: 24rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #9B9EAC;
}

.information .more {
  position: relative;
  width: 492rpx;
  height: 116rpx;
  margin: 0rpx auto;
  padding: 40rpx 0;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #E72410;
}

.information .more::before {
  position: absolute;
  content: '';
  width: 160rpx;
  height: 1rpx;
  top: 50%;
  left: 0;
  background-color: rgba(231, 36, 16, 0.16);
}

.information .more::after {
  position: absolute;
  content: '';
  width: 160rpx;
  height: 1rpx;
  top: 50%;
  right: 0;
  background-color: rgba(231, 36, 16, 0.16);
}

.more .red_right {
  width: 20rpx;
  height: 20rpx;
  margin-left: 8rpx;
}

.page_conpany_nav {
  height: 96rpx !important;
}

.select-item-box {
  /* max-height: 480rpx; */
  overflow-y: auto;
  padding: 0 24rpx;
  padding-bottom: 113rpx;
}

.select-item {
  height: 96rpx;
  color: #74798C;
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
}

.select-item.active .select {
  width: 32rpx;
  height: 32rpx;
  background: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAAATlJREFUWEftlj1uwjAYhl8DGwi8komiMLGyU4aeoBfgBqh7pa6MHIKBWyBuwAGQMAONOlSy1KVDEqMgWQpJIP6hSSWS2dHz+P38+TNByR8pmY9KoErg8RLwXDru7vhGdl+hCXz26YzUsBDAwtnxt0iiMAEJP+9cgDV/g1H7+PNdiEASjgCTLuOskARuwf9cIA+eKbDvUfrEOLedESrwlIDn0g8ITEWIZ2fPD6YSqvALAc9tDYH6FiCN6JSaSujAUwl8DTqvocDKVEIXnnkGTCVM4Fe7QFfCFH6zDVUlbOC590CehC08VyBacE3iHnAlgSwJAiwFwbscLPG7XffuUB5GF0lIigCzgSsnIHnJctjCtQVi5ZjDJy9ypOrGHl+vXIL4T2ugMQF8G3ApT7IsYaME7rHzKoF/k8AJqsP2IbiZ1dsAAAAASUVORK5CYII=') no-repeat center center;
  background-size: 100% 100%;
}



.select-item text {
  font-size: 28rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #20263A;
  line-height: 33rpx;
}

.active text {
  font-weight: 600;
  color: #E72410 !important;
}

.select-item:not(:first-child)::before {
  content: " ";
  height: 2rpx;
  width: 100%;
  background: #EEEEEE;
  position: absolute;
  top: 0;
  transform: scaleY(0.5);
}