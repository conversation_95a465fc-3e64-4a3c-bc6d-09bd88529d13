/* companyPackage/pages/addCard.scss */
.page_cardForm {
  background-color: #f7f7f7;
  height: 100vh;
}
.base-info {
  font-size: 28rpx;
  padding: 24rpx 0 16rpx 24rpx;
  color: #74798c;
}
.wrap {
  padding: 0 24rpx;
  background-color: #fff;
}
.btn-box {
  width: 100%;
  height: 168rpx;
  background-color: #fff;
  padding: 10rpx 24rpx;
  position: fixed;
  bottom: 0;
}

.btn-box .btn {
  width: 100%;
  height: 80rpx;
  background: linear-gradient(90deg, #f56e60 0%, #e72410 100%);
  border-radius: 8rpx;
  font-size: 32rpx;
  font-weight: 400;
  color: #ffffff;
  text-align: center;
  line-height: 80rpx;
}
