import {home, chain} from '../../../../service/api';
import {getHeight} from '../../../../utils/height';
const app = getApp();
Page({
  data: {
    inputShowed: false, //是否聚焦
    inputVal: '', //搜索值
    allChain: [],
    searrchRes: [],
    loading: false,
    isLogin: app.isLogin()
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: async function (options) {
    this.scrollH();
    let res = await chain.chainAll();
    let allChain = [];
    res.forEach(item => {
      allChain.push({...item});
      item.children &&
        item.children.forEach(i => {
          // i.children && i.children.forEach(itm => {
          //   allChain.push({ ...itm })
          // })
          allChain.push({...i});
        });
    });
    this.setData({
      allChain
    });

    // 外部跳转进入
    const {key_word = null, second_model_type} = options;
    if (key_word) {
      this.setData({second_model_type});
      this.onInput({detail: {value: key_word}});
    }
  },
  onShow() {
    this.setData({isLogin: app.isLogin()});
  },

  // input相关--onblur后面真机看是否保留
  onConfirm: function (e) {
    let keyword = e.detail.value;
  },
  onClear() {
    this.unLocked();
    this.setData({
      inputVal: '',
      searrchRes: []
    });
  },
  init() {
    this.setData({
      inputVal: '',
      inputShowed: false
    });
  },
  isBlur() {
    this.setData({
      inputShowed: true
    });
  },
  onBlur() {
    this.setData({
      inputShowed: false
    });
  },
  onInput(e) {
    let keyword = e.detail.value;
    this.setData({
      inputVal: keyword,
      loading: true
    });

    clearTimeout(this.data.timer);
    // if (keyword.trim().length == 0) return;
    let timer = setTimeout(() => {
      // 向后台发送数据
      this.quest(keyword);
    }, 600);
    this.data.timer = timer;
  },
  goBack() {
    this.unLocked();
    this.init();
    wx.navigateBack({
      delta: 1 // 回退前 delta(默认为1) 页面
    });
  },
  unLocked() {
    wx.hideKeyboard();
    this.setData({
      loading: false,
      inputShowed: false
    });
  },
  scrollH() {
    var that = this;
    getHeight(that, ['.searchs'], data => {
      const {screeHeight, res} = data;
      let h1 = res[0]?.height || 0;
      let scrollHeight = screeHeight - h1 - 38;
      that.setData({
        scrollHeight
      });
    });
  },
  quest(val) {
    if (!val) {
      this.setData({
        searrchRes: []
      });
      return;
    }
    let res = this.data.allChain.filter(item => {
      return item.name.indexOf(val) != -1;
    });
    res.forEach(item => {
      item.chainArr = this.arrfiy(val, item.name);
    });
    console.log('res', res, res.length);
    this.setData({
      searrchRes: res,
      loading: false
    });
  },
  arrfiy(key, chain) {
    return chain.replace(new RegExp(`${key}`, 'g'), `%%${key}%%`).split('%%');
  },
  onClose() {
    this.setData({
      showVisible: false
    });
  },
  // handleChain({ currentTarget: { dataset: { item } } }) {
  //   const url = `/companyPackage/pages/industryChain/newChainMap/newChainMap?chainTypeName=${item?.name || item.chain_name}&industry_code=${item.chain_code}`;
  //   app.route(this, url)
  // },
  // 点击产业链
  onIndustrClick({
    currentTarget: {
      dataset: {item}
    }
  }) {
    const {second_model_type, isLogin} = this.data;
    if (isLogin) {
      let params = {
        enterprise_name: item.name,
        enterprise_id: item.chain_code,
        behavior_history_mode: 'INDEX_PAGE',
        enterprise_log: '-',
        model_type: second_model_type ? 'BUSINESS' : 'CHAIN',
        second_model_type
      };
      home.addBevHis(params); //新增浏览历史
    }
    const url = `/industryPackage/pages/IndustryListMasonry/index?chain_name=${item.name}&chain_code=${item.chain_code}`;
    app.route(this, url);
  }
});
