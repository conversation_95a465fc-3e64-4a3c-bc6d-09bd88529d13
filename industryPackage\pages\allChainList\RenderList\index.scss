// 产业列表渲染组件样式 - 参考 hunt 组件布局
.searWrap {
  display: flex;
  background: #f2f2f2;
  // 左侧筛选区域
  .searWrap-r {
    width: 194rpx;
    background: #f2f2f2;

    // 隐藏滚动条
    ::-webkit-scrollbar {
      display: none;
      width: 0 !important;
      height: 0 !important;
      opacity: 0;
    }
    // 左侧筛选项
    .searL {
      width: 100%;
      height: 100%;

      // 标题样式 - 参考 hunt 组件
      .tit {
        padding: 24rpx 26rpx;
        font-size: 24rpx;
        color: #525665;
        font-weight: 400;

        transition: all 0.2s ease;
      }
      // border: 1px solid red;

      // 激活状态 - 完全参考 hunt 组件样式
      .active {
        position: relative;
        background: #fff;
        border-radius: 16rpx 0 0 16rpx;
        color: #e72410;
        font-weight: 600;
        text {
          position: absolute;
          top: 50%;
          left: 0rpx;
          transform: translateY(-50%);
          width: 4rpx;
          height: 32rpx;
          background: linear-gradient(180deg, rgba(231, 36, 16, 0.99) 0%, #f17b6f 74%);
        }
        // 上圆角装饰
        &::before {
          position: absolute;
          content: "";
          top: -26rpx;
          right: 0;
          width: 26rpx;
          height: 26rpx;
          background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABoAAAAaCAYAAACpSkzOAAAAAXNSR0IArs4c6QAAAMlJREFUSEvt1SEOAjEQheF/EwQSQQICsQKxAoFE4DjCHmEFEssBcBgk90AgkUiOwTEeGTKbEEICIe0IQpPafn3TTKcgaBVBDn/o60r/VukkdUMSSepFQf0oqIyCJlHQIgpqoqBtFHTKDlkPAdcIaAnss0L29QAXoMoNrYCdffnZIEmVp7FUeSBJQ+AMlO0AS57IkSMwfZySSSEv1wEYP4/iJJCkDmAPvwHub5IU8masgfWrFB+VznugvZ11t+0BMPJDZ8AcsDRv1w3mV0k0AXl2AAAAAABJRU5ErkJggg==");
          background-size: contain;
          background-repeat: no-repeat;
        }

        // 下圆角装饰和右侧连接线
        &::after {
          position: absolute;
          content: "";
          bottom: -26rpx;
          right: 0;
          width: 26rpx;
          height: 26rpx;
          background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABoAAAAaCAYAAACpSkzOAAAAAXNSR0IArs4c6QAAANlJREFUSEvt1jEKwkAQheE/IJhCMKVlCkHLFBbewCvYp7AVPIQWHsF7aGfhBew8hIcYGZiVCK6CZqcQA4GwDPPxZkmyGZFLRLpADnSAnt0DoATGwASYWk2szX09e1vxokBE+sAcWBoerf4KCl1FRJMvgE0sYStQAxwBBxvvQ7pWIe0sIrqPe6BqSq1DDewEDAOWBDJMx3gOe5YMMmwFbPU5NaTv4EVHmBSyVDWw84AK4JocslRHL2jtBdVe0MwLqryg0gsqfg7KXRIl/6gm//E9O6H8R/fxMfAGrshJOEGy/j4AAAAASUVORK5CYII=");
          background-size: contain;
          background-repeat: no-repeat;
          // 同时添加右侧连接线
          box-shadow: 1rpx 0 0 0 #fff;
          z-index: 10;
        }
      }
    }
  }

  // 右侧内容区域
  .searWrap-l {
    width: 556rpx;
    background: #fff;

    // 隐藏滚动条
    ::-webkit-scrollbar {
      display: none;
      width: 0;
      height: 0;
      color: transparent;
    }

    .content {
      padding: 24rpx;
      min-height: 100%;

      // 日期
      .fixed_time {
        font-weight: 400;
        font-size: 24rpx;
        color: #9b9eac;
      }
      // 头部三个卡片
      .three_card {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 16rpx;
        .fixed-item {
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          width: 160rpx;
          height: 100rpx;
          background: linear-gradient(180deg, #fff5e5 0%, #ffffff 50%), #ffffff;
          border-radius: 8rpx;
          border: 1rpx solid #fcdeb3;
          .item_num {
            font-weight: 600;
            font-size: 32rpx;
            color: #525665;
          }
          .item_title {
            font-weight: 400;
            font-size: 24rpx;
            color: #ceab95;
            margin-top: 4rpx;
          }
        }
      }

      // 内容项基础样式
      .content-item {
        margin-bottom: 32rpx;
        border-radius: 12rpx;
        background: #fff;
        overflow: hidden;

        &:last-child {
          margin-bottom: 0;
        }

        // 标题样式
        .title {
          font-size: 32rpx;
          font-weight: 600;
          color: #20263a;
          margin-bottom: 24rpx;
          padding-bottom: 16rpx;
          border-bottom: 1rpx solid #f0f0f0;
        }

        // 内容区域
        .item-content {
          padding: 20rpx 0;
        }
      }

      // 卡片样式
      .card-item {
        margin-top: 40rpx;
        // 卡片标题
        .card-title {
          display: flex;
          justify-content: space-between;
          align-items: center;
          font-weight: 600;
          font-size: 28rpx;
          color: #20263a;
          text:nth-of-type(1) {
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 1;
            overflow: hidden;
            max-width: 350rpx;
          }

          text:nth-child(2) {
            font-weight: 400;
            font-size: 24rpx;
            color: #9b9eac;
          }
        }

        // 卡片内容
        .card-content {
          display: flex;
          flex-wrap: wrap;
          justify-content: space-between;

          .content_item {
            width: 246rpx;
            height: 186rpx;
            box-sizing: border-box;
            background: #f7f7f7;
            border-radius: 8rpx;
            padding: 16rpx;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            align-items: flex-start;
            margin-top: 16rpx;

            .item_tit {
              font-weight: 400;
              font-size: 24rpx;
              color: #20263a;
            }

            .item-count {
              font-weight: 400;
              font-size: 24rpx;
              color: #9b9eac;
            }

            .item-add {
              font-weight: 400;
              font-size: 22rpx;
              color: #f03f2d;
              margin-left: 8rpx;

              &::before {
                content: "+";
              }
            }
          }
        }

        // 卡片底部信息
        .card-footer {
          display: flex;
          justify-content: space-between;
          align-items: center;
          font-size: 24rpx;
          color: #9b9eac;

          .card-tag {
            background: #f0f0f0;
            padding: 4rpx 12rpx;
            border-radius: 12rpx;
            color: #74798c;
          }

          .card-count {
            font-weight: 500;
            color: #e72410;
          }
        }
      }
    }
  }
}
// 产业图谱样式
.industrial {
  .searWrap-r .searL .active {
    text {
      background: linear-gradient(360deg, #c2a5ff 0%, #8441ff 100%) !important;
    }
    color: #7c5ecd !important;
  }
  .searWrap-l {
    .fixed-item {
      background: linear-gradient(180deg, #f3eeff 0%, #ffffff 50%), #ffffff !important;
      border: 1rpx solid #e4d1ff !important;

      .item_title {
        color: #ae9ed7 !important;
      }
    }
    .content_item {
      height: 264rpx !important;
      background: #f7f4ff !important;

      .item-add {
        color: #7c5ecd !important;
      }

      .lock-img {
        position: absolute;
        right: 0;
        top: 0;
        width: 40rpx;
        height: 40rpx;
        opacity: 0;
      }
      // 产业图谱有锁的情况
      &.lock {
        position: relative;
        background: #f7f7f7 !important;
        .lock-img {
          opacity: 1;
        }
      }
    }
    .bottom_txt {
      display: flex;
      align-items: center;
      margin-top: 22rpx;

      .img {
        width: 44rpx;
        height: 36rpx;
        margin-right: 4rpx;
      }
      font-weight: 400;
      font-size: 22rpx;
      color: #7c5ecd;
    }
  }
}
