// 绘制相关的混入逻辑

// 导入常量配置
const {DRAW_CONSTANTS} = require('../config/constants.js');

const drawMixin = {
  methods: {
    // 绘制圆角矩形（优化版本）
    drawRoundedRect(x, y, width, height, radius, fillColor) {
      if (!this._validateDrawParams(x, y, width, height, fillColor)) {
        return;
      }

      const ctx = this.ctx;
      if (!ctx) {
        console.warn('Canvas上下文不存在');
        return;
      }

      try {
        if (this.isLegacyCanvas) {
          this._drawLegacyRect(ctx, x, y, width, height, fillColor);
        } else {
          this._drawModernRoundedRect(
            ctx,
            x,
            y,
            width,
            height,
            radius,
            fillColor
          );
        }
      } catch (error) {
        console.error('绘制圆角矩形失败:', error);
        // 降级到普通矩形
        this._drawFallbackRect(ctx, x, y, width, height, fillColor);
      }
    },

    // 验证绘制参数
    _validateDrawParams(x, y, width, height, fillColor) {
      if (
        typeof x !== 'number' ||
        typeof y !== 'number' ||
        typeof width !== 'number' ||
        typeof height !== 'number'
      ) {
        console.warn('绘制参数必须是数字:', {x, y, width, height});
        return false;
      }

      if (width <= 0 || height <= 0) {
        console.warn('宽度和高度必须大于0:', {width, height});
        return false;
      }

      if (!fillColor) {
        console.warn('填充颜色不能为空');
        return false;
      }

      return true;
    },

    // 绘制旧版矩形
    _drawLegacyRect(ctx, x, y, width, height, fillColor) {
      ctx.setFillStyle(fillColor);
      ctx.fillRect(x, y, width, height);
    },

    // 绘制现代圆角矩形
    _drawModernRoundedRect(ctx, x, y, width, height, radius, fillColor) {
      const safeRadius = Math.min(radius || 0, width / 2, height / 2);

      ctx.fillStyle = fillColor;
      ctx.beginPath();

      if (safeRadius <= 0) {
        // 如果半径为0，绘制普通矩形
        ctx.rect(x, y, width, height);
      } else {
        // 绘制圆角矩形
        ctx.moveTo(x + safeRadius, y);
        ctx.lineTo(x + width - safeRadius, y);
        ctx.quadraticCurveTo(x + width, y, x + width, y + safeRadius);
        ctx.lineTo(x + width, y + height - safeRadius);
        ctx.quadraticCurveTo(
          x + width,
          y + height,
          x + width - safeRadius,
          y + height
        );
        ctx.lineTo(x + safeRadius, y + height);
        ctx.quadraticCurveTo(x, y + height, x, y + height - safeRadius);
        ctx.lineTo(x, y + safeRadius);
        ctx.quadraticCurveTo(x, y, x + safeRadius, y);
      }

      ctx.closePath();
      ctx.fill();
    },

    // 降级绘制普通矩形
    _drawFallbackRect(ctx, x, y, width, height, fillColor) {
      try {
        if (this.isLegacyCanvas) {
          ctx.setFillStyle(fillColor);
          ctx.fillRect(x, y, width, height);
        } else {
          ctx.fillStyle = fillColor;
          ctx.fillRect(x, y, width, height);
        }
      } catch (error) {
        console.error('降级绘制矩形也失败:', error);
      }
    },

    // 绘制箭头（优化版本）
    drawArrow(x, y, direction = DRAW_CONSTANTS.DIRECTIONS.RIGHT) {
      if (!this._validateArrowParams(x, y, direction)) {
        return;
      }

      const ctx = this.ctx;
      if (!ctx) {
        console.warn('Canvas上下文不存在');
        return;
      }

      try {
        const size = this._getArrowSize();
        const points = this._getArrowPoints(x, y, direction, size);

        this._drawArrowShape(ctx, points);
      } catch (error) {
        console.error('绘制箭头失败:', error);
      }
    },

    // 验证箭头参数
    _validateArrowParams(x, y, direction) {
      if (typeof x !== 'number' || typeof y !== 'number') {
        console.warn('箭头坐标必须是数字:', {x, y});
        return false;
      }

      const validDirections = Object.values(DRAW_CONSTANTS.DIRECTIONS);
      if (!validDirections.includes(direction)) {
        console.warn('无效的箭头方向:', direction);
        return false;
      }

      return true;
    },

    // 获取箭头大小
    _getArrowSize() {
      return this.rpxToPx
        ? this.rpxToPx(DRAW_CONSTANTS.ARROW_SIZE_RPX)
        : DRAW_CONSTANTS.DEFAULT_ARROW_SIZE;
    },

    // 获取箭头顶点坐标
    _getArrowPoints(x, y, direction, size) {
      const points = {x1: x, y1: y, x2: x, y2: y, x3: x, y3: y};

      switch (direction) {
        case DRAW_CONSTANTS.DIRECTIONS.DOWN:
          points.x2 = x - size;
          points.y2 = y - size;
          points.x3 = x + size;
          points.y3 = y - size;
          break;
        case DRAW_CONSTANTS.DIRECTIONS.RIGHT:
          points.x2 = x - size;
          points.y2 = y - size;
          points.x3 = x - size;
          points.y3 = y + size;
          break;
        case DRAW_CONSTANTS.DIRECTIONS.LEFT:
          points.x2 = x + size;
          points.y2 = y - size;
          points.x3 = x + size;
          points.y3 = y + size;
          break;
        case DRAW_CONSTANTS.DIRECTIONS.UP:
          points.x2 = x - size;
          points.y2 = y + size;
          points.x3 = x + size;
          points.y3 = y + size;
          break;
      }

      return points;
    },

    // 绘制箭头形状
    _drawArrowShape(ctx, points) {
      if (this.isLegacyCanvas) {
        ctx.setFillStyle(DRAW_CONSTANTS.ARROW_COLOR);
      } else {
        ctx.fillStyle = DRAW_CONSTANTS.ARROW_COLOR;
      }

      ctx.beginPath();
      ctx.moveTo(points.x1, points.y1);
      ctx.lineTo(points.x2, points.y2);
      ctx.lineTo(points.x3, points.y3);
      ctx.closePath();
      ctx.fill();
    }
  }
};

module.exports = {drawMixin};
