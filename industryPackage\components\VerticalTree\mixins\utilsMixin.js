// 工具函数相关的混入逻辑

// 导入常量配置
const {UTILS_CONSTANTS} = require('../config/constants.js');

const utilsMixin = {
  data: {
    // 缓存系统信息，避免重复调用
    _systemInfo: null,
    _rpxRatio: null
  },

  methods: {
    // 检查API可用性
    checkApiAvailability() {
      if (!this._apiAvailabilityCache) {
        this._apiAvailabilityCache = {
          hasWindowInfo: typeof wx.getWindowInfo === 'function',
          hasDeviceInfo: typeof wx.getDeviceInfo === 'function',
          hasAppBaseInfo: typeof wx.getAppBaseInfo === 'function',
          hasSystemInfoSync: typeof wx.getSystemInfoSync === 'function'
        };
      }
      return this._apiAvailabilityCache;
    },

    // 初始化系统信息（只调用一次）
    initSystemInfo() {
      if (this.data._systemInfo) return;

      const apiAvailability = this.checkApiAvailability();
      let screenWidth = UTILS_CONSTANTS.DEFAULT_SCREEN_WIDTH;
      let initMethod = 'default';

      try {
        if (apiAvailability.hasWindowInfo) {
          const windowInfo = wx.getWindowInfo();
          screenWidth = windowInfo.screenWidth;
          initMethod = 'getWindowInfo';
          console.log('使用wx.getWindowInfo获取屏幕信息');
        } else if (apiAvailability.hasSystemInfoSync) {
          const systemInfo = wx.getSystemInfoSync();
          screenWidth = systemInfo.screenWidth;
          initMethod = 'getSystemInfoSync';
          console.warn('使用已废弃的wx.getSystemInfoSync，建议升级微信版本');
        } else {
          throw new Error('没有可用的系统信息API');
        }

        const rpxRatio = screenWidth / UTILS_CONSTANTS.RPX_BASE;

        this.setData({
          _systemInfo: {screenWidth, initMethod},
          _rpxRatio: rpxRatio
        });

        // console.log('系统信息初始化成功:', {
        //   screenWidth,
        //   rpxRatio,
        //   method: initMethod
        // });
      } catch (error) {
        console.warn('获取系统信息失败，使用默认值:', error);
        this._handleSystemInfoError(screenWidth);
      }
    },

    // 处理系统信息获取失败
    _handleSystemInfoError(screenWidth) {
      const rpxRatio = screenWidth / UTILS_CONSTANTS.RPX_BASE;
      this.setData({
        _systemInfo: {screenWidth, initMethod: 'fallback'},
        _rpxRatio: rpxRatio
      });
      console.log('使用默认屏幕宽度:', screenWidth);
    },

    // 将rpx转换为px（优化版本）
    rpxToPx(rpx) {
      if (typeof rpx !== 'number') {
        console.warn('rpxToPx: 参数必须是数字', rpx);
        return 0;
      }

      if (!this.data._rpxRatio) {
        this.initSystemInfo();
      }

      return Math.round(rpx * this.data._rpxRatio);
    },

    // 批量转换rpx到px（用于样式对象）
    convertRpxObject(rpxObj) {
      if (!rpxObj || typeof rpxObj !== 'object') {
        console.warn('convertRpxObject: 参数必须是对象', rpxObj);
        return {};
      }

      const result = {};
      for (const key in rpxObj) {
        if (rpxObj.hasOwnProperty(key)) {
          result[key] =
            typeof rpxObj[key] === 'number'
              ? this.rpxToPx(rpxObj[key])
              : rpxObj[key];
        }
      }
      return result;
    },
    // 计算文字宽度（带缓存优化）
    calculateTextWidth(text, fontSize) {
      if (!text) return 0;

      // 初始化缓存（在组件实例上）
      if (!this._textWidthCache) {
        this._textWidthCache = new Map();
      }

      const cacheKey = `${text}_${fontSize}`;
      if (this._textWidthCache.has(cacheKey)) {
        return this._textWidthCache.get(cacheKey);
      }

      let width = 0;
      for (let i = 0; i < text.length; i++) {
        const char = text.charAt(i);
        if (this._isChineseChar(char)) {
          width += fontSize * UTILS_CONSTANTS.CHINESE_CHAR_RATIO;
        } else {
          width += fontSize * UTILS_CONSTANTS.ENGLISH_CHAR_RATIO;
        }
      }

      this._textWidthCache.set(cacheKey, width);
      return width;
    },

    // 检查是否为中文字符
    _isChineseChar(char) {
      return /[\u4e00-\u9fa5]/.test(char);
    },

    // 计算节点宽度（带缓存优化）
    calculateNodeWidth(text, style, count = null, isHorizontalLayout = false) {
      if (style.width) return style.width;

      // 初始化缓存（在组件实例上）
      if (!this._nodeWidthCache) {
        this._nodeWidthCache = new Map();
      }

      const cacheKey = `${text}_${JSON.stringify(
        style
      )}_${count}_${isHorizontalLayout}`;
      if (this._nodeWidthCache.has(cacheKey)) {
        return this._nodeWidthCache.get(cacheKey);
      }

      let width;
      if (count !== null && !isHorizontalLayout) {
        width = this._calculateFirstLevelNodeWidth(text, style, count);
      } else if (count !== null && isHorizontalLayout) {
        width = this._calculateHorizontalNodeWidth(text, style, count);
      } else {
        width = this._calculateBasicNodeWidth(text, style);
      }

      this._nodeWidthCache.set(cacheKey, width);
      return width;
    },

    // 计算第一级节点宽度
    _calculateFirstLevelNodeWidth(text, style, count) {
      const nameWidth = this.calculateTextWidth(text, style.fontSize);
      const countWidth = this.calculateTextWidth(
        count.toString(),
        style.countFontSize || style.fontSize
      );
      const maxTextWidth = Math.max(nameWidth, countWidth);
      const padding = (style.padding || 0) * 2;
      const iconSpace = style.iconSize || 0;

      return Math.max(
        style.minWidth || UTILS_CONSTANTS.MIN_NODE_WIDTH,
        maxTextWidth + padding + iconSpace
      );
    },

    // 计算水平布局节点宽度
    _calculateHorizontalNodeWidth(text, style, count) {
      const nameWidth = this.calculateTextWidth(text, style.fontSize);
      const countWidth = this.calculateTextWidth(
        count.toString(),
        style.countFontSize || style.fontSize
      );
      const textGap = style.textGap || 0;
      const totalTextWidth = nameWidth + textGap + countWidth;
      const padding = (style.paddingX || 0) * 2;

      return Math.max(
        style.minWidth || UTILS_CONSTANTS.MIN_NODE_WIDTH,
        totalTextWidth + padding
      );
    },

    // 计算基础节点宽度
    _calculateBasicNodeWidth(text, style) {
      const textWidth = this.calculateTextWidth(text, style.fontSize);
      return Math.max(
        style.minWidth || UTILS_CONSTANTS.MIN_NODE_WIDTH,
        textWidth + (style.paddingX || 0) * 2
      );
    },

    // 计算节点高度（带缓存优化）
    calculateNodeHeight(
      text,
      style,
      isVerticalText = false,
      isFirstLevel = false,
      isHorizontalLayout = false
    ) {
      if (style.height) return style.height;

      // 初始化缓存（在组件实例上）
      if (!this._nodeHeightCache) {
        this._nodeHeightCache = new Map();
      }

      const cacheKey = `${text}_${JSON.stringify(
        style
      )}_${isVerticalText}_${isFirstLevel}_${isHorizontalLayout}`;
      if (this._nodeHeightCache.has(cacheKey)) {
        return this._nodeHeightCache.get(cacheKey);
      }

      let height;
      if (isVerticalText) {
        height = this._calculateVerticalTextHeight(text, style);
      } else if (isFirstLevel) {
        height = this._calculateFirstLevelHeight(style);
      } else if (isHorizontalLayout) {
        height = this._calculateHorizontalLayoutHeight(style);
      } else {
        height = this._calculateBasicHeight(style);
      }

      this._nodeHeightCache.set(cacheKey, height);
      return height;
    },

    // 计算垂直文字高度
    _calculateVerticalTextHeight(text, style) {
      const lineHeight = style.fontSize * UTILS_CONSTANTS.LINE_HEIGHT_RATIO;
      const baseHeight = text.length * lineHeight + (style.paddingY || 0) * 2;
      return Math.max(
        style.minHeight || UTILS_CONSTANTS.MIN_NODE_HEIGHT,
        baseHeight
      );
    },

    // 计算第一级节点高度
    _calculateFirstLevelHeight(style) {
      const nameHeight = style.fontSize;
      const countHeight = style.countFontSize || style.fontSize;
      const textGap = style.textGap || 0;
      const padding = (style.padding || 0) * 2;
      const baseHeight = nameHeight + textGap + countHeight + padding;
      return Math.max(
        style.minHeight || UTILS_CONSTANTS.MIN_NODE_HEIGHT,
        baseHeight
      );
    },

    // 计算水平布局高度
    _calculateHorizontalLayoutHeight(style) {
      const nameHeight = style.fontSize;
      const countHeight = style.countFontSize || style.fontSize;
      const maxFontHeight = Math.max(nameHeight, countHeight);
      const padding = (style.paddingY || 0) * 2;
      const baseHeight = maxFontHeight + padding;
      return Math.max(
        style.minHeight || UTILS_CONSTANTS.MIN_NODE_HEIGHT,
        baseHeight
      );
    },

    // 计算基础高度
    _calculateBasicHeight(style) {
      const baseHeight = style.fontSize + (style.paddingY || 0) * 2;
      return Math.max(
        style.minHeight || UTILS_CONSTANTS.MIN_NODE_HEIGHT,
        baseHeight
      );
    },

    // 数据预处理：全部展开，不折叠（优化版本）
    processTreeData(rawData) {
      if (!this._validateTreeData(rawData)) return [];

      try {
        return rawData.map(rootNode => this._processRootNode(rootNode));
      } catch (error) {
        console.error('处理树形数据失败:', error);
        return [];
      }
    },

    // 验证树形数据
    _validateTreeData(data) {
      if (!data || !Array.isArray(data) || data.length === 0) {
        console.warn('树形数据无效:', data);
        return false;
      }
      return true;
    },

    // 处理根节点
    _processRootNode(rootNode) {
      if (!rootNode || typeof rootNode !== 'object') {
        console.warn('根节点数据无效:', rootNode);
        return {name: '未知节点', count: 0, children: []};
      }

      const children = rootNode.children || [];
      return {
        name: rootNode.name || '未知节点',
        count: this._validateCount(rootNode.count),
        children: this.processAllNodes(children, 1), // 根节点的子节点从深度1开始
        // 使用基于深度的默认展开状态逻辑
        isExpanded: this._getDefaultExpandedStateByDepth(
          rootNode,
          children.length > 0,
          0 // 根节点深度为0
        ),
        hasChildren: children.length > 0,
        expertchain_code: rootNode.expertchain_code
      };
    },

    // 获取默认展开状态
    _getDefaultExpandedState(node, hasChildren) {
      // 如果节点已经有明确的展开状态设置，使用原有状态
      if (node.isExpanded !== undefined) {
        return node.isExpanded;
      }

      // 如果没有子节点，不需要展开状态
      if (!hasChildren) {
        return false;
      }

      // 通过分析节点结构来判断层级
      // 默认状态：只显示根节点和第一级节点，隐藏第二级及以下
      if (node.children && node.children.length > 0) {
        // 检查子节点是否还有子节点
        const hasGrandChildren = node.children.some(
          child => child.children && child.children.length > 0
        );

        // 检查孙子节点是否还有子节点
        const hasGreatGrandChildren = node.children.some(
          child =>
            child.children &&
            child.children.some(
              grandChild =>
                grandChild.children && grandChild.children.length > 0
            )
        );

        if (hasGreatGrandChildren) {
          // 有曾孙节点，说明这是根节点，展开显示第一级节点
          return true;
        } else if (hasGrandChildren) {
          // 有孙子节点但没有曾孙节点，说明这是第一级节点，收起隐藏第二级节点
          return false;
        } else {
          // 只有子节点，说明这是第二级节点，收起隐藏第三级节点
          return false;
        }
      }

      // 叶子节点，不需要展开状态
      return false;
    },

    // 基于深度的默认展开状态设置（更准确的方法）
    _getDefaultExpandedStateByDepth(node, hasChildren, depth) {
      // 如果节点已经有明确的展开状态设置，使用原有状态
      if (node.isExpanded !== undefined) {
        return node.isExpanded;
      }

      // 如果没有子节点，不需要展开状态
      if (!hasChildren) {
        return false;
      }

      // 默认状态相当于执行一次"全部收起"
      // depth 0: 根节点 - 展开（显示第一级节点）
      // depth 1: 第一级节点 - 收起（隐藏第二级节点）
      // depth 2+: 第二级及以下节点 - 收起（隐藏）

      return depth === 0; // 只有根节点展开
    },

    // 验证count值
    _validateCount(count) {
      const numCount = Number(count);
      return isNaN(numCount) || numCount < 0 ? 0 : numCount;
    },

    // 处理所有节点：全部展开（优化版本）
    processAllNodes(nodes, depth = 0) {
      if (!nodes || !Array.isArray(nodes) || nodes.length === 0) return [];

      try {
        return nodes.map(node => this._processNode(node, depth));
      } catch (error) {
        console.error('处理节点失败:', error);
        return [];
      }
    },

    // 处理单个节点
    _processNode(node, depth = 0) {
      if (!node || typeof node !== 'object') {
        console.warn('节点数据无效:', node);
        return {name: '未知节点', count: 0, children: []};
      }

      const children = node.children || [];
      return {
        name: node.name || '未知节点',
        count: this._validateCount(node.count),
        children: this.processAllNodes(children, depth + 1),
        // 设置默认展开状态：根据节点深度决定
        isExpanded: this._getDefaultExpandedStateByDepth(
          node,
          children.length > 0,
          depth
        ),
        hasChildren: children.length > 0,
        depth: depth, // 添加深度信息用于调试
        expertchain_code: node.expertchain_code
      };
    },

    // 清理缓存（性能优化：防止内存泄漏）
    clearCache() {
      if (this._textWidthCache) {
        this._textWidthCache.clear();
      }
      if (this._nodeWidthCache) {
        this._nodeWidthCache.clear();
      }
      if (this._nodeHeightCache) {
        this._nodeHeightCache.clear();
      }
    },

    // 获取缓存统计信息
    getCacheStats() {
      return {
        textWidthCacheSize: this._textWidthCache
          ? this._textWidthCache.size
          : 0,
        nodeWidthCacheSize: this._nodeWidthCache
          ? this._nodeWidthCache.size
          : 0,
        nodeHeightCacheSize: this._nodeHeightCache
          ? this._nodeHeightCache.size
          : 0
      };
    }
  }
};

module.exports = {utilsMixin};
