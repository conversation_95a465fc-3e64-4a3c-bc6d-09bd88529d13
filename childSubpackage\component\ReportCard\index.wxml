<!-- 卡片列表 -->
<view class="report-list">
  <view
    class="report-item"
    wx:for="{{reportList}}"
    wx:key="index"
    bindtap="onReportClick"
    data-index="{{index}}"
    hover-class="item-hover"
    hover-stay-time="150"
    hover-start-time="0"
  >
    <!-- 左边图片 -->
    <view class="image-container">
      <image
        class="report-image"
        src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/cyt_v2/image/industry/i_yb_ico.png"
        mode="aspectFill"
      ></image>
      <!-- 图片上面文字 -->
      <view class="txt">{{item.imgTit}}</view>
    </view>

    <!-- 右边内容 -->
    <view class="content">
      <!-- 标题 -->
      <view class="title">{{item.title}}</view>

      <!-- 信息行：页数、大小、标签 -->
      <view class="info-row">
        <view class="file-info">{{item.page_num}}页 {{item.size}}</view>
        <view class="tags">
          <view
            class="tag"
            wx:for="{{item.tags}}"
            wx:key="tagIndex"
            wx:for-item="tag"
          >
            {{tag}}
          </view>
        </view>
      </view>

      <!-- 底部：机构和时间 -->
      <view class="bottom-row">
        <view class="left">
          <view class="organization">{{item.organization}}</view>
          <view class="date">{{item.date}}</view>
        </view>
        <view class="right">
          <image
            src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/cyt_v2/image/industry/i_share.png"
          ></image>
        </view>
      </view>
    </view>
  </view>
</view>
