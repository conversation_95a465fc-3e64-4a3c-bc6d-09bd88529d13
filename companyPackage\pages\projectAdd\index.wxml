<import src="/template/null/null"></import>
<import src="/template/more/more"></import>
<import src="/template/loading/index"></import>
<wxs module="tool">
  var isShowCont = function (ent_name, isHeightParams) {
    if (isHeightParams) { //高级搜索过来直接显示
      return false
    } else {
      if (ent_name.length == '' && ent_name.length <= 0) { //不是高级搜索的情况-不管登录 
        return true
      } else {
        return false
      }
    }
  }
  var isShowHis = function (ent_name, isLogin, isHeightParams) {
    if (isHeightParams) { //高级搜索过来的--不显示 
      return true
    } else {
      if (ent_name.length > 0 || !isLogin) { //没有登录++有内容  也不显示
        return true
      } else {
        return false
      }
    }
  }
  module.exports = {
    isShowCont: isShowCont,
    isShowHis: isShowHis
  }
</wxs>
<view class="pages">
  <!-- input -->
  <view class='searchs'>
    <view class="s-input">
      <view class="s-input-img">
        <image src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/search.png" mode="aspectFit"></image>
      </view>
      <view class="s-input-item">
        <input class="s-input-item-i" type="text" placeholder='请输入项目名称' placeholder-class='placeholder' bindblur='onBlur' value="{{ents_name}}" focus="{{inputShowed}}" bindinput="onInput" bindconfirm='onConfirm' confirm-type='search' />
        <view hidden="{{ent_name.length <= 0}}" catchtap="onClear" class="input-clear">
          <view class="clearIcon"></view>
        </view>
      </view>
    </view>
    <view class="search-cancel" bindtap="goBack" bindtap="goBack">取消</view>
  </view>
  <view>
    <!-- 筛选条件 -->
    <DropDownMenu height="{{filtrateHeight}}" dropDownMenuTitle="{{dropDownMenuTitle}}" class="drop-menu" bindsubmit="onFlitter" heightParams="{{heightParams}}" excludeFields="{{['area_code_list', 'industry_code_list','ent_name']}}" bindvip="vipPop" />
    <block>
      <!-- 卡片 -->
      <view wx:if="{{!bazaarIsNull}}">
        <!-- padding-bottom: 20rpx; -->
        <scroll-view bindrefresherrefresh="bazaarRefresher" refresher-triggered="{{bazaarIsTriggered}}" refresher-enabled bindscrolltolower="bazaarloadMore" scroll-y style="height: {{cardHeight}}px;background: #f7f7f7; ">
          <view class="bus-card">
            <block>
              <block wx:if="{{bazaarlist.length>0}}">
                <block wx:for="{{bazaarlist}}" wx:key="index">
                  <Card bindcardFun='onCard' bindhandleTit="handleTit" obj="{{item}}" bindtap="goto" data-item="{{item}}" />
                </block>
              </block>
              <!-- <view wx:else style="height: {{cardHeight}}px;">
                <template is="load"></template>
              </view> -->
            </block>
            <!-- 开通vip -->
            <view class="vip" wx:if="{{permission=='普通VIP'&&bazaarlist.length>=10}}">
              <vipOccupancy bindsubmit="vipPop"></vipOccupancy>
            </view>
            <view wx:elif="{{bazaarlist.length&& bazaarlist.length<count}}" style="width: 100%;">
              <template is='more' data="{{hasData:bazaarHasData}}"></template>
            </view>
          </view>
        </scroll-view>
      </view>
      <!--暂无数据 -->
      <view wx:if="{{bazaarIsNull}}" style="width: 100%;height: 600rpx;">
        <template is='null'></template>
      </view>
    </block>
  </view>
</view>


<Contact visible='{{showContact}}' entId="{{activeEntId}}"></Contact>
<!-- 地址弹窗 -->
<dialog visible='{{showAddress}}' title="地址" isShowConfirm='{{false}}' showFooter="{{false}}">
  <view class="dialog-con">
    <view style="padding: 0 50rpx;">
      <map id="map" longitude="{{location.lon}}" latitude="{{location.lat}}" markers="{{addmarkers}}" scale="{{11}}" style="width: 100%; height: 306rpx;">
      </map>
    </view>
    <view style="margin: 32rpx 0;font-size: 28rpx;">{{locationTxt}}</view>
    <view bindtap="goMap" class="map">
      导航
    </view>
    <view class="cancel" bindtap="onCloseAddress">
      取消
    </view>
  </view>
</dialog>
<!-- vip弹窗 -->
<VipPop visible="{{vipVisible}}"></VipPop>