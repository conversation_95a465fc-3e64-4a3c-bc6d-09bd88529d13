import {getHeight} from '../../../../utils/height';
import {chain} from '../../../../service/api';
const tabs = [
  {title: '上游', name: 'upstream', list: []},
  {title: '中游', name: 'midstream', list: []},
  {title: '下游', name: 'downstream', list: []}
];
const app = getApp();
Page({
  data: {
    tabs,
    chainTypeCode: null
  },
  onLoad(options) {
    let {chainTypeName = '机器人', industry_code} = options;
    wx.setNavigationBarTitle({
      title: chainTypeName
    });
    this.setData(
      {
        chainTypeName,
        chainTypeCode: industry_code
      },
      () => this.init()
    );
  },
  onReady() {
    this.gethegiht();
  },
  onShow() {},
  async init() {
    //初始化得到数据
    wx.showLoading({
      title: '正在加载',
      mask: true
    });
    let {chainTypeCode, tabs} = this.data;
    const [err, res] = await app.to(chain.getChains(chainTypeCode));
    if (err) {
      //报错了-提示报错信息 显示缺省页面
      return;
    }
    res.forEach(item => {
      if (item.belong_to == '1') {
        tabs[0].list.push(item);
      }
      if (item.belong_to == '2') {
        tabs[1].list.push(item);
      }
      if (item.belong_to == '3') {
        tabs[2].list.push(item);
      }
    });
    // console.log(tabs)
    // 将第一级push进去
    tabs.map(item => {
      return item.list.map(i => {
        i.children.unshift({
          chain_code: i.chain_code,
          name: '不限',
          parent_name: i.name
        });
        return i;
      });
    });
    this.setData({tabs});
    wx.hideLoading();
  },
  gethegiht() {
    const that = this;
    getHeight(that, ['.navH'], data => {
      let {screeHeight, res} = data;
      let scrollH = screeHeight - res[0]?.top;
      this.setData({scrollH});
    });
  },
  getItem(e) {
    //点击单个标签
    let {item} = e.currentTarget.dataset;
    let {name, chain_code, parent_name} = item;
    let url = `/industryPackage/pages/IndustryListMasonry/index?chain_name=${name}&&chain_code=${chain_code}`;
    if (parent_name) {
      //说明点击的是不限，就传第一级
      url = `/industryPackage/pages/IndustryListMasonry/index?chain_name=${parent_name}&&chain_code=${chain_code}`;
    }
    app.route(this, url);
  },
  setHighlight(chain_code) {
    let tabs = this.data.tabs;
    tabs.forEach(item => {
      return item.list.forEach(i => {
        return i.children.forEach(itm => {
          itm.active = false;
          if (itm.chain_code === chain_code) {
            itm.active = true;
          }
          return itm;
        });
      });
    });
    this.setData({tabs: tabs});
  }
});
