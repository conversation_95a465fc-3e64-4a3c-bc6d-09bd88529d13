<view class="tabs-container {{currentCode === 'chainMap' ? 'chainMap' : ''}}">
  <view class="tabs-wrapper">
    <view
      class="tab-item {{currentCode === item.chain_code ? 'active' : ''}}"
      wx:for="{{customTabList.length ? customTabList : tabList}}"
      wx:key="chain_code"
      data-index="{{index}}"
      bindtap="onTabClick"
    >
      <!-- 背景图标  -->
      <image
        src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/cyt_v2/image/industry/i_tabvip.png"
        class="vip"
        wx:if="{{currentCode === item.chain_code && item.chain_code != 'chainMap' }}"
      ></image>
      <image
        src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/cyt_v2/image/industry/i_tabzhuanshi.png"
        class="vip"
        wx:if="{{currentCode === item.chain_code && item.chain_code === 'chainMap' }}"
      ></image>
      {{item.chain_name}}
    </view>
  </view>
</view>
