/* childSubpackage/pages/projectManagement/index.scss */
@import '../../../template/more/more.scss';
@import '../../../template/null/null.scss';
.project-manage {
  width: 100%;
  height: 100vh;
  background-color: #F7F7F7;
}
.head {
  height: 80rpx;
}
.list {
  padding-bottom: 20rpx;
  padding-top: 1rpx;
}
.footer {
  width: 100%;
  height: 168rpx;
  background-color: #fff;
  position: fixed;
  bottom: 0;
  left: 0;
  display: flex;
  justify-content: space-between;
  padding: 10rpx 24rpx 0;
}
.footer .btn {
  flex: 1;
  max-width: 340rpx;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  font-size: 32rpx;
  color: #20263A;
  font-weight: 400;
  background: linear-gradient(90deg, #EEEEEE 0%, #F5F5F5 100%);
  border-radius: 8rpx;
}
.footer .btn.select-handover {
  margin: 0 18rpx;
  background: linear-gradient(90deg, #F56E60 0%, #E72410 100%);
  color: #fff;
}
.footer .btn.complete, 
.footer .btn.complete-del {
  background: linear-gradient(90deg, #F56E60 0%, #E72410 100%);
  color: #fff;
}

/* dialog文字样式 */
.dialog-con {
  font-size: 28rpx;
  font-family: PingFang SC, PingFang SC-Regular;
  font-weight: 400;
  text-align: CENTER;
  color: #74798c;
}

.dialog-con .map {
  position: relative;
  width: 100%;
  height: 112rpx;
  line-height: 112rpx;
  text-align: center;
  font-size: 34rpx;
  font-family: PingFang SC-Medium, PingFang SC;
  font-weight: 500;
  color: #E72410;
}

.weui-dialog__title {
  font-weight: 500 !important;
  color: #000000 !important;
  line-height: 40rpx;
  font-size: 34rpx !important;
}

.light_bd {
  padding: 0 !important;
}

.dialog-con .map::after {
  content: " ";
  width: 200%;
  height: 1rpx;
  background: #E5E5E5;
  position: absolute;
  top: 0;
  left: 0%;
  transform: scaleY(0.5);
}

.dialog-con .cancel {
  position: relative;
  height: 112rpx;
  line-height: 112rpx;
  text-align: center;
  font-size: 34rpx;
  font-family: PingFang SC-Medium, PingFang SC;
  font-weight: 500;
  color: #000000;
}

.dialog-con .cancel::after {
  content: " ";
  width: 200%;
  height: 1rpx;
  background: #E5E5E5;
  position: absolute;
  top: 0;
  left: 0%;
  transform: scaleY(0.5);
}