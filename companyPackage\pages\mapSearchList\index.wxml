<import src="/template/null/null"></import>
<import src="/template/more/more"></import>
<import src="/template/loading/index"></import>
<view class="pages">
  <!-- input -->
  <view class="searchs">
    <view class="s-input">
      <view class="s-input-img">
        <image
          src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/search.png"
          mode="aspectFit"
        ></image>
      </view>
      <view class="s-input-item">
        <input
          class="s-input-item-i"
          type="text"
          placeholder="请输入企业名称或地址"
          placeholder-class="placeholder"
          bindblur="onBlur"
          value="{{ent_name}}"
          focus="{{inputShowed}}"
          bindinput="onInput"
          bindconfirm="onConfirm"
          confirm-type="search"
        />
        <view
          hidden="{{ent_name.length <= 0}}"
          catchtap="onClear"
          class="input-clear"
        >
          <view class="clearIcon"></view>
        </view>
      </view>
    </view>
    <view class="search-cancel" bindtap="goBack" bindtap="goBack">取消</view>
  </view>
  <!-- 历史记录 -->
  <view class="history_wrap" wx:if="{{isLogin && !ent_name}}">
    <!-- 最近搜索 -->
    <block wx:if="{{historyList.length>0}}">
      <view class="page__autofit search_a">
        <view class="his_title">
          <text class="his_title_l">最近搜索</text>
          <view class="his_title_icon" bindtap="handleIcon" data-index="a">
            <image
              src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/delet.png"
              mode="aspectFit"
            ></image>
          </view>
        </view>
        <view class="his_content">
          <!-- 内容 -->
          <view class="text-box">
            <block wx:for="{{historyList}}" wx:key="index">
              <view
                class="his_content_item"
                bindtap="historyTap"
                data-item="{{item}}"
              >
                {{item}}
              </view>
            </block>
          </view>
        </view>
      </view>
    </block>
  </view>
  <!-- 产业链列表 -->
  <scroll-view
    scroll-y
    wx:if="{{ ent_name }}"
    style="height:{{searchScrollHeight}}px;"
    class="chain-wrap"
  >
    <view
      class="chain-item"
      wx:for="{{chainList}}"
      wx:key="item"
      data-item="{{item}}"
      bindtap="onIndustrClick"
    >
      <!-- 图标 -->
      <image
        src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/cyt_v2/image/companyPackageImg/address.png"
        class="img"
      ></image>
      <!-- 文字 -->
      <view>
        <view class="top">
          <text
            class="{{ v.type === 'HIGH'?'text-high':'' }}"
            wx:for="{{item.label}}"
            wx:key="text"
            wx:for-item="v"
            >{{ v.text }}</text
          ></view
        >
        <view class="bottom"> {{item.address}}</view>
      </view>
    </view>
    <block wx:if="{{!chainList.length}}">
      <view class="no_data">
        <template is="null" />
      </view>
    </block>
  </scroll-view>
</view>
