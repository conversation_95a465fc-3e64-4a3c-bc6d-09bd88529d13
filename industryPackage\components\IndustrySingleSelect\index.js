import {singleChainNodeNumTreeApi} from '../../../service/industryApi';

// Tab 选项配置 - 根据 link_type 分类
const TAB_OPTIONS = [
  {
    code: 'upstream',
    name: '上游',
    link_type: 1
  },
  {
    code: 'midstream',
    name: '中游',
    link_type: 2
  },
  {
    code: 'downstream',
    name: '下游',
    link_type: 3
  }
];

Component({
  properties: {
    visible: {
      type: Boolean,
      value: false
    },
    position: {
      type: String,
      value: 'top'
    },
    startDistance: {
      type: String,
      value: '0px'
    },
    defaultCode: {
      type: null,
      value: ''
    },
    top: {
      type: Number,
      value: 0
    },
    zIndex: {
      type: Number,
      value: 10
    },
    // 产业链代码，用于获取真实数据
    chain_code: {
      type: String,
      value: '',
      observer(newVal) {
        if (newVal && this.data.visible) {
          this.loadRealData(newVal);
        }
      }
    }
  },

  data: {
    // 兼容原有的两级结构（向后兼容，仅用于对外接口）
    parentList: [],
    activeChildList: [],
    selectedPath: {
      parent: null,
      child: null
    },
    finalSelection: null,
    readyToShow: false,
    tabs: TAB_OPTIONS,
    activeTabIndex: 0, // 默认选中第一个tab（上游）
    // 真实数据存储
    realData: null, // 从API获取的原始数据
    processedData: {
      upstream: [], // 上游数据
      midstream: [], // 中游数据
      downstream: [] // 下游数据
    },
    // 状态记忆：区分已确认的选择和临时选择
    confirmedSelection: {
      tabIndex: 0, // 已确认选择的Tab索引
      selectedCode: null, // 已确认选择的code
      hasConfirmed: false // 是否有已确认的选择
    },
    // 当前临时选择状态（全局唯一）
    tempSelection: {
      tabIndex: 0, // 当前临时选择的Tab索引
      selectedCode: null, // 当前临时选择的code
      hasSelection: false // 是否有临时选择
    },
    // 动态多级列表数据
    levelLists: [], // 存储所有级别的列表数据 [{level: 0, items: [...]}]
    activeLevels: [], // 当前激活的层级路径 [0, 2, 1] 表示第0级选中第0个，第1级选中第2个，第2级选中第1个
    maxLevels: 5, // 最大支持层级数
    levelWidth: 50 // 每级列表的宽度百分比
  },

  lifetimes: {
    attached() {
      this.setData({readyToShow: false});
      // 如果有 chain_code，则加载真实数据
      if (this.properties.chain_code) {
        this.loadRealData(this.properties.chain_code);
      }
    }
  },

  observers: {
    visible: function (bl) {
      if (bl) {
        const {confirmedSelection} = this.data;

        // 确保数据已加载
        if (!this.data.realData && this.properties.chain_code) {
          this.loadRealData(this.properties.chain_code);
          return;
        }

        // 确保数据已初始化
        if (
          this.data.parentList.length === 0 &&
          this.data.processedData.upstream.length > 0
        ) {
          this.initializeTabData(this.data.activeTabIndex);
        }

        // 优先级：defaultCode > confirmedSelection > 默认显示
        if (this.data.defaultCode) {
          // 有传入defaultCode，使用defaultCode
          this.setData({readyToShow: false});
          const code = this.extractCodeFromDefaultCode(this.data.defaultCode);
          if (code) {
            this.setDefaultSelectWithTab(code);
          } else {
            this.setData({readyToShow: true});
          }
        } else if (
          confirmedSelection.hasConfirmed &&
          confirmedSelection.selectedCode
        ) {
          // 没有defaultCode但有已确认的选择，初始化临时选择为已确认选择
          this.setData({
            readyToShow: false,
            // 将确认选择复制到临时选择
            'tempSelection.tabIndex': confirmedSelection.tabIndex,
            'tempSelection.selectedCode': confirmedSelection.selectedCode,
            'tempSelection.hasSelection': true
          });

          // 切换到已确认选择的Tab
          if (confirmedSelection.tabIndex !== this.data.activeTabIndex) {
            this.setData({
              activeTabIndex: confirmedSelection.tabIndex
            });
            this.initializeTabData(confirmedSelection.tabIndex);
          }
          // 恢复选择状态
          setTimeout(() => {
            this.setDefaultSelect(confirmedSelection.selectedCode);
          }, 50);
        } else {
          // 没有任何已确认选择，清空临时选择，显示默认状态
          this.setData({
            'tempSelection.tabIndex': 0,
            'tempSelection.selectedCode': null,
            'tempSelection.hasSelection': false,
            readyToShow: true
          });
        }
      }
    },
    defaultCode: function (code) {
      if (code && this.data.visible) {
        this.setData({readyToShow: false});
        const extractedCode = this.extractCodeFromDefaultCode(code);
        // 只有当提取出有效的code时才设置默认选择
        if (extractedCode) {
          this.setDefaultSelectWithTab(extractedCode);
        } else {
          // 没有有效code时直接显示
          this.setData({readyToShow: true});
        }
      }
    }
  },

  methods: {
    // 加载真实数据
    async loadRealData(chain_code) {
      try {
        wx.showLoading({title: '加载中...', mask: true});

        const response = await singleChainNodeNumTreeApi({
          chain_code: chain_code,
          count_type: 1 // 默认获取全部企业数据
        });

        // 处理API返回的数据
        this.processRealData(response);

        wx.hideLoading();
      } catch (error) {
        console.error('加载产业链数据失败:', error);
        wx.hideLoading();
        wx.showToast({
          title: '数据加载失败',
          icon: 'none'
        });
      }
    },

    // 处理真实数据，按照 link_type 分类
    processRealData(apiData) {
      if (!apiData || !apiData.childs) {
        console.warn('API返回数据格式异常:', apiData);
        this.setData({readyToShow: true});
        return;
      }

      const processedData = {
        upstream: [], // link_type = 1
        midstream: [], // link_type = 2
        downstream: [] // link_type = 3
      };

      // 递归处理子节点，转换数据格式
      const processNode = node => {
        if (!node || !node.name) return null;

        return {
          code: node.expertchain_code,
          name: node.name,
          link_type: node.link_type,
          level: node.level,
          parent: node.parent,
          children: node.childs
            ? node.childs.map(child => processNode(child)).filter(Boolean)
            : []
        };
      };

      // 处理API返回的数据结构：跳过第一层的上中下游分类节点，直接使用其子节点
      apiData.childs.forEach(streamNode => {
        // streamNode 是上游/中游/下游节点
        if (streamNode.childs && streamNode.childs.length > 0) {
          // 处理该流向下的所有业务分类节点
          const businessCategories = streamNode.childs
            .map(businessNode => processNode(businessNode))
            .filter(Boolean);

          // 根据流向分类存储
          switch (streamNode.link_type) {
            case 1: // 上游
              processedData.upstream.push(...businessCategories);
              break;
            case 2: // 中游
              processedData.midstream.push(...businessCategories);
              break;
            case 3: // 下游
              processedData.downstream.push(...businessCategories);
              break;
            default:
              // 如果没有 link_type 或者是其他值，默认放到上游
              processedData.upstream.push(...businessCategories);
              break;
          }
        }
      });

      // 更新组件数据
      this.setData({
        realData: apiData,
        processedData: processedData,
        readyToShow: true
      });

      // 初始化第一个tab的数据
      this.initializeTabData(0);
    },

    // 初始化指定tab的数据
    initializeTabData(tabIndex) {
      const tabCode = TAB_OPTIONS[tabIndex].code;
      let dataSource = [];

      switch (tabCode) {
        case 'upstream':
          dataSource = this.data.processedData.upstream;
          break;
        case 'midstream':
          dataSource = this.data.processedData.midstream;
          break;
        case 'downstream':
          dataSource = this.data.processedData.downstream;
          break;
      }

      // 统一使用动态多级模式
      this.initializeDynamicLevels(dataSource);
    },

    // 初始化动态多级列表数据
    initializeDynamicLevels(dataSource) {
      // 限制数据深度为最多5层，超过的截取掉
      const limitedDataSource = this.limitDataDepth(
        dataSource,
        this.data.maxLevels
      );

      const levelLists = [];

      // 第一级：根节点列表
      levelLists.push({
        level: 0,
        items: limitedDataSource.map((item, index) => ({
          ...item,
          selected: false,
          active: index === 0 // 第一个默认激活
        }))
      });

      // 如果第一个节点有子级，显示第二级
      if (
        limitedDataSource[0] &&
        limitedDataSource[0].children &&
        limitedDataSource[0].children.length > 0
      ) {
        levelLists.push({
          level: 1,
          items: limitedDataSource[0].children.map(child => ({
            ...child,
            selected: false,
            active: false
          }))
        });
      }

      // 计算每级列表的宽度
      const levelWidth = this.calculateLevelWidth(levelLists.length);

      this.setData({
        levelLists,
        activeLevels: [0], // 记录激活路径：第0级选中第0个
        levelWidth, // 设置每级列表的宽度
        // 同时更新原有数据结构以保持兼容性
        parentList: levelLists[0] ? levelLists[0].items : [],
        activeChildList: levelLists[1] ? levelLists[1].items : [],
        selectedPath: {
          parent: null,
          child: null
        },
        finalSelection: null,
        readyToShow: true
      });
    },

    // 限制数据深度，超过maxDepth的层级会被截取
    limitDataDepth(data, maxDepth, currentDepth = 1) {
      if (currentDepth >= maxDepth) {
        // 达到最大深度，移除children
        return data.map(item => ({
          ...item,
          children: []
        }));
      }

      return data.map(item => ({
        ...item,
        children:
          item.children && item.children.length > 0
            ? this.limitDataDepth(item.children, maxDepth, currentDepth + 1)
            : []
      }));
    },

    // 计算每级列表的宽度百分比
    calculateLevelWidth(totalLevels) {
      if (totalLevels <= 2) return 50; // 2级时各占50%
      if (totalLevels === 3) return 33.33; // 3级时各占33.33%
      if (totalLevels === 4) return 25; // 4级时各占25%
      if (totalLevels === 5) return 20; // 5级时各占20%
      return 20; // 默认20%
    },

    // 从不同格式的defaultCode中提取code值（支持字符串、对象、数组格式）
    extractCodeFromDefaultCode(defaultCode) {
      if (!defaultCode) return '';
      if (typeof defaultCode === 'string') {
        return defaultCode;
      }
      if (Array.isArray(defaultCode)) {
        if (defaultCode.length === 0) return '';
        const firstItem = defaultCode[0];
        if (typeof firstItem === 'string') {
          return firstItem;
        }
        if (typeof firstItem === 'object' && firstItem.code) {
          return firstItem.code;
        }
        return '';
      }
      if (typeof defaultCode === 'object' && defaultCode.code) {
        return defaultCode.code;
      }
      return '';
    },

    // Tab 切换处理：切换数据源并重新初始化，根据临时选择状态决定是否恢复
    switchTab(e) {
      const {index} = e.currentTarget.dataset;
      const {tempSelection} = this.data;

      // 更新Tab状态
      this.setData({
        activeTabIndex: index, // 更新当前激活的Tab索引
        readyToShow: false // 暂时隐藏内容，等待数据初始化完成
      });

      // 初始化新Tab的数据
      this.initializeTabData(index);

      // 如果临时选择的Tab就是当前切换到的Tab，恢复临时选择
      if (
        tempSelection?.hasSelection &&
        tempSelection.tabIndex === index &&
        tempSelection.selectedCode
      ) {
        setTimeout(() => {
          this.setDefaultSelect(tempSelection.selectedCode);
        }, 50);
      }
      // 否则保持默认状态（第一个父级激活，右侧显示其子项）
    },

    // 智能默认选择：根据defaultCode自动查找所属Tab并切换，然后设置选中状态
    setDefaultSelectWithTab(targetCode) {
      if (!targetCode) return;

      // 在所有数据源中递归查找匹配的code
      let foundTab = null;

      // 遍历所有Tab的数据源
      for (let tabIndex = 0; tabIndex < TAB_OPTIONS.length; tabIndex++) {
        const tabCode = TAB_OPTIONS[tabIndex].code;
        let dataSource = [];

        // 根据tabCode获取对应的数据源
        switch (tabCode) {
          case 'upstream':
            dataSource = this.data.processedData.upstream;
            break;
          case 'midstream':
            dataSource = this.data.processedData.midstream;
            break;
          case 'downstream':
            dataSource = this.data.processedData.downstream;
            break;
        }

        // 在当前数据源中递归查找匹配的code
        if (this.searchCodeInDataSource(dataSource, targetCode)) {
          foundTab = tabIndex;
          break;
        }
      }

      if (foundTab !== null) {
        // 切换到对应的Tab
        this.setData({
          activeTabIndex: foundTab
        });

        // 先初始化数据
        this.initializeTabData(foundTab);

        // 延迟设置默认选择（确保数据初始化完成）
        setTimeout(() => {
          this.setDefaultSelect(targetCode);
        }, 50);
      } else {
        // 没找到匹配项，直接显示
        this.setData({readyToShow: true});
      }
    },

    // 在数据源中递归查找指定code
    searchCodeInDataSource(dataSource, targetCode) {
      const searchRecursive = items => {
        for (let item of items) {
          if (item.code === targetCode) {
            return true;
          }
          if (item.children && item.children.length > 0) {
            if (searchRecursive(item.children)) {
              return true;
            }
          }
        }
        return false;
      };

      return searchRecursive(dataSource);
    },

    // 设置默认选中项：在当前Tab的数据源中查找并回显完整路径
    setDefaultSelect(targetCode) {
      if (!targetCode) return;

      // 获取当前Tab的数据源
      const tabCode = TAB_OPTIONS[this.data.activeTabIndex].code;
      let dataSource = [];

      switch (tabCode) {
        case 'upstream':
          dataSource = this.data.processedData.upstream;
          break;
        case 'midstream':
          dataSource = this.data.processedData.midstream;
          break;
        case 'downstream':
          dataSource = this.data.processedData.downstream;
          break;
      }

      // 在数据源中查找完整路径
      const foundPath = this.findCodePathInDataSource(dataSource, targetCode);

      if (foundPath && foundPath.length > 0) {
        // 找到匹配项，重建并回显完整路径
        this.rebuildAndRestorePath(foundPath);
      } else {
        // 没找到匹配项，直接显示
        this.setData({readyToShow: true});
      }
    },

    // 在数据源中查找指定code的完整路径（从根到目标节点）
    findCodePathInDataSource(dataSource, targetCode) {
      // 递归查找函数，返回从根到目标节点的完整路径
      const searchRecursive = (items, currentPath = []) => {
        for (let i = 0; i < items.length; i++) {
          const item = items[i];
          const newPath = [...currentPath, {item, index: i}];

          // 检查当前项是否匹配
          if (item.code === targetCode) {
            return newPath;
          }

          // 如果有子级，递归查找
          if (item.children && item.children.length > 0) {
            const result = searchRecursive(item.children, newPath);
            if (result) return result;
          }
        }
        return null;
      };

      return searchRecursive(dataSource);
    },

    // 根据找到的路径重建级别列表并回显
    rebuildAndRestorePath(foundPath) {
      if (!foundPath || foundPath.length === 0) return;

      const newLevelLists = [];
      const newActiveLevels = [];

      // 逐级构建级别列表
      for (let level = 0; level < foundPath.length; level++) {
        const pathItem = foundPath[level];
        const {item, index} = pathItem;

        // 记录激活路径
        newActiveLevels.push(index);

        if (level === 0) {
          // 第一级：使用根级数据
          const tabCode = TAB_OPTIONS[this.data.activeTabIndex].code;
          let rootData = [];

          switch (tabCode) {
            case 'upstream':
              rootData = this.data.processedData.upstream;
              break;
            case 'midstream':
              rootData = this.data.processedData.midstream;
              break;
            case 'downstream':
              rootData = this.data.processedData.downstream;
              break;
          }

          newLevelLists.push({
            level: 0,
            items: rootData.map((rootItem, rootIndex) => ({
              ...rootItem,
              selected: rootIndex === index, // 路径上的都选中（显示勾勾）
              active: rootIndex === index // 路径上的都激活
            }))
          });
        } else {
          // 后续级别：使用父级的children
          const parentItem = foundPath[level - 1].item;
          if (parentItem.children && parentItem.children.length > 0) {
            newLevelLists.push({
              level: level,
              items: parentItem.children.map((child, childIndex) => ({
                ...child,
                selected: childIndex === index, // 路径上的都选中（显示勾勾）
                active: childIndex === index // 路径上的都激活
              }))
            });
          }
        }
      }

      // 检查最后选中的节点是否有子节点，如果有则展开显示
      const lastSelectedItem = foundPath[foundPath.length - 1].item;
      if (lastSelectedItem.children && lastSelectedItem.children.length > 0) {
        // 添加子节点级别
        newLevelLists.push({
          level: foundPath.length,
          items: lastSelectedItem.children.map(child => ({
            ...child,
            selected: false, // 子节点不选中
            active: false // 子节点不激活
          }))
        });
      }

      // 计算宽度
      const levelWidth = this.calculateLevelWidth(newLevelLists.length);

      // 更新状态
      this.setData({
        levelLists: newLevelLists,
        levelWidth,
        activeLevels: newActiveLevels,
        readyToShow: true
      });

      // 更新兼容性数据和最终选择
      this.updateDynamicSelection();
    },

    // 重置组件状态：清除所有选择，恢复到初始状态
    reset() {
      // 重新初始化当前Tab的数据
      this.initializeTabData(this.data.activeTabIndex);
    },

    // 关闭弹窗：取消当前操作，将确认选择恢复到临时选择
    close() {
      const {confirmedSelection} = this.data;

      // 将确认选择恢复到临时选择（取消操作）
      if (confirmedSelection.hasConfirmed) {
        this.setData({
          'tempSelection.tabIndex': confirmedSelection.tabIndex,
          'tempSelection.selectedCode': confirmedSelection.selectedCode,
          'tempSelection.hasSelection': true
        });
      } else {
        // 没有确认选择，清空临时选择
        this.setData({
          'tempSelection.tabIndex': 0,
          'tempSelection.selectedCode': null,
          'tempSelection.hasSelection': false
        });
      }

      // 重置组件状态
      this.reset();

      // 触发关闭事件，通知父组件
      this.triggerEvent('close');
    },

    // 清除所有选择记录（可供外部调用，用于重置组件状态）
    clearAllSelections() {
      this.setData({
        'confirmedSelection.tabIndex': 0,
        'confirmedSelection.selectedCode': null,
        'confirmedSelection.hasConfirmed': false,
        // 清除临时选择
        'tempSelection.tabIndex': 0,
        'tempSelection.selectedCode': null,
        'tempSelection.hasSelection': false
      });
    },

    // 选择某级别的项目（动态多级模式）
    selectLevelItem(e) {
      const {level, index} = e.currentTarget.dataset;
      const {levelLists} = this.data;

      if (!levelLists[level] || !levelLists[level].items[index]) return;

      // 找到选中的项目
      const selectedItem = levelLists[level].items[index];

      // 更新当前级别的选中状态
      this.updateLevelSelection(level, index);

      // 如果有子级，展开下一级
      if (selectedItem.children && selectedItem.children.length > 0) {
        this.expandNextLevel(level + 1, selectedItem.children);
      } else {
        // 没有子级，清除后续级别
        this.clearSubsequentLevels(level + 1);
      }

      // 更新最终选择和兼容性数据
      this.updateDynamicSelection();
    },

    // 更新某级别的选中状态（同时更新父级路径的选中状态）
    updateLevelSelection(level, selectedIndex) {
      const {levelLists, activeLevels} = this.data;
      const newLevelLists = [...levelLists];
      const newActiveLevels = [...activeLevels];

      // 更新当前级别的选中状态
      newLevelLists[level].items = newLevelLists[level].items.map(
        (item, index) => ({
          ...item,
          selected: index === selectedIndex,
          active: index === selectedIndex
        })
      );

      // 更新激活路径
      newActiveLevels[level] = selectedIndex;
      // 清除后续级别的激活路径
      newActiveLevels.splice(level + 1);

      // 重要：同时选中父级路径上的所有节点
      for (let parentLevel = 0; parentLevel < level; parentLevel++) {
        const activeIndex = newActiveLevels[parentLevel];
        if (activeIndex !== undefined && newLevelLists[parentLevel]) {
          newLevelLists[parentLevel].items = newLevelLists[
            parentLevel
          ].items.map((item, index) => ({
            ...item,
            selected: index === activeIndex, // 父级路径上的节点也要选中
            active: index === activeIndex
          }));
        }
      }

      this.setData({
        levelLists: newLevelLists,
        activeLevels: newActiveLevels
      });
    },

    // 展开下一级
    expandNextLevel(nextLevel, childrenData) {
      const {levelLists} = this.data;
      const newLevelLists = [...levelLists];

      // 清除当前级别之后的所有级别
      newLevelLists.splice(nextLevel);

      // 添加新的级别
      newLevelLists.push({
        level: nextLevel,
        items: childrenData.map(child => ({
          ...child,
          selected: false,
          active: false
        }))
      });

      // 重新计算宽度
      const levelWidth = this.calculateLevelWidth(newLevelLists.length);

      this.setData({
        levelLists: newLevelLists,
        levelWidth
      });
    },

    // 清除后续级别
    clearSubsequentLevels(fromLevel) {
      const {levelLists} = this.data;
      const newLevelLists = levelLists.slice(0, fromLevel);

      // 重新计算宽度
      const levelWidth = this.calculateLevelWidth(newLevelLists.length);

      this.setData({
        levelLists: newLevelLists,
        levelWidth
      });
    },

    // 更新动态选择的最终结果和兼容性数据
    updateDynamicSelection() {
      const {levelLists} = this.data;
      let finalSelection = null;
      let selectedPath = {parent: null, child: null};

      // 从最后一级开始查找选中项
      for (let i = levelLists.length - 1; i >= 0; i--) {
        const selectedItem = levelLists[i].items.find(item => item.selected);
        if (selectedItem) {
          finalSelection = selectedItem;

          // 构建选择路径：找到选择链上的父级和子级
          if (levelLists.length === 1) {
            // 只有一级，选中的就是父级
            selectedPath.parent = selectedItem;
            selectedPath.child = null;
          } else if (i === 0) {
            // 选中的是第一级
            selectedPath.parent = selectedItem;
            selectedPath.child = null;
          } else {
            // 选中的是后续级别，找到路径上的第一级作为parent，当前选中的作为child
            selectedPath.parent = levelLists[0].items.find(
              item => item.selected
            );
            selectedPath.child = selectedItem;
          }
          break;
        }
      }

      // 更新兼容性数据和临时选择
      const currentTabIndex = this.data.activeTabIndex;
      this.setData({
        finalSelection,
        selectedPath,
        parentList: levelLists[0] ? levelLists[0].items : [],
        activeChildList: levelLists[1] ? levelLists[1].items : [],
        // 更新临时选择
        'tempSelection.tabIndex': currentTabIndex,
        'tempSelection.selectedCode': finalSelection
          ? finalSelection.code
          : null,
        'tempSelection.hasSelection': !!finalSelection
      });
    },

    // 提交选择：验证选择结果，记住选择状态，并返回给父组件
    submit() {
      const {
        finalSelection,
        selectedPath,
        tempSelection,
        tabs,
        activeTabIndex
      } = this.data;

      // 验证是否有选择项
      if (!finalSelection) {
        wx.showToast({title: '请选择选项', icon: 'none'});
        return;
      }

      // 确认选择：将临时选择转为已确认选择
      this.setData({
        'confirmedSelection.tabIndex': tempSelection.tabIndex,
        'confirmedSelection.selectedCode': tempSelection.selectedCode,
        'confirmedSelection.hasConfirmed': true,
        // 清除临时选择
        'tempSelection.tabIndex': 0,
        'tempSelection.selectedCode': null,
        'tempSelection.hasSelection': false
      });

      // 构造返回的选择结果
      const selectionWithParent = {...finalSelection};
      if (selectedPath.child) {
        // 如果选择了子级，添加父级信息
        selectionWithParent.parent = selectedPath.parent?.code;
      } else if (selectedPath.parent) {
        // 如果只选择了父级，parent设为null
        selectionWithParent.parent = null;
      }

      // 添加当前Tab信息到路径中
      const currentTab = tabs[activeTabIndex];
      const pathWithTab = {
        ...selectedPath,
        tabCode: currentTab.code, // 当前Tab的code
        tabName: currentTab.name // 当前Tab的名称
      };

      // 触发提交事件，返回选择结果和路径信息
      this.triggerEvent('submit', {
        selection: selectionWithParent, // 选择的项目（包含父级信息）
        path: pathWithTab // 选择路径（包含Tab信息）
      });
    }
  }
});
