import {
  business,
  chain,
  common
} from '../../../../service/api';
import {
  setTagColor,
  preventActive
} from '../../../../utils/util.js'
import {
  getHeight
} from '../../../../utils/height.js'
import {
  hasPrivile
} from '../../../../utils/route'
import {
  collect
} from '../../../../utils/mixin/collect'
const app = getApp();
Page({
  data: {
    company_num: 0,
    //请求的相关
    paramsNew: {
      page_index: 1,
      page_size: 10
    },
    requestData: [],
    hasData: true, // 判断接口是否还有数据返回
    isTriggered: false, // 下拉刷新状态
    // 联系方式
    showContact: false,
    contactList: [], //联系方式列表
    activeEntId: '', // 当前点击企业的id
    // 地址
    showAddress: false,
    markers: [],
    locationTxt: '',
    location: {
      lat: '',
      lon: '',
    },
    dropDownMenuTitle: ['所属地区', '全部行业', '更多筛选'],
    isLogin: app.isLogin(),
    entUnlogin: true, // 是否是在未登录的情况点击了企业标题
  },
  onLoad: function (options) {
    const {
      key_word
    } = options;
    wx.setNavigationBarTitle({
      title: key_word
    })
    const {
      paramsNew
    } = this.data
    paramsNew.key_word = key_word;
    this.setData({
      paramsNew
    })
  },
  // 下拉刷新
  handleRefresher() {
    let {
      paramsNew
    } = this.data;
    paramsNew.page_index = 1;
    this.setData({
      paramsNew,
      requestData: [],
      isTriggered: true,
      hasData: true,
    })
    this.quest();
  },
  //加载更多
  async loadMore() {
    // 判断vip类型 主要是针对普通用户 
    if (this.data.requestData.length >= 20) {
      let permission = await hasPrivile({
        packageType: true
      })
      this.setData({
        permission,
      })
      if (permission == '普通VIP') return;
    }
    let {
      paramsNew,
      hasData
    } = this.data;
    if (!hasData) return;
    paramsNew.page_index += 1;
    this.setData({
      paramsNew
    });
    this.quest(true);
  },

  async quest(bl) {
    wx.showLoading({
      title: '正在加载',
      mask: true
    })
    let {
      paramsNew,
      hasData,
      requestData
    } = this.data;
    hasData = true;
    // business.queryIndustryChain(paramsNew) 这个接口没数据，先用chainDetail接口调样式，调完换回去
    // console.log('222', paramsNew);
    let {
      dataList,
      count
    } = await business.queryIndustryChain(paramsNew);
    dataList = dataList.map(item => {
      item.tags = setTagColor(item.tags);
      return item;
    });
    dataList = this.keyConversion(dataList);
    dataList.forEach(item => {
      item.tags = item.tags.splice(0, 3)
    })
    if (dataList.length < paramsNew.page_size || count == paramsNew.page_size) hasData = false;
    this.setData({
      hasData,
      company_num: count,
      requestData: bl ? requestData.concat(dataList) : dataList,
      isTriggered: false
    })
    wx.hideLoading();
  },
  // 点击企业标题
  handleTit() {
    const {
      isLogin
    } = this.data;
    this.setData({
      entUnlogin: !isLogin
    });
  },
  onCloseContact() {
    this.setData({
      showContact: false
    })
  },
  onCloseAddress() {
    this.setData({
      showAddress: false
    })
  },
  onShow() {
    const {
      entUnlogin
    } = this.data;
    this.handleHeight();
    entUnlogin && this.quest();
  },
  setVipVisible(detail) {
    this.setData({
      vipVisible: detail
    });
  },
  // 筛选
  onFlitter(e) {
    let {
      dropDownMenuTitle,
      paramsNew,
      requestData,
    } = this.data
    const obj = e.detail;
    dropDownMenuTitle[0] = obj.name1
    dropDownMenuTitle[1] = obj.name2,
      delete obj['name1'];
    delete obj['name2'];
    delete obj['isFilter'];
    paramsNew = {
      ...paramsNew,
      page_index: 1,
      page_size: 10,
      ...obj
    }
    requestData = []
    this.setData({
      paramsNew: paramsNew,
      requestData,
      dropDownMenuTitle
    }, () => {
      this.handleRefresher()
    })
  },
  keyConversion(data) {
    return data.map(item => {
      for (const key in item) {
        const reg = /[A-Z]/g;
        if (reg.test(key)) {
          const newKey = key.replace(reg, str => '_' + str.toLowerCase());
          item[newKey] = item[key];
          Reflect.deleteProperty(item, key);
        };
      };
      return item;
    })
  },

  // 卡片点击回调 
  async onCard(data) {
    let that = this
    preventActive(this, async () => {
      // 地址  "site"  联系方式 "relation" 官网 "official" 收藏 "collect" 四个字段对呀四个方法逻辑-具体见设计图
      const type = data.detail.type
      const comDetail = data.detail.data
      // 处理收藏 
      if (type == 'collect') {
        comDetail.tags = comDetail.tags.map(tag => tag.tagName);
        collect(that, comDetail, 'requestData')
      } else if (type == 'relation') {
        this.setData({
          activeEntId: comDetail.ent_id,
          showContact: true
        });
      } else if (type === 'site') {
        this.setData({
          location: comDetail.location,
          locationTxt: comDetail.register_address,
          markers: [{
            id: 1,
            latitude: comDetail.location.lat,
            longitude: comDetail.location.lon,
            iconPath: "https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/marker.png",
            width: 20,
            height: 20,
          }],
          showAddress: true
        })
      }
    })
  },
  // 动态获取蒙层高度
  handleHeight() {
    const that = this
    getHeight(that, '.page_head', (data) => {
      // console.log("计算蒙层高度", data)
      this.setData({
        computedHeight: data.screeHeight - data.res[0].height,
        isLogin: app.isLogin()
      })
    })
  },
  vipPop(val) {
    this.setData({
      vipVisible: val
    })
  }
});