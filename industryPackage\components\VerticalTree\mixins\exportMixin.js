// 导出图片功能混入逻辑

// 导入常量配置
const {
  CANVAS_CONSTANTS,
  UTILS_CONSTANTS,
  EXPORT_CONSTANTS,
  ERROR_MESSAGES
} = require('../config/constants.js');

const exportMixin = {
  data: {
    // 导出相关状态
    _isExporting: false,
    _exportCanvas: null,
    _exportCtx: null,
    // 权限相关状态
    openSet: false
  },

  methods: {
    // 导出按钮点击处理
    handleExportImage() {
      if (this.data._isExporting) return;

      wx.showLoading({title: '正在生成图片...'});
      this.setData({_isExporting: true});

      // 使用wx.nextTick确保UI更新后再执行
      wx.nextTick(() => {
        this.exportImage();
      });
    },

    // 核心导出逻辑
    exportImage() {
      try {
        // 1. 计算完整内容尺寸
        const fullSize = this._calculateFullContentSize();

        // 2. 创建导出用临时canvas
        const {canvas, ctx} = this._createExportCanvas(
          fullSize.width,
          fullSize.height
        );

        // 3. 在临时canvas上渲染完整内容
        this._renderFullContentToCanvas(ctx, fullSize);

        // 4. 导出图片
        wx.canvasToTempFilePath({
          canvas: canvas,
          fileType: 'png',
          quality: 1,
          success: res => {
            this._handleExportSuccess(res.tempFilePath);
          },
          fail: err => {
            this._handleExportError('导出图片失败', err);
          }
        });
      } catch (error) {
        this._handleExportError('生成图片失败', error);
      }
    },

    // 计算完整内容尺寸
    _calculateFullContentSize() {
      let bounds = {
        minX: Infinity,
        maxX: -Infinity,
        minY: Infinity,
        maxY: -Infinity
      };

      // 获取当前布局数据
      const layout = this.currentLayout || this._getCurrentLayout();

      if (layout && layout.nodes && layout.nodes.length > 0) {
        // 遍历所有节点
        layout.nodes.forEach(node => {
          const nodeBounds = this._getNodeBounds(node);
          bounds.minX = Math.min(bounds.minX, nodeBounds.left);
          bounds.maxX = Math.max(bounds.maxX, nodeBounds.right);
          bounds.minY = Math.min(bounds.minY, nodeBounds.top);
          bounds.maxY = Math.max(bounds.maxY, nodeBounds.bottom);
        });
      } else {
        // 如果没有布局数据，使用默认尺寸
        console.warn('没有找到布局数据，使用默认尺寸');
        bounds = {
          minX: 0,
          maxX: this.rpxToPx(750),
          minY: 0,
          maxY: this.rpxToPx(1200)
        };
      }

      // 考虑连接线的边界
      if (layout && layout.connections && layout.connections.length > 0) {
        const lineBounds = this._getConnectionLineBounds(layout.connections);
        bounds.minX = Math.min(bounds.minX, lineBounds.minX);
        bounds.maxX = Math.max(bounds.maxX, lineBounds.maxX);
        bounds.minY = Math.min(bounds.minY, lineBounds.minY);
        bounds.maxY = Math.max(bounds.maxY, lineBounds.maxY);
      }

      // 添加边距
      const padding = this.rpxToPx(EXPORT_CONSTANTS.PADDING);

      return {
        width: bounds.maxX - bounds.minX + padding * 2,
        height: bounds.maxY - bounds.minY + padding * 2,
        offsetX: -bounds.minX + padding,
        offsetY: -bounds.minY + padding,
        contentBounds: bounds
      };
    },

    // 获取当前布局数据
    _getCurrentLayout() {
      try {
        // 如果没有缓存的布局，重新计算
        const treeData = this._prepareTreeData();
        if (treeData && treeData.length > 0) {
          return this.calculateTreeLayout(treeData);
        }
      } catch (error) {
        console.error('获取当前布局失败:', error);
      }
      return null;
    },

    // 获取单个节点边界
    _getNodeBounds(node) {
      return {
        left: node.x,
        right: node.x + node.width,
        top: node.y - node.height / 2,
        bottom: node.y + node.height / 2
      };
    },

    // 获取连接线边界
    _getConnectionLineBounds(connections) {
      if (!connections || connections.length === 0) {
        return {
          minX: 0,
          maxX: 0,
          minY: 0,
          maxY: 0
        };
      }

      let minX = Infinity,
        maxX = -Infinity;
      let minY = Infinity,
        maxY = -Infinity;

      connections.forEach(conn => {
        // 连接线的起点和终点
        minX = Math.min(minX, conn.fromX, conn.toX);
        maxX = Math.max(maxX, conn.fromX, conn.toX);
        minY = Math.min(minY, conn.fromY, conn.toY);
        maxY = Math.max(maxY, conn.fromY, conn.toY);
      });

      return {minX, maxX, minY, maxY};
    },

    // 创建导出用临时canvas
    _createExportCanvas(width, height) {
      try {
        // 使用离屏canvas
        const canvas = wx.createOffscreenCanvas({
          type: '2d',
          width: width,
          height: height
        });

        const ctx = canvas.getContext('2d');

        // 获取设备像素比
        const dpr = this._getDevicePixelRatio();

        // 设置高清显示
        canvas.width = width * dpr;
        canvas.height = height * dpr;
        ctx.scale(dpr, dpr);

        // 设置默认样式
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';

        // 保存canvas引用用于清理
        this.setData({
          _exportCanvas: canvas,
          _exportCtx: ctx
        });

        return {canvas, ctx};
      } catch (error) {
        console.error('创建导出canvas失败:', error);
        throw new Error('创建导出canvas失败');
      }
    },

    // 获取设备像素比
    _getDevicePixelRatio() {
      try {
        if (wx.getWindowInfo) {
          const windowInfo = wx.getWindowInfo();
          return windowInfo.pixelRatio || UTILS_CONSTANTS.DEFAULT_DPR;
        } else if (wx.getDeviceInfo) {
          const deviceInfo = wx.getDeviceInfo();
          return deviceInfo.pixelRatio || UTILS_CONSTANTS.DEFAULT_DPR;
        } else {
          return UTILS_CONSTANTS.DEFAULT_DPR;
        }
      } catch (error) {
        console.warn('获取设备像素比失败，使用默认值:', error);
        return UTILS_CONSTANTS.DEFAULT_DPR;
      }
    },

    // 在临时canvas上渲染完整内容
    _renderFullContentToCanvas(exportCtx, fullSize) {
      // 保存当前渲染状态
      const originalState = this._saveCurrentRenderState();

      try {
        // 调整渲染参数用于导出
        this._adjustRenderingForExport(exportCtx, fullSize);

        // 获取布局数据
        const layout = this.currentLayout || this._getCurrentLayout();
        if (!layout) {
          throw new Error('无法获取布局数据');
        }

        // 直接渲染到导出canvas，不使用拖拽偏移
        this._renderTreeContentForExport(layout, exportCtx, fullSize);
      } finally {
        // 恢复原始渲染状态
        this._restoreRenderingState(originalState);
      }
    },

    // 专门用于导出的渲染方法
    _renderTreeContentForExport(layout, exportCtx, fullSize) {
      // 保存导出canvas状态
      exportCtx.save();

      try {
        // 设置背景色（与正常canvas保持一致）
        this._setExportCanvasBackground(exportCtx, fullSize);

        // 应用偏移，确保内容在canvas中居中
        exportCtx.translate(fullSize.offsetX, fullSize.offsetY);

        // 临时设置ctx为导出canvas
        const originalCtx = this.ctx;
        this.ctx = exportCtx;

        // 绘制连接线
        this.drawConnections(layout);
        // 绘制节点
        this.drawNodes(layout);

        // 恢复原始ctx
        this.ctx = originalCtx;
      } finally {
        // 恢复导出canvas状态
        exportCtx.restore();
      }
    },

    // 设置导出canvas的背景色
    _setExportCanvasBackground(exportCtx, fullSize) {
      // 使用与正常canvas相同的背景色
      exportCtx.fillStyle = EXPORT_CONSTANTS.BACKGROUND_COLOR;
      exportCtx.fillRect(0, 0, fullSize.width, fullSize.height);
    },

    // 保存当前渲染状态
    _saveCurrentRenderState() {
      return {
        ctx: this.data.ctx,
        translateX: this.data.translateX,
        translateY: this.data.translateY,
        isExporting: this.data._isExporting
      };
    },

    // 调整渲染参数用于导出
    _adjustRenderingForExport(exportCtx, fullSize) {
      // 只设置导出状态，不改变translateX和translateY
      this.setData({
        _isExporting: true
      });
    },

    // 恢复原始渲染状态
    _restoreRenderingState(originalState) {
      this.setData({
        ctx: originalState.ctx,
        translateX: originalState.translateX,
        translateY: originalState.translateY,
        _isExporting: originalState.isExporting
      });
    },

    // 处理导出成功
    _handleExportSuccess(tempFilePath) {
      wx.hideLoading();
      this.setData({_isExporting: false});
      this._cleanupExportResources();

      if (tempFilePath) {
        this.saveImage(tempFilePath);
      } else {
        this._showExportSuccess();
      }
    },

    // 处理导出错误
    _handleExportError(message, error) {
      wx.hideLoading();
      this.setData({_isExporting: false});
      this._cleanupExportResources();
      console.error(message, error);
      this._showExportError(message);
    },

    // 保存到相册
    saveImage(tempFilePath) {
      const that = this;

      wx.getSetting({
        success: res => {
          if (!res.authSetting['scope.writePhotosAlbum']) {
            // 没有权限，请求授权
            that._requestPhotoAlbumPermission(tempFilePath);
          } else {
            // 有权限，直接保存
            that._saveToPhotoAlbum(tempFilePath);
          }
        },
        fail: err => {
          that._handleExportError('获取权限信息失败', err);
        }
      });
    },

    // 请求相册权限
    _requestPhotoAlbumPermission(tempFilePath) {
      const that = this;

      wx.authorize({
        scope: 'scope.writePhotosAlbum',
        success: () => {
          that._saveToPhotoAlbum(tempFilePath);
        },
        fail: () => {
          // 用户拒绝授权，显示设置引导
          that.setData({openSet: true});
        }
      });
    },

    // 保存到相册
    _saveToPhotoAlbum(tempFilePath) {
      const that = this;

      wx.saveImageToPhotosAlbum({
        filePath: tempFilePath,
        success: () => {
          that._showExportSuccess();
        },
        fail: err => {
          if (err.errMsg === 'saveImageToPhotosAlbum:fail auth deny') {
            that.setData({openSet: true});
          } else {
            that._showExportError('保存失败，请截屏保存', err);
          }
        }
      });
    },

    // 权限弹窗确认
    openSetSubmit() {
      this.setData({openSet: false});
      wx.openSetting({
        success: res => {
          if (res.authSetting['scope.writePhotosAlbum']) {
            wx.showToast({title: '权限已开启', icon: 'success'});
          }
        }
      });
    },

    // 权限弹窗关闭
    openSetClose() {
      this.setData({openSet: false});
    },

    // 清理导出资源
    _cleanupExportResources() {
      this.setData({
        _exportCanvas: null,
        _exportCtx: null
      });
    },

    // 显示导出错误
    _showExportError(message) {
      wx.showToast({
        title: message || '导出失败',
        icon: 'none',
        duration: 2000
      });
    },

    // 显示导出成功
    _showExportSuccess() {
      wx.showToast({
        title: '已保存到相册',
        icon: 'success',
        duration: 2000
      });
    }
  }
};

module.exports = {exportMixin};
