.search-card {
  width: 100%;
  background: #ffffff;
  padding: 32rpx 24rpx 16rpx;
  margin-top: 20rpx;
}

.search-card .card-boxs {
  display: flex;
  align-self: start;
}

.search-card .card-logo {
  width: 100rpx;
  height: 100rpx;
  flex-shrink: 0;
  box-shadow: 0px 0px 8rpx 0px rgba(32, 38, 58, 0.1);
  /* border-radius: 8rpx; */
  margin-right: 24rpx;
  overflow: hidden;
}

.card-head {
  width: 100%;
}

.card_h {
  display: flex;
  justify-content: space-between;
  flex-wrap: nowrap;
  flex: 1;
  margin-bottom: 10rpx;
}

.card_h_l {
  /* display: flex; */
  width: 420rpx;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  flex-wrap: nowrap;
}

.card_h_l .text {
  display: inline;
  font-size: 32rpx;
  font-family:
    PingFang SC,
    PingFang SC-Regular;
  font-weight: 400;
  text-align: LEFT;
  color: #20263a;
}

.card_h_l .activetext {
  color: #e72410 !important;
}

.card_h_l .activetexts {
  color: rgba(7, 110, 228, 1) !important;
}

.card_h_r {
  display: flex;
  align-items: center;
  justify-content: center;
  /* width: 108rpx; */
  /* height: 48rpx; */
  /* opacity: 0.5; */
  /* border: 2rpx solid rgba(222, 222, 222, 0.5); */
  /* border-radius: 4rpx; */
}

.card_h_r text {
  font-size: 24rpx;
  font-family:
    PingFang SC,
    PingFang SC-Regular;
  font-weight: 400;
  text-align: LEFT;
  color: #74798c;
}

.card_h_r_img {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36rpx;
  height: 36rpx;
  margin-right: 4rpx;
}

.card_h_r_2 {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 108rpx;
  height: 48rpx;
  border-radius: 4rpx 4rpx 4rpx 4rpx;
  opacity: 1;
  border: 2rpx solid #e72410;
}

.card_h_r_2 > view {
  width: 20rpx;
  height: 20rpx;
  background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUBAMAAAB/pwA+AAAAAXNSR0IArs4c6QAAABtQTFRFAAAAAGvmCGvmBGzlBm7lBW/lBW3kBm7lB27k3u5XhQAAAAh0Uk5TAB8fO09jZ4fShlGuAAAAJ0lEQVQI12NgAAKPRgYYoBrTBQgq2kAkQwccIDOVgCCiGUQy0MgNADPzHDrqzoqoAAAAAElFTkSuQmCC");
  background-size: 100% 100%;
  background-repeat: no-repeat;
}

.card_h_r_2 > text {
  font-size: 24rpx;
  font-family:
    PingFang SC-Regular,
    PingFang SC;
  font-weight: 400;
  color: #e72410;
  margin-left: 8rpx;
}

/* 监控 */
.card_h_r_3 {
  display: flex;
  align-items: center;
  justify-content: center;
  background: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAGAAAAA4CAYAAAACRf2iAAAAAXNSR0IArs4c6QAAAj1JREFUeF7tnDFOAlEURd//n4BsgFBKYqlRV2CjrVvQGAujxtVoMKHANWiJhTswxlhRYMfvgYwBCWZGJo40cMMtrx3xzjE5ZwobnrOFn8FgsF0qla7M7NDMNs2stLjRZ8jAt5l9mtlLCOG2XC6/FZ92+Yder7dRr9ebZnYK4TVGDbRijDeNRuMrfTALMJf/NH/rUaD2oAHnXKff7x+nEbIASZK09eaDFteft6vV6pkbDod7IYTX9XkigAZm3vsdlyRJy8zOwYc15xi4TwN0zWyLwxMFNNBNA0z0ryaojTefpAFmPJ5IoIGZAoDGyHMFIAtFcQqAGiPvFYAsFMUpAGqMvFcAslAUpwCoMfJeAchCUZwCoMbIewUgC0VxCoAaI+8VgCwUxSkAaoy8VwCyUBSnAKgx8l4ByEJRnAKgxsh7BSALRXEKgBoj7xWALBTFKQBqjLxXALJQFKcAqDHyXgHIQlGcAqDGyHsFIAtFcQqAGiPvFYAsFMUpAGqMvFcAslAUpwCoMfJeAchCUZwCoMbIewUgC0VxCoAaI+8VgCwUxSkAaoy8VwCyUBSnAKgx8j4LoC9qk60CuOyL2jpVABgjT7NTBemRpgsyWLgVDDjnWm5+oux9hb0mXAOzEMJ+drBpNBq1vfc6VcYVvIz2e7ApXc1Plj2a2dGyp/R7ioFOjPHvZFkeoVarNb33J/ktOcqfEqRoIL1M8xBjvPx3tK+4GI/Hu9Pp9NrMDnS2kvL25Gcrn733d5VK5aNI/QH7bCwtHk14GAAAAABJRU5ErkJggg==");
  background-size: 100% 100%;
  background-repeat: no-repeat;
  width: 96rpx;
  height: 56rpx;
  border-radius: 8rpx;
  overflow: hidden;
  font-size: 24rpx;
  font-family:
    PingFang SC-Regular,
    PingFang SC;
  font-weight: 400;
  color: #e72410;
}

.card_tag {
  display: flex;
  overflow: hidden;
  /* width: 580rpx; */
}

.card_tag_box {
  overflow: hidden;
  text-overflow: ellipsis;
  text-align: justify;
  /* display: flex; */
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  position: relative;
}

.card_tag_i {
  display: inline-flex;
  padding: 2rpx 12rpx;
  /* min-width: 72rpx; */
  margin-right: 16rpx;
  font-size: 24rpx;
  font-family:
    PingFang SC,
    PingFang SC-Regular;
  font-weight: 400;
  justify-content: center;
  align-items: center;
  border-radius: 4rpx;
}

.card_c {
  display: flex;
  justify-content: space-around;
  padding: 20rpx 0 20rpx;
}

.card_c_i {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
}

.zj {
  flex: 1.336;
}

.card_c_is::before {
  position: absolute;
  content: "";
  right: 0;
  top: 50%;
  -webkit-transform: translateY(-50%);
  transform: translateY(-50%);
  border-right: 1px solid rgba(238, 238, 238, 0.8);
  width: 0;
  height: 48rpx;
}

.card_c_is::after {
  position: absolute;
  content: "";
  left: 0;
  top: 50%;
  -webkit-transform: translateY(-50%);
  transform: translateY(-50%);
  border-right: 1px solid rgba(238, 238, 238, 0.8);
  width: 0;
  height: 48rpx;
}

.card_c_i .name {
  font-size: 24rpx;
  font-family:
    PingFang SC,
    PingFang SC-Regular;
  font-weight: 400;
  text-align: CENTER;
  color: #9b9eac;
  padding-bottom: 12rpx;
}

.card_c_i .cont {
  font-size: 28rpx;
  font-family:
    PingFang SC,
    PingFang SC-Regular;
  font-weight: 400;
  text-align: CENTER;
  /* color: #74798c; */
  color: #3d4255;
}

.card_c_i .contblue {
  /* color: #74798C; */
  color: #1e75db;
}

/* 那根横线 后面在调 */
.card_ico {
  position: relative;
  display: flex;
  justify-content: flex-end;
  flex-wrap: nowrap;
  padding: 16rpx 0rpx 0 0;
}

.card_ico::after {
  content: " ";
  width: calc(100% + 48rpx);
  height: 1px;
  background: #eee;
  /* background: red; */
  position: absolute;
  top: 0;
  left: -24rpx;
  transform: scaleY(0.5);
}

.card_ico_i {
  display: flex;
  justify-content: center;
  align-items: center;
  /* background: rgba(7, 110, 228, 0.06); */
  border: 1rpx solid #dedede;
  border-radius: 8rpx;
  margin-left: 40rpx;
  padding: 8rpx 12rpx;
  font-size: 24rpx;
  font-family:
    PingFang SC,
    PingFang SC-Regular;
  font-weight: 400;
  color: #20263a;
}

.card_ico_i_img {
  width: 32rpx;
  height: 32rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 4rpx;
}
.bangdan_logo {
  display: flex;
  align-items: flex-start;
  flex-shrink: 0;
  margin-right: 20rpx;
}
.bangdan_logo .img {
  width: 96rpx;
  height: 96rpx;
  margin-left: 12rpx;
}
.bangdan_logo .left {
  width: 48rpx;
  height: 60rpx;
  position: relative;
  display: flex;
  justify-content: center;
}
.bangdan_logo text {
  position: relative;
  z-index: 10;
  font-weight: bold;
  font-size: 24rpx;
  top: 8rpx;
}
.bangdan_logo .bd_bg {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
}
