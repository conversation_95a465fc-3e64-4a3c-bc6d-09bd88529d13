<import src="/template/tabBar/index"></import>
<import src="/template/null/null"></import>
<wxs module="tool" src="./tool.wxs"></wxs>
<view class="page">
  <CustomNavbar
    text="项目详情"
    id="navigationBar"
    class="navigationBar"
    textPositon="center"
    navColor="{{['rgba(255, 255, 255, 1)', 'rgba(255, 255, 255, 1)']}}"
    showNav
    navBg="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/other/top_bg0.png"
    navBg0="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/other/statusbg.png"
  />
  <scroll-view
    class="mine"
    style="height: {{contentH}}px"
    scroll-y
    scroll-with-animation
    bindscroll="onScroll"
    throttle="{{false}}"
  >
    <image
      class="head_bg"
      src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/other/top_bg1.png"
      mode="aspectFill"
    ></image>
    <view class="header-box">
      <view class="box-up">
        <view class="left">
          <image
            src="{{obj.logo ?'https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/default.png':obj.logo}}"
            mode="aspectFit"
          ></image>
        </view>
        <view class="right">
          <view class="name_tag">
            <text class="com_name"> {{ obj.ent_name }}</text>
          </view>
          <view class="tag_box tag_box_II">
            <view class="tag_item">
              <view
                class="track_status"
                style="color:#FD9331 ;background-color: rgba(253,147,49,0.4);"
              >
                <text>
                  {{obj.project_process==undefined? '跟进状态':tool.enumTrackStatus(obj.project_process)}}</text
                >
                <view
                  class="track_status_icon"
                  bindtap="editTag"
                  data-item="track_status"
                >
                  <image
                    src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/cyt_v2/image/edit.png"
                    mode="aspectFill"
                  />
                </view>
              </view>
              <view
                class="track_status"
                wx:if="{{tool.checkkey(obj.project_type)}}"
                style="color:#1E75DB ;background-color: rgba(30, 117, 219, 0.4);"
              >
                <text>{{tool.enumType(obj.project_type)}}</text>
                <view
                  class="track_status_icon"
                  bindtap="editTag"
                  data-item="project_type"
                >
                  <image
                    src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/cyt_v2/image/edit-type.png"
                    mode="aspectFill"
                  />
                </view>
              </view>
              <view
                class="track_status"
                style="color:#1E75DB ;background-color: rgba(30,117,219,0.4);"
                wx:else
              >
                <text>项目类型</text>
                <view
                  class="track_status_icon"
                  bindtap="editTag"
                  data-item="project_type"
                >
                  <image
                    src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/cyt_v2/image/plus.png"
                    mode="aspectFill"
                  />
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
      <view
        class="box-down"
        style="margin-top: 13rpx;justify-content: space-between;padding-right: 35rpx;"
      >
        <view class="box-down-item" wx:if="{{obj.collect_date}}">
          <text class="box-down-label">收藏日期：</text>
          <text>{{obj.collect_date||'--'}}</text>
        </view>
        <view class="box-down-item" wx:if="{{obj.latest_track_time}}">
          <text class="box-down-label">最近跟进：</text>
          <text>{{tool.fomatTime(obj.latest_track_time)}}</text>
        </view>
      </view>
      <view class="fuc-box">
        <view
          class="fuc-item"
          bindtap="clickFunBox"
          data-item="push"
          wx:if="{{isvip || selfIsTeamVip}}"
        >
          <view class="fuc-item-icon">
            <image
              src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/cyt_v2/image/business/translate.png"
              mode="aspectFill"
            />
          </view>
          <text>转给他人</text>
        </view>
        <view class="fuc-item" bindtap="clickFunBox" data-item="website">
          <view class="fuc-item-icon">
            <image
              src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/cyt_v2/image/business/website.png"
              mode="aspectFill"
            />
          </view>
          <text>官网</text>
        </view>
        <view class="fuc-item" bindtap="clickFunBox" data-item="site">
          <view class="fuc-item-icon">
            <image
              src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/cyt_v2/image/business/adress.png"
              mode="aspectFill"
            />
          </view>
          <text>地址</text>
        </view>
        <view class="fuc-item" bindtap="clickFunBox" data-item="report">
          <view class="fuc-item-icon">
            <image
              src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/cyt_v2/image/business/report.png"
              mode="aspectFill"
            />
          </view>
          <text>企业报告</text>
        </view>
      </view>
    </view>

    <view style="margin-top:{{ contentT }}rpx;border-top: 24rpx solid #F7F7F7;">
      <view id="navbars" style="height: 82rpx; ">
        <template
          is="nav"
          data="{{activeIndex,tabs,sliderLeft,sliderOffset,sliderWidth}}"
        ></template>
      </view>
      <view class="track-box" wx:if="{{activeIndex==0}}">
        <view class="plan-item" wx:for="{{trackPlanList}}" wx:key="index">
          <view class="item-box-top">
            <view class="item-box-tag"> 待完成 </view>
            <view class="icon_fnc_box">
              <view
                class="icon_fnc_box_icon"
                bindtap="openPlanDialog"
                data-item="{{tool.objPush('deletePlan',item)}}"
              >
                <image
                  src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/cyt_v2/image/delet.png"
                  mode="aspectFill"
                />
              </view>
              <view
                class="icon_fnc_box_icon"
                bindtap="openPlanDialog"
                data-item="{{tool.objPush('finishPlan',item)}}"
              >
                <image
                  src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/cyt_v2/image/finsh.png"
                  mode="aspectFill"
                />
              </view>
            </view>
          </view>
          <view class="item-box-center">
            <text class="item-box-label">跟进时间：</text>
            <text>{{item.to_do_time}}</text>
          </view>
          <view class="item-box-center">
            <text class="item-box-label">计划内容：</text>
            <text>{{item.plan_content}}</text>
          </view>
        </view>
        <view class="history">
          <block wx:for="{{trackDateList}}" wx:key="index" data-item="item">
            <view class="list">
              <view class="date">
                <text>
                  {{trackDateList[trackDateList.length-1-index]._create_time}}</text
                >
              </view>
              <view class="history-tips">
                {{trackDateList[trackDateList.length-1-index].track_user_name}}
                <text style="color: #9B9EAC;">添加了 </text>
                <text style="color: #1E75DB;">跟进记录</text>
              </view>
              <OrgTag obj="{{trackDateList[trackDateList.length-1-index]}}">
              </OrgTag>
            </view>
          </block>
          <!--暂无数据-->
          <view
            wx:if="{{trackDateList.length <= 0}}"
            style="width: 100%; height:100px"
          >
            <template is="null"></template>
          </view>
        </view>
      </view>
      <view class="contact-box" wx:else>
        <Wrapper id="contactPerson" title="自建联系人" marginTop="20rpx">
          <view
            slot="content"
            class="wrapper-content"
            style="margin-bottom: 100rpx;"
          >
            <view class="contact-list" wx:for="{{contactList}}" wx:key="index">
              <view class="contact-list-head">
                <view class="item-l">
                  <text class="item-l-contact_name">{{item.contact_name}}</text>
                  <text class="item-l-position">{{item.position}}</text>
                </view>
                <view class="icon_fnc_box">
                  <view
                    class="icon_fnc_box_icon"
                    bindtap="openPlanDialog"
                    data-item="{{tool.objPush('deleteContact',item)}}"
                  >
                    <image
                      src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/cyt_v2/image/delet.png"
                      mode="aspectFill"
                    />
                  </view>
                  <view
                    class="icon_fnc_box_icon"
                    bindtap="openPlanDialog"
                    data-item="{{tool.objPush('editContact',item)}}"
                  >
                    <image
                      src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/cyt_v2/image/edit_contact.png"
                      mode="aspectFill"
                    />
                  </view>
                </view>
              </view>
              <view class="contact-list-body" wx:if="{{item.tel}}">
                <text>手机：</text>
                <text style="color: #1E75DB;">{{item.tel}}</text>
              </view>
              <!-- <view class="contact-list-body" wx:if="{{item.tel}}">
                <text >微信：</text>
                <text style="color: #20263A;" >{{item.tel}}</text>
              </view> -->
              <view class="contact-list-body" wx:if="{{item.mail}}">
                <text>邮箱：</text>
                <text style="color: #20263A;">{{item.mail}}</text>
              </view>
            </view>
            <view
              wx:if="{{contactList.length <= 0}}"
              style="width: 100%; height:100px"
            >
              <template is="null"></template>
            </view>
          </view>
        </Wrapper>
        <Wrapper id="contactSociaty" title="公开联系人" marginTop="20rpx">
          <view slot="content" class="wrapper-content">
            <view
              wx:for="{{socialContact}}"
              wx:key="index"
              class="socialContact"
            >
              <view class="phone-box">
                <view class="phone-box-icon">
                  <image
                    src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/cyt_v2/image/business/phone-log.png"
                    mode="aspectFill"
                  />
                </view>
                <view>
                  {{item.contact_data}}
                </view>
              </view>
              <view class="name-box">
                {{item.name}}
              </view>
              <view class="sourse-box">
                {{item.resources}}
              </view>
            </view>
            <view
              wx:if="{{socialContact.length <= 0}}"
              style="width: 100%; height:100px"
            >
              <template is="null"></template>
            </view>
          </view>
        </Wrapper>
      </view>
    </view>
    <view class="footer-box">
      <view
        class="footer-box-item"
        wx:for="{{footerList}}"
        wx:key="index"
        bindtap="openFuncUrl"
        data-item="{{item.url}}"
      >
        <view class="footer-box-item-icon">
          <image src="{{item.icon}}" mode="aspectFill" />
        </view>
        <view class="footer-box-item-lable">
          {{item.name}}
        </view>
      </view>
    </view>
  </scroll-view>

  <half-screen-pop
    zIndex="9999"
    title="{{selectList.label}}"
    position="bottom"
    visible="{{visible}}"
    showFooter="{{false}}"
    confirmBtnText="取消"
    confirmBtnText="保存"
    footHeigh="300rpx"
    bindsubmit="submit"
    bindclose="close"
  >
    <view slot="customContent" class="custom_content">
      <view class="select-item-box">
        <view
          wx:for="{{selectList.list}}"
          wx:key="index"
          wx:for-item="item"
          class="select-item {{selectList.select==index?'active':''}}"
          data-item="{{item}}"
          catchtap="selectValue"
        >
          <text>{{item}}</text>
          <text class="select"></text>
        </view>
      </view>
    </view>
  </half-screen-pop>
  <Dialog
    title="{{dialogTitle}}"
    cancelBtnText="取消"
    top="{{navHeight}}"
    isShowCancel="false"
    bindsubmit="dialogFun"
    visible="{{showDialog}}"
    bindclose="closeDialog"
    bind
    showFooter="{{dialogType !== 'ent'}}"
    showCloseBtn="{{dialogType === 'ent'}}"
    zIndex="9999"
  >
    <view class="dialog_content">
      <view class="tip-title"> 提示 </view>
      <view class="tip-content">
        {{dialogDetail}}
      </view>
    </view>
  </Dialog>
  <!-- 地址弹窗 -->
  <Dialog
    visible="{{showAddress}}"
    title="地址"
    isShowConfirm="{{false}}"
    cancelBtnText="关闭"
    bindclose="onCloseAddress"
  >
    <view class="dialog-con">
      <map
        id="map"
        longitude="{{location.lon}}"
        latitude="{{location.lat}}"
        markers="{{markers}}"
        scale="15"
        style="width: 100%; height: 500rpx;"
      >
      </map>
      <view style="margin: 20rpx 0;">{{locationTxt}}</view>
    </view>
  </Dialog>
  <!-- 项目成员弹窗 -->
  <MemberListPop
    memberList="{{memberList}}"
    visible="{{memberVisible}}"
    title="{{memberTitle}}"
    bindselectMember="selectMember"
  ></MemberListPop>
</view>
