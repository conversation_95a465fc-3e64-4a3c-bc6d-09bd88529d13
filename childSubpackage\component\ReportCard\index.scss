.report-list {
  background: #ffffff;
}

.report-item {
  display: flex;
  background: #ffffff;
  padding: 32rpx 24rpx;
  position: relative;
  transition: background-color 0.2s ease;

  // 1px底部边框
  &::after {
    content: "";
    position: absolute;
    bottom: 0;
    left: 24rpx;
    width: 702rpx;
    height: 1rpx;
    background: #eee;
    transform: scaleY(0.5);
  }

  &:last-child::after {
    display: none;
  }

  // 点击态效果
  &:active {
    background-color: #f8f9fa;
  }
}

.image-container {
  flex-shrink: 0;
  margin-right: 20rpx;
  position: relative;

  .report-image {
    width: 104rpx;
    height: 136rpx;
  }

  .txt {
    position: absolute;
    width: 104rpx;
    height: 28rpx;
    background: linear-gradient(270deg, #fd9331 0%, #ffb93e 100%);
    font-weight: 600;
    font-size: 20rpx;
    color: #ffffff;
    top: 84rpx;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

.report-item .content {
  flex: 1;
  display: flex;
  flex-direction: column;

  .title {
    font-weight: 400;
    font-size: 28rpx;
    color: #20263a;
    margin-bottom: 16rpx;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .info-row {
    display: flex;
    align-items: center;
    margin-bottom: 16rpx;

    .file-info {
      font-weight: 400;
      font-size: 24rpx;
      color: #9b9eac;
      border-radius: 4rpx;
      border: 1rpx solid #a0a5ba;
      padding: 0 10rpx;
      margin-right: 16rpx;
      flex-shrink: 0;
    }

    .tags {
      display: flex;
      gap: 16rpx;

      .tag {
        height: 36rpx;
        padding: 0 10rpx;
        background: rgba(74, 184, 255, 0.1);
        border-radius: 4rpx;
        font-weight: 400;
        font-size: 24rpx;
        color: #4ab8ff;
        max-width: 200rpx;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;

        &:nth-of-type(2) {
          background: rgba(253, 147, 49, 0.1);
          color: #fd9331;
        }
      }
    }
  }

  .bottom-row {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .left {
      display: flex;
      align-items: center;
      .organization {
        font-weight: 400;
        font-size: 24rpx;
        color: #74798c;
        margin-right: 40rpx;
      }

      .date {
        font-weight: 400;
        font-size: 24rpx;
        color: #9b9eac;
      }
    }

    .right {
      width: 36rpx;
      height: 36rpx;
    }
  }
}
