<!-- 这里面的input框能共用 -->
<view class="addUser">
  <view class="zhanwei"></view>
  <form catchsubmit="formSubmit">
    <view class="from-box">
      <h-input
        title="姓名"
        placeholder="请输入联系人姓名（必填）"
        name="contact_name"
        maxlength="{{24}}"
        value="{{form.contact_name}}"
        type="text"
      />
      <h-input
        title="手机"
        placeholder="请输入联系人手机（必填）"
        value="{{form.tel}}"
        name="tel"
        maxlength="{{11}}"
        type="number"
      />
      <h-input
        title="邮箱"
        value="{{form.mail}}"
        name="mail"
        placeholder="请输入联系人邮箱"
        type="email"
      />
      <formitem
        title="公司名称"
        value="{{form.contact_ent_name}}"
        bindclearEntMsg="clearEntMsg"
        bindclick="search"
        bindblur="check"
      ></formitem>
      <!-- <h-input title="公司名称" placeholder="请输入联系人所在公司名称" value="{{form.contact_ent_name}}" name='contact_ent_name' maxlength="{{24}}" /> -->
      <h-input
        title="公司地址"
        placeholder="请输入联系人公司地址"
        value="{{form.contact_ent_address}}"
        name="contact_ent_address"
        maxlength="{{24}}"
      />
      <h-input
        title="联系人职位"
        placeholder="请输入联系人职位"
        value="{{form.position}}"
        name="position"
        maxlength="{{24}}"
      />
    </view>
    <view class="sync" wx:if="{{isEdite}}">
      <view class="sync-label"> 同步该联系人至“智能名片” </view>
      <view>
        <image
          src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/cyt_v2/image/business/btn_on.png"
          class="check-group-r"
          wx:if="{{form.synced}}"
          bindtap="switchChange"
        ></image>
        <image
          src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/cyt_v2/image/business/btn_off.png"
          class="check-group-r"
          wx:if="{{!form.synced}}"
          bindtap="switchChange"
        ></image>
      </view>
    </view>
    <!-- 按钮 -->
    <view class="box-btn">
      <button form-type="submit" class="btm-btn">保存</button>
    </view>
  </form>
</view>
