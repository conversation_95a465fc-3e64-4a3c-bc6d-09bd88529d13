import {
  getNameFromPop,
  handleMultiple
} from '../../../../../../components/hunt/mixin';
import {
  clone
} from '../../../../../../utils/util';
var huntBehavior = require('../../../../../../components/hunt/common/common');
var behavior = require('../../../../../../template/menuhead/index');
var moreFilter = require('../../js/morefilter');

const app = getApp();
Component({
  behaviors: [huntBehavior, behavior, moreFilter],
  properties: {},
  data: {
    // 筛选相关
    regionData: [], //选中的地区
    industryData: [],

    tipVisible: false, // 提示弹窗
    tipContent: '', // 提示内容
    showPicker: false // 时间选择器显示隐藏
  },
  observers: {
    regionData: function (list) {
      // console.log(list)
      if (list.length > 0) {
        this.setData({
          district_val: true
        });
      }
    },
    industryData: function (list) {
      if (list.length > 0) {
        this.setData({
          source_val: true
        });
      }
    }
  },
  methods: {
    // 提示弹窗显示
    showTip({
      currentTarget: {
        dataset: {
          type
        }
      }
    }) {
      const temp = {
        entScaleContent: '根据不同招商场景，对企业规模进行的等级划分，S、A+、A企业适合政府园区招商；B+、B、C+、C企业适合民营园区招商；D、E+、E企业可能存在高成长性企业',
        benefitAssessContent: '基于大数据模型，结合企业实际经营情况数据，建立企业效益评估模型，对企业经营产生的效益进行评估分类'
      };
      this.setData({
        tipVisible: true,
        tipContent: temp[type + 'Content']
      });
    },
    close(e) {
      const {
        minCapital,
        maxCapital,
        regTime,
        regTime: {
          startTime,
          endTime
        }
      } = this.data;
      // 点击取消的时候将修改之前的数据重新赋值给当前展示用的变量
      this.setData({
        viewMinCapital: minCapital, // 最低资本(页面展示用: 根据点击取消时minCapital的状态进行数据更新)
        viewMaxCapital: maxCapital, // 最高资本(页面展示用: 根据点击取消时maxCapital的状态进行数据更新)
        viewStartTime: startTime, // 注册年限开始时间(页面展示用: 根据点击取消时regTime.startTime的状态进行数据更新)
        viewEndTime: endTime, // 注册年限结束时间(页面展示用: 根据点击取消时regTime.endTime的状态进行数据更新)
        'regTime.list': regTime.prevList
      });
      this.closeHyFilter();
    },
    setVipVisible(detail) {
      this.triggerEvent('setVipVisible', detail);
    },
    // 更多筛选
    handleMoreFilter({
      detail
    }) {
      let {
        filter_val,
        viewMinCapital,
        viewMaxCapital,
        viewStartTime,
        viewEndTime
      } = this.data;
      if (
        viewMinCapital &&
        viewMaxCapital &&
        Number(viewMinCapital) > Number(viewMaxCapital)
      ) {
        app.showToast('最低注册资本不能大于最高注册资本');
        return;
      }
      detail.map(item => {
        let data =
          (item?.codeList && JSON.parse(JSON.stringify(item.codeList))) || [];
        this.setData({
          [item.id]: {
            ...this.data[item.id],
            list: JSON.parse(JSON.stringify(item.allList)),
            cur: data
          },
          [`${item.id}.prevList`]: this.data[item.id].canInput ?
            JSON.parse(JSON.stringify(item.allList)) : []
        });
      });
      filter_val =
        detail.some(item => item.codeList.length > 0) ||
        viewMinCapital ||
        viewMaxCapital ||
        viewStartTime ||
        viewEndTime;
      this.setData({
          filter_val,
          minCapital: viewMinCapital,
          maxCapital: viewMaxCapital,
          'regTime.startTime': viewStartTime,
          'regTime.endTime': viewEndTime
        },
        () => this.backAll()
      );
    },
    // 打开日期弹窗
    showDatePicker({
      currentTarget: {
        dataset: {
          type
        }
      }
    }) {
      const {
        data
      } = this;
      const timeType = type.split('-')[1];
      this.setData({
        showPicker: true,
        backfillDate: data[timeType] || '',
        dateType: type,
        title: timeType == 'viewStartTime' ? '开始时间' : '结束时间'
      });
    },
    //获取时间
    setDate({
      detail: {
        date,
        dateType
      }
    }) {
      if (date && !date.includes(undefined)) {
        const {
          data,
          data: {
            viewStartTime = 0,
            viewEndTime = 0
          }
        } = this;
        const temp = dateType.split('-');

        const start = new Date(
          temp[1] === 'viewEndTime' ? viewStartTime : date
        ).getTime();
        const end = new Date(
          temp[1] === 'viewEndTime' ? date : viewEndTime
        ).getTime();
        if (start > end) {
          app.showToast('开始时间不能大于结束时间');
          return;
        }

        const list = data[temp[0]].list.map(item => {
          item.status = '';
          return item;
        });
        this.setData({
          [temp[1]]: date,
          [`${temp[0]}.list`]: list
        });
      }
    },
    // 选中日期清除输入框的日期
    handleregTime() {
      this.setData({
        viewStartTime: '',
        viewEndTime: ''
      });
    },
    // 地区
    getRegion(e) {
      let data = JSON.parse(JSON.stringify(e.detail)); //解決checked狀態丟失的情況--拷貝--切記
      // console.log('获取的地区数据', data)
      let {
        regionData,
        district_val
      } = this.data;

      if (data[0]?.code == 'all') {
        //说明是全国的 就清除选中的
        regionData = [];
      } else {
        regionData = data;
      }
      district_val = regionData.length > 0;
      this.setData({
          regionData,
          district_val
        },
        () => this.backAll()
      );
    },
    // 全部行业
    getIndustry(e) {
      const data = JSON.parse(JSON.stringify(e.detail)); //解決checked狀態丟失的情況--拷貝--切記
      let {
        industryData,
        source_val
      } = this.data;
      if (data[0]?.code == 'all') {
        //说明是全国的 就清除选中的
        industryData = [];
      } else {
        industryData = data;
      }
      source_val = industryData.length > 0;
      this.setData({
          industryData,
          source_val
        },
        () => this.backAll()
      );
    },
    closeRegion(e) {
      this.closeHyFilter();
    },

    // 获取所需数据格式
    formatData(data, delimiter) {
      let temp = null;
      if (!delimiter) {
        let arr = [],
          name = '';
        if (data.length) {
          arr = handleMultiple(data)
            .filter(item => item.status === 'checked')
            .map(item => item.code);
          const str = getNameFromPop(data, {
            slice: true
          });
          name = str.length >= 4 ? str.slice(0, 4) + '...' : str;
        }
        temp = {
          arr,
          name
        };
      } else {
        // 有分隔符的数据根据分隔符特殊处理
        temp = [];
        for (const item of data) {
          const tempArr = item.split(delimiter);
          temp.push({
            start: tempArr[0],
            end: tempArr[1]
          });
        }
      }
      return temp;
    },
    handleInput({
      currentTarget: {
        dataset: {
          type
        }
      },
      detail: {
        value
      }
    }) {
      // const { viewMinCapital, viewMaxCapital=123 } = this.data;
      // console.log('value', value);
      // console.log('viewMinCapital', viewMinCapital);
    },
    // 总的返回
    backAll() {
      let {
        regionData,
        industryData,
        minCapital,
        maxCapital,
        regTime,
        entScale,
        benefitAssess
      } = this.data;
      const {
        startTime,
        endTime
      } = regTime;
      if (startTime || endTime) regTime.cur = [startTime + '$' + endTime];
      const requestData = {
        name1: this.formatData(regionData).name || '全国',
        name2: this.formatData(industryData).name || '全部行业',
        trade_types: this.formatData(industryData).arr, //行业
        areas: this.formatData(regionData).arr, //地区,
        register_capital: [{
          start: minCapital,
          end: maxCapital
        }], // 注册资本
        register_time: this.formatData(regTime.cur, '$'), // 注册年限
        ent_scale: this.formatData(entScale.cur, '$'), // 企业规模 
        benefit_assess: this.formatData(benefitAssess.cur, '$'), // 效益评估 
      };
      this.triggerEvent('submit', requestData);
      this.closeHyFilter();
    }
  }
});