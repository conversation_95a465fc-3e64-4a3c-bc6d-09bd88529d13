const app = getApp();
import CryptoJS from '../../../utils/crypto-js-mini';
import {user} from '../../../service/api';
Page({
  /**
   * 页面的初始数据
   */
  data: {
    phone: '',
    code: '',
    password_new: '',
    againPwd: '',
    time: 0, //倒计时
    timer: null,
    code_id: ''
  },
  onLoad(options) {
    let userData = wx.getStorageSync('userData');
    let {
      member_data: {phone}
    } = userData;
    this.setData({phone});
  },
  changePhone(e) {
    this.setData({phone: e.detail.value});
  },
  getCode() {
    let phoneReg = /^[1][3,4,5,6,7,8,9][0-9]{9}$/;
    if (!phoneReg.test(this.data.phone)) {
      app.showToast(`请输入正确的手机号码!`, 'none', 1000);
      return;
    } else {
      let {time, timer} = this.data;
      let num = 60;
      this.setData({
        time: num
      });
      this.getVerifyCode(this.data.phone);
      timer = setInterval(() => {
        num -= 1;
        this.setData({
          time: num
        });
        if (num <= 0) clearInterval(timer);
      }, 1000);
      this.setData({
        timer
      });
    }
  },
  // 获取验证码
  getVerifyCode(receiver) {
    user
      .getCode({
        receiver
      })
      .then(res => {
        this.setData({
          code_id: res.code_id
        });
      });
  },

  formSubmit(e) {
    let form = e.detail.value;
    //验证手机号
    let phoneReg = /^[1][3,4,5,6,7,8,9][0-9]{9}$/;
    let checkPwd =
      /^(?![0-9]+$)(?![a-zA-Z]+$)[0-9a-zA-Z-`=\[\];',.~!@#$%^&*()_+|{}:"?]{8,16}$/;
    if (!phoneReg.test(form.phone)) {
      app.showToast(`请输入正确的手机号码!`, 'none', 1000);
      return;
    }
    if (!this.data.code_id) {
      app.showToast(`请获取验证码`, 'none', 1000);
      return;
    }
    if (!checkPwd.test(form.password_new)) {
      app.showToast(`密码格式错误，请重新输入`, 'none', 1000);
      return;
    }
    if (form.password_new !== form.againPwd) {
      app.showToast(`输入的两次密码不一致，请检查`, 'none', 1000);
      return;
    }
    delete form.againPwd;
    form.password_new = CryptoJS.enc.Base64.stringify(
      CryptoJS.enc.Utf8.parse(form.password_new)
    );
    form.code_id = this.data.code_id;
    user.updatePassword(form).then(res => {
      if (res.id) {
        wx.clearStorageSync();
        // app.route(this, '/pages/login/login')
        wx.reLaunch({url: '/pages/login/login'});
        this.setData({}, () => app.isLogin());
      }
    });
  }
});
