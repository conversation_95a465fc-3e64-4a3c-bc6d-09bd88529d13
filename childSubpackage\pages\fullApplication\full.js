import {
  allList,
  allListAry,
  homePageServiceList,
  othermapping
} from '../../../utils/fullappliction';
import {debounce} from '../../../utils/formate';
import {home} from '../../../service/api';

const app = getApp();
const throttleDrag = (callback, delay) => {
  let timerId;
  return function (event) {
    clearTimeout(timerId);
    timerId = setTimeout(() => {
      callback.call(this, event);
    }, delay);
  };
};

Page({
  data: {
    hidden: true,
    flag: false,
    x: 0,
    y: 0,
    homePageServiceList: [],
    recentUseList: [], //最近使用5个
    hisList: [],
    disabled: true,
    elements: [],
    allList: JSON.parse(JSON.stringify(allList)),
    overflow: true,
    curItem: {},
    // input
    inputShowed: false, //是否聚焦
    ent_name: '', //搜索值
    ents_name: ''
  },
  onLoad: function (options) {
    this.fullApply();
    this.gethisList();
    this.getPositon();
  },
  scroll: throttleDrag(function () {
    // 页面滚动时执行
    this.getPositon();
    // console.log(111)
  }, 1000),
  // 最近使用
  async gethisList() {
    const res = (await home.applyhisList()) || [];
    if (res?.length) {
      res.forEach(i => {
        i.img = `https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/${i.code}.png`;
      });
    }
    this.setData(
      {
        recentUseList: res.length > 5 ? res.slice(0, 5) : res
      },
      () => this.getPositon()
    );
  },
  // 首页服务数据，
  async fullApply() {
    const FULL_APPLY_STORAGE_KEY = 'fullApply';
    let arrStr = wx.getStorageSync(FULL_APPLY_STORAGE_KEY);
    if (arrStr) {
      arrStr = JSON.parse(arrStr);
    }
    if (!Array.isArray(arrStr) || arrStr?.length === 0) {
      const res = await home.applyList();
      if (Array.isArray(res) && res?.length > 0) {
        arrStr = res;
      }
    }
    if (!Array.isArray(arrStr) || arrStr?.length === 0) {
      arrStr = homePageServiceList;
    }
    wx.setStorageSync(FULL_APPLY_STORAGE_KEY, JSON.stringify(arrStr));
    this.setData({
      homePageServiceList: arrStr
    });
  },
  // 得到item位置
  getPositon() {
    var query = wx.createSelectorQuery();
    var item = query
      .in(this)
      .select('.itemss')
      .boundingClientRect()
      .exec(res => {
        if (res[0].height) {
          this.setData({
            itemHW: {
              width: res[0].width,
              height: res[0].height
            }
          });
        }
      });
    // console.log(item)
    var nodesRef = query.selectAll('.itemss');
    nodesRef
      .fields(
        {
          dataset: true,
          rect: true
        },
        result => {
          this.setData({
            elements: result
          });
        }
      )
      .exec();
  },
  //长按
  longtap: function (e) {
    const detail = e.detail;
    let {item} = e.currentTarget.dataset;
    let x = e.currentTarget.offsetLeft,
      y = e.currentTarget.offsetTop;
    this.setData(
      {
        overflow: false,
        flag: true
      },
      () => {
        this.setData({
          x,
          y,
          hidden: false,
          curItem: item
        });
      }
    );
  },
  //触摸开始
  touchs: function (e) {
    this.setData({
      beginIndex: e.currentTarget.dataset.index
    });
  },
  //触摸结束
  touchend: function (e) {
    this.setData({
      overflow: true
    });
    if (!this.data.flag) {
      return;
    }
    const x = e.changedTouches[0].pageX;
    const y = e.changedTouches[0].pageY;
    const list = this.data.elements;
    let data = this.data.homePageServiceList;
    for (var j = 0; j < list.length; j++) {
      const item = list[j];
      if (x > item.left && x < item.right && y > item.top && y < item.bottom) {
        const endIndex = item.dataset.index;
        const beginIndex = this.data.beginIndex;
        //向后移动
        if (beginIndex < endIndex) {
          let tem = data[beginIndex];
          for (let i = beginIndex; i < endIndex; i++) {
            data[i] = data[i + 1];
          }
          data[endIndex] = tem;
        }
        //向前移动
        if (beginIndex > endIndex) {
          let tem = data[beginIndex];
          for (let i = beginIndex; i > endIndex; i--) {
            data[i] = data[i - 1];
          }
          data[endIndex] = tem;
        }

        this.setData(
          {
            homePageServiceList: data
          },
          async () => {
            //通知首页（用了show所以不需要特别操作）， 发请求给后端，位置变换了通知首页改变顺序
            wx.setStorageSync('fullApply', JSON.stringify(data));
            await home.applyAdd(data);
          }
        );
      }
    }
    this.setData({
      hidden: true,
      flag: false
    });
  },
  //滑动--就是计算滑块
  touchm: function (e) {
    let index = e.currentTarget.dataset.index;
    const curItem = this.data.elements[index];
    // console.log('y', e.touches[0].pageY, curItem['top'], this.data.itemHW.height / 2)
    if (this.data.flag) {
      const x = e.touches[0].pageX - this.data.itemHW.width / 2;
      const y =
        index > 4
          ? e.touches[0].pageY - curItem['top'] + this.data.itemHW.height / 2
          : e.touches[0].pageY - curItem['top'] - this.data.itemHW.height / 2; //这里后面概要调一下
      this.setData({
        x,
        y
      });
    }
  },
  //
  onBlur() {
    // 拿到ent_name --传入最近搜索历史
    const ent_nameue = this.data.ent_name;
    if (ent_nameue.trim().length > 0) {
      // console.log(222)
      //
    } else {
      this.setData({
        inputShowed: false
      });
    }
  },
  onInput: debounce(function ([...e]) {
    let keyword = e[0].detail.value;
    // console.log(e[0], keyword)
    if (keyword || keyword == '') {
      this.setData(
        {
          ent_name: keyword
        },
        () => {
          console.log('keyword', keyword);
          this.inputQuest(keyword);
        }
      );
    }
  }, 200),
  onClear() {
    this.unLocked();
    this.setData({
      ent_name: '',
      ents_name: ''
    });
    // this.inputQuest('suspend')
  },
  unLocked() {
    wx.hideKeyboard();
    this.setData({
      loading: false,
      inputShowed: false
    });
  },
  inputQuest(keyword) {
    let hisList = [];
    if (!keyword || !keyword.trim()) {
      this.setData({
        hisList
      });
      return;
    }
    // 遍历key
    allListAry.forEach(item => {
      item.heightKey = item.title.split('');
      item.isFlter = false;
      for (let i = 0; i < keyword.length; i++) {
        let codePoint = keyword[i];
        if (codePoint == '') return;
        // 遍历数组
        let idx = item.title.indexOf(codePoint);
        item.heightKey = item.heightKey.map(is => {
          if (typeof is == 'string') {
            // 说明是第一次
            return {
              text: is,
              isHight: codePoint == is
            };
          }
          is['isHight'] = is['isHight'] ? true : is.text == codePoint;
          return is;
        });
        item.isFlter = item.isFlter ? true : idx != -1;
      }
    });
    hisList = allListAry.filter(i => i.isFlter);
    // console.log(hisList)
    this.setData({
      hisList
    });
  },
  handleRecentUseList(originalArray, prop) {
    //去重
    if (!originalArray.length) return;
    const newArray = [];
    const lookupObject = {};
    for (let i in originalArray) {
      lookupObject[originalArray[i][prop]] = originalArray[i];
    }
    for (let i in lookupObject) {
      newArray.push(lookupObject[i]);
    }
    console.log('gg', newArray);
    this.setData({
      recentUseList: newArray.length > 5 ? newArray.slice(0, 5) : newArray
    });
    // return newArray;
  },
  // 点击单个itm
  async clickItm(e) {
    let {item, type} = e.currentTarget.dataset;
    // console.log('item', item)
    // console.log('11', item, othermapping)
    if (!type) {
      // 通知最近使用接口-发请求
      try {
        await home.applyhisAddList(item);
      } catch (err) {
        app.showToast('err', 'none', 1500, true);
      }
    }
    setTimeout(
      () =>
        this.handleRecentUseList([item, ...this.data.recentUseList], 'code'),
      1000
    );
    if (Object.keys(othermapping).includes(item.title)) {
      console.log(othermapping[item.title]);
      if (['/pages/hIndusty/index'].includes(othermapping[item.title])) {
        app.route(this, othermapping[item.title], 'switchTab');
      } else {
        app.route(this, othermapping[item.title]);
      }
      return;
    }
    // 没有url就提升用户去下载app
    app.showToast('查看更多,请下载App!', 'none', 1500, false);
  }
});

// ps:最近使用根据后端来掉接口
